{"name": "ModrkClient", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint ."}, "dependencies": {"@miblanchard/react-native-slider": "^2.1.0", "@react-native-async-storage/async-storage": "^1.19.3", "@react-native-community/clipboard": "^1.5.1", "@react-native-community/datetimepicker": "^7.6.2", "@react-native-community/netinfo": "^9.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-firebase/app": "^18.3.0", "@react-native-firebase/messaging": "^18.3.0", "@react-native-masked-view/masked-view": "^0.3.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "add": "^2.0.6", "babel-plugin-transform-remove-console": "^6.9.4", "crypto-js": "^4.1.1", "i": "^0.3.7", "jwt-decode": "^3.1.2", "link": "^1.5.1", "moment": "^2.29.4", "native-base": "^2.13.14", "npm": "^8.18.0", "react": "18.3.1", "react-native": "^0.72.12", "react-native-appsflyer": "^6.14.2-rc1", "react-native-audio-recorder-player": "^3.5.1", "react-native-background-timer": "^2.4.1", "react-native-camera": "^4.2.1", "react-native-confirmation-code-input": "^1.0.4", "react-native-copilot": "^2.5.1", "react-native-device-info": "^10.9.0", "react-native-document-picker": "^9.1.1", "react-native-fast-image": "^8.6.3", "react-native-geocoding": "^0.5.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.13.1", "react-native-google-places-autocomplete": "^2.5.6", "react-native-i18n": "^2.0.15", "react-native-image-picker": "^5.6.0", "react-native-image-resizer": "^1.4.5", "react-native-image-viewing": "^0.2.2", "react-native-image-zoom-viewer": "^3.0.1", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.8.3", "react-native-maps": "1.7.1", "react-native-modalize": "^2.1.1", "react-native-pager-view": "^6.2.0", "react-native-permissions": "^3.9.3", "react-native-popover-view": "^5.1.8", "react-native-push-notification": "^8.1.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-raw-bottom-sheet": "^2.2.0", "react-native-render-html": "^6.3.4", "react-native-restart": "^0.0.27", "react-native-safe-area-context": "^4.7.2", "react-native-screens": "^3.22.1", "react-native-select-dropdown": "^3.4.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-slider": "^0.11.0", "react-native-snap-carousel": "^3.9.1", "react-native-sound": "^0.11.2", "react-native-star-rating": "^1.1.0", "react-native-svg": "^13.14.0", "react-native-swipe-list-view": "^3.2.9", "react-native-vlc-media-player": "^1.0.70", "react-native-webview": "^13.2.2", "react-redux": "^8.1.2", "redux": "^4.2.1", "redux-saga": "^1.2.3", "redux-thunk": "^2.4.2", "rn-fetch-blob": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.3.1"}, "jest": {"preset": "react-native"}}
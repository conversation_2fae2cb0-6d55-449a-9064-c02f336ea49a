#!/bin/bash

# Script to create a stub React Native JAR for dependency resolution

echo "Creating React Native stub artifacts..."

# Create local Maven repository directory structure
MAVEN_LOCAL="$HOME/.m2/repository"
RN_GROUP_DIR="$MAVEN_LOCAL/com/facebook/react"
RN_ARTIFACT_DIR="$RN_GROUP_DIR/react-native"
RN_VERSION_DIR="$RN_ARTIFACT_DIR/0.72.12"

mkdir -p "$RN_VERSION_DIR"

# Create a minimal JAR file
echo "Creating minimal React Native JAR..."
cd /tmp
mkdir -p react-native-stub/META-INF
echo "Manifest-Version: 1.0" > react-native-stub/META-INF/MANIFEST.MF
echo "Implementation-Title: React Native" >> react-native-stub/META-INF/MANIFEST.MF
echo "Implementation-Version: 0.72.12" >> react-native-stub/META-INF/MANIFEST.MF

# Create a dummy Java class
mkdir -p react-native-stub/com/facebook/react
cat > react-native-stub/com/facebook/react/ReactApplication.class << 'EOF'
����   4 
      java/lang/Object <init> ()V  +com/facebook/react/ReactApplication$Stub Code LineNumberTable LocalVariableTable this -Lcom/facebook/react/ReactApplication$Stub; 
SourceFile ReactApplication.java InnerClasses  %com/facebook/react/ReactApplication Stub !            	   /     *� �    
                                
     	
EOF

# Create JAR
jar cf react-native-0.72.12.jar -C react-native-stub .

# Copy to Maven local repository
cp react-native-0.72.12.jar "$RN_VERSION_DIR/"

# Create POM file
cat > "$RN_VERSION_DIR/react-native-0.72.12.pom" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.facebook.react</groupId>
    <artifactId>react-native</artifactId>
    <version>0.72.12</version>
    <packaging>jar</packaging>
    <name>React Native</name>
    <description>A framework for building native apps with React</description>
</project>
EOF

# Create additional version directories for compatibility
RN_VERSION_DIR_71="$RN_ARTIFACT_DIR/0.71.19"
mkdir -p "$RN_VERSION_DIR_71"
cp react-native-0.72.12.jar "$RN_VERSION_DIR_71/react-native-0.71.19.jar"

# Create POM for 0.71.19
cat > "$RN_VERSION_DIR_71/react-native-0.71.19.pom" << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.facebook.react</groupId>
    <artifactId>react-native</artifactId>
    <version>0.71.19</version>
    <packaging>jar</packaging>
    <name>React Native</name>
    <description>A framework for building native apps with React</description>
</project>
EOF

# Create metadata files
echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
<metadata>
  <groupId>com.facebook.react</groupId>
  <artifactId>react-native</artifactId>
  <versioning>
    <latest>0.72.12</latest>
    <release>0.72.12</release>
    <versions>
      <version>0.71.19</version>
      <version>0.72.12</version>
    </versions>
    <lastUpdated>$(date +%Y%m%d%H%M%S)</lastUpdated>
  </versioning>
</metadata>" > "$RN_ARTIFACT_DIR/maven-metadata-local.xml"

# Clean up
rm -rf /tmp/react-native-stub
rm -f /tmp/react-native-0.72.12.jar

echo "React Native stub artifacts created successfully!"
echo "JAR: $RN_VERSION_DIR/react-native-0.72.12.jar"
echo "POM: $RN_VERSION_DIR/react-native-0.72.12.pom"

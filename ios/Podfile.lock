PODS:
  - Apps<PERSON>lyerFramework (6.14.2):
    - AppsFlyerFramework/Main (= 6.14.2)
  - AppsFlyerFramework/Main (6.14.2)
  - boost (1.76.0)
  - BVLinearGradient (2.8.3):
    - React-Core
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.72.12)
  - FBReactNativeSpec (0.72.12):
    - RCT-<PERSON>olly (= 2021.07.22.00)
    - RCTRequired (= 0.72.12)
    - RCTTypeSafety (= 0.72.12)
    - React-Core (= 0.72.12)
    - React-jsi (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - Firebase (10.20.0):
    - Firebase/Core (= 10.20.0)
  - Firebase/Core (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.20.0)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Messaging (10.20.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.20.0)
  - FirebaseAnalytics (10.20.0):
    - FirebaseAnalytics/AdIdSupport (= 10.20.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - Flipper (0.201.0):
    - Flipper-Folly (~> 2.6)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (*******)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - FlipperKit (0.201.0):
    - FlipperKit/Core (= 0.201.0)
  - FlipperKit/Core (0.201.0):
    - Flipper (~> 0.201.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.201.0):
    - Flipper (~> 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.201.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.201.0)
  - FlipperKit/FKPortForwarding (0.201.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.201.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
  - FlipperKit/FlipperKitLayoutPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutTextSearchable (0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.201.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.201.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Maps-iOS-Utils (4.1.0):
    - Google-Maps-iOS-Utils/Clustering (= 4.1.0)
    - Google-Maps-iOS-Utils/Geometry (= 4.1.0)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.1.0)
    - Google-Maps-iOS-Utils/Heatmap (= 4.1.0)
    - Google-Maps-iOS-Utils/QuadTree (= 4.1.0)
    - GoogleMaps
  - Google-Maps-iOS-Utils/Clustering (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/Geometry (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/GeometryUtils (4.1.0):
    - GoogleMaps
  - Google-Maps-iOS-Utils/Heatmap (4.1.0):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps
  - Google-Maps-iOS-Utils/QuadTree (4.1.0):
    - GoogleMaps
  - GoogleAppMeasurement (10.20.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.20.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.20.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMaps (6.2.1):
    - GoogleMaps/Maps (= 6.2.1)
  - GoogleMaps/Base (6.2.1)
  - GoogleMaps/Maps (6.2.1):
    - GoogleMaps/Base
  - GoogleUtilities (7.13.3):
    - GoogleUtilities/AppDelegateSwizzler (= 7.13.3)
    - GoogleUtilities/Environment (= 7.13.3)
    - GoogleUtilities/ISASwizzler (= 7.13.3)
    - GoogleUtilities/Logger (= 7.13.3)
    - GoogleUtilities/MethodSwizzler (= 7.13.3)
    - GoogleUtilities/Network (= 7.13.3)
    - "GoogleUtilities/NSData+zlib (= 7.13.3)"
    - GoogleUtilities/Privacy (= 7.13.3)
    - GoogleUtilities/Reachability (= 7.13.3)
    - GoogleUtilities/SwizzlerTestHelpers (= 7.13.3)
    - GoogleUtilities/UserDefaults (= 7.13.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/ISASwizzler (7.13.3):
    - GoogleUtilities/Privacy
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/SwizzlerTestHelpers (7.13.3):
    - GoogleUtilities/MethodSwizzler
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - hermes-engine (0.72.12):
    - hermes-engine/Pre-built (= 0.72.12)
  - hermes-engine/Pre-built (0.72.12)
  - libevent (2.1.12)
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - MobileVLCKit (3.5.1)
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - OpenSSL-Universal (1.1.1100)
  - Permission-Camera (3.10.1):
    - RNPermissions
  - Permission-LocationAlways (3.10.1):
    - RNPermissions
  - Permission-LocationWhenInUse (3.10.1):
    - RNPermissions
  - Permission-Notifications (3.10.1):
    - RNPermissions
  - PromisesObjC (2.4.0)
  - RCT-Folly (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.07.22.00)
  - RCT-Folly/Default (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCT-Folly/Futures (2021.07.22.00):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - libevent
  - RCTRequired (0.72.12)
  - RCTTypeSafety (0.72.12):
    - FBLazyVector (= 0.72.12)
    - RCTRequired (= 0.72.12)
    - React-Core (= 0.72.12)
  - React (0.72.12):
    - React-Core (= 0.72.12)
    - React-Core/DevSupport (= 0.72.12)
    - React-Core/RCTWebSocket (= 0.72.12)
    - React-RCTActionSheet (= 0.72.12)
    - React-RCTAnimation (= 0.72.12)
    - React-RCTBlob (= 0.72.12)
    - React-RCTImage (= 0.72.12)
    - React-RCTLinking (= 0.72.12)
    - React-RCTNetwork (= 0.72.12)
    - React-RCTSettings (= 0.72.12)
    - React-RCTText (= 0.72.12)
    - React-RCTVibration (= 0.72.12)
  - React-callinvoker (0.72.12)
  - React-Codegen (0.72.12):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.12)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/Default (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/DevSupport (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.12)
    - React-Core/RCTWebSocket (= 0.72.12)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.72.12)
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-Core/RCTWebSocket (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Core/Default (= 0.72.12)
    - React-cxxreact
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimeexecutor
    - React-utils
    - SocketRocket (= 0.6.1)
    - Yoga
  - React-CoreModules (0.72.12):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.12)
    - React-Codegen (= 0.72.12)
    - React-Core/CoreModulesHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-RCTBlob
    - React-RCTImage (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
    - SocketRocket (= 0.6.1)
  - React-cxxreact (0.72.12):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.12)
    - React-debug (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-jsinspector (= 0.72.12)
    - React-logger (= 0.72.12)
    - React-perflogger (= 0.72.12)
    - React-runtimeexecutor (= 0.72.12)
  - React-debug (0.72.12)
  - React-hermes (0.72.12):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - RCT-Folly/Futures (= 2021.07.22.00)
    - React-cxxreact (= 0.72.12)
    - React-jsi
    - React-jsiexecutor (= 0.72.12)
    - React-jsinspector (= 0.72.12)
    - React-perflogger (= 0.72.12)
  - React-jsi (0.72.12):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
  - React-jsiexecutor (0.72.12):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-cxxreact (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-perflogger (= 0.72.12)
  - React-jsinspector (0.72.12)
  - React-logger (0.72.12):
    - glog
  - react-native-appsflyer (6.14.2-rc1):
    - AppsFlyerFramework (= 6.14.2)
    - React
  - react-native-background-timer (2.4.1):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-document-picker (9.3.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-geolocation-service (5.3.1):
    - React
  - react-native-image-picker (5.7.0):
    - React-Core
  - react-native-image-resizer (1.4.5):
    - React-Core
  - react-native-maps (1.7.1):
    - React-Core
  - react-native-netinfo (9.5.0):
    - React-Core
  - react-native-pager-view (6.8.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - react-native-render-html (6.3.4):
    - React-Core
  - react-native-restart (0.0.27):
    - React-Core
  - react-native-safe-area-context (4.14.1):
    - React-Core
  - react-native-video (5.2.1):
    - React-Core
    - react-native-video/Video (= 5.2.1)
  - react-native-video/Video (5.2.1):
    - React-Core
  - react-native-vlc-media-player (1.0.38):
    - MobileVLCKit (= 3.5.1)
    - React
    - TVVLCKit (= 3.5.1)
  - react-native-webview (13.15.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - React-NativeModulesApple (0.72.12):
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.72.12)
  - React-RCTActionSheet (0.72.12):
    - React-Core/RCTActionSheetHeaders (= 0.72.12)
  - React-RCTAnimation (0.72.12):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.12)
    - React-Codegen (= 0.72.12)
    - React-Core/RCTAnimationHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-RCTAppDelegate (0.72.12):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-hermes
    - React-NativeModulesApple
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
  - React-RCTBlob (0.72.12):
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.12)
    - React-Core/RCTBlobHeaders (= 0.72.12)
    - React-Core/RCTWebSocket (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-RCTNetwork (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-RCTImage (0.72.12):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.12)
    - React-Codegen (= 0.72.12)
    - React-Core/RCTImageHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-RCTNetwork (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-RCTLinking (0.72.12):
    - React-Codegen (= 0.72.12)
    - React-Core/RCTLinkingHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-RCTNetwork (0.72.12):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.12)
    - React-Codegen (= 0.72.12)
    - React-Core/RCTNetworkHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-RCTSettings (0.72.12):
    - RCT-Folly (= 2021.07.22.00)
    - RCTTypeSafety (= 0.72.12)
    - React-Codegen (= 0.72.12)
    - React-Core/RCTSettingsHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-RCTText (0.72.12):
    - React-Core/RCTTextHeaders (= 0.72.12)
  - React-RCTVibration (0.72.12):
    - RCT-Folly (= 2021.07.22.00)
    - React-Codegen (= 0.72.12)
    - React-Core/RCTVibrationHeaders (= 0.72.12)
    - React-jsi (= 0.72.12)
    - ReactCommon/turbomodule/core (= 0.72.12)
  - React-rncore (0.72.12)
  - React-runtimeexecutor (0.72.12):
    - React-jsi (= 0.72.12)
  - React-runtimescheduler (0.72.12):
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-runtimeexecutor
  - React-utils (0.72.12):
    - glog
    - RCT-Folly (= 2021.07.22.00)
    - React-debug
  - ReactCommon/turbomodule/bridging (0.72.12):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.12)
    - React-cxxreact (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-logger (= 0.72.12)
    - React-perflogger (= 0.72.12)
  - ReactCommon/turbomodule/core (0.72.12):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2021.07.22.00)
    - React-callinvoker (= 0.72.12)
    - React-cxxreact (= 0.72.12)
    - React-jsi (= 0.72.12)
    - React-logger (= 0.72.12)
    - React-perflogger (= 0.72.12)
  - rn-fetch-blob (0.12.0):
    - React-Core
  - RNAudioRecorderPlayer (3.5.1):
    - React-Core
  - RNCAsyncStorage (1.24.0):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCMaskedView (0.3.2):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNCPushNotificationIOS (1.11.0):
    - React-Core
  - RNDateTimePicker (7.7.0):
    - React-Core
  - RNDeviceInfo (10.14.0):
    - React-Core
  - RNFastImage (8.6.3):
    - React-Core
    - SDWebImage (~> 5.11.1)
    - SDWebImageWebPCoder (~> 0.8.4)
  - RNFBApp (18.9.0):
    - Firebase/CoreOnly (= 10.20.0)
    - React-Core
  - RNFBMessaging (18.9.0):
    - Firebase/Messaging (= 10.20.0)
    - FirebaseCoreExtension (= 10.20.0)
    - React-Core
    - RNFBApp
  - RNGestureHandler (2.27.1):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
  - RNI18n (2.0.15):
    - React
  - RNPermissions (3.10.1):
    - React-Core
  - RNScreens (3.37.0):
    - RCT-Folly (= 2021.07.22.00)
    - React-Core
    - React-RCTImage
  - RNSound (0.11.2):
    - React-Core
    - RNSound/Core (= 0.11.2)
  - RNSound/Core (0.11.2):
    - React-Core
  - RNSVG (13.14.1):
    - React-Core
  - SDWebImage (5.11.1):
    - SDWebImage/Core (= 5.11.1)
  - SDWebImage/Core (5.11.1)
  - SDWebImageWebPCoder (0.8.5):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - SocketRocket (0.6.1)
  - Yoga (1.14.0)

DEPENDENCIES:
  - AppsFlyerFramework
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - BVLinearGradient (from `../node_modules/react-native-linear-gradient`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Firebase
  - FirebaseCore
  - FirebaseCoreInternal
  - Flipper (= 0.201.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= *******)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - FlipperKit (= 0.201.0)
  - FlipperKit/Core (= 0.201.0)
  - FlipperKit/CppBridge (= 0.201.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.201.0)
  - FlipperKit/FBDefines (= 0.201.0)
  - FlipperKit/FKPortForwarding (= 0.201.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.201.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.201.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.201.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.201.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.201.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.201.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.201.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Maps-iOS-Utils
  - GoogleMaps
  - GoogleUtilities
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - libevent (~> 2.1.12)
  - MobileVLCKit
  - OpenSSL-Universal (= 1.1.1100)
  - Permission-Camera (from `../node_modules/react-native-permissions/ios/Camera/Permission-Camera.podspec`)
  - Permission-LocationAlways (from `../node_modules/react-native-permissions/ios/LocationAlways/Permission-LocationAlways.podspec`)
  - Permission-LocationWhenInUse (from `../node_modules/react-native-permissions/ios/LocationWhenInUse/Permission-LocationWhenInUse.podspec`)
  - Permission-Notifications (from `../node_modules/react-native-permissions/ios/Notifications/Permission-Notifications.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-appsflyer (from `../node_modules/react-native-appsflyer`)
  - react-native-background-timer (from `../node_modules/react-native-background-timer`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - react-native-document-picker (from `../node_modules/react-native-document-picker`)
  - react-native-geolocation-service (from `../node_modules/react-native-geolocation-service`)
  - react-native-image-picker (from `../node_modules/react-native-image-picker`)
  - react-native-image-resizer (from `../node_modules/react-native-image-resizer`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - "react-native-netinfo (from `../node_modules/@react-native-community/netinfo`)"
  - react-native-pager-view (from `../node_modules/react-native-pager-view`)
  - react-native-render-html (from `../node_modules/react-native-render-html`)
  - react-native-restart (from `../node_modules/react-native-restart`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-video (from `../node_modules/react-native-video`)
  - react-native-vlc-media-player (from `../node_modules/react-native-vlc-media-player`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - rn-fetch-blob (from `../node_modules/rn-fetch-blob`)
  - RNAudioRecorderPlayer (from `../node_modules/react-native-audio-recorder-player`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCMaskedView (from `../node_modules/@react-native-masked-view/masked-view`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFastImage (from `../node_modules/react-native-fast-image`)
  - "RNFBApp (from `../node_modules/@react-native-firebase/app`)"
  - "RNFBMessaging (from `../node_modules/@react-native-firebase/messaging`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNI18n (from `../node_modules/react-native-i18n`)
  - RNPermissions (from `../node_modules/react-native-permissions`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSound (from `../node_modules/react-native-sound`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - AppsFlyerFramework
    - CocoaAsyncSocket
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - FlipperKit
    - fmt
    - Google-Maps-iOS-Utils
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleMaps
    - GoogleUtilities
    - libevent
    - libwebp
    - MobileVLCKit
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  BVLinearGradient:
    :path: "../node_modules/react-native-linear-gradient"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
  Permission-Camera:
    :path: "../node_modules/react-native-permissions/ios/Camera/Permission-Camera.podspec"
  Permission-LocationAlways:
    :path: "../node_modules/react-native-permissions/ios/LocationAlways/Permission-LocationAlways.podspec"
  Permission-LocationWhenInUse:
    :path: "../node_modules/react-native-permissions/ios/LocationWhenInUse/Permission-LocationWhenInUse.podspec"
  Permission-Notifications:
    :path: "../node_modules/react-native-permissions/ios/Notifications/Permission-Notifications.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-appsflyer:
    :path: "../node_modules/react-native-appsflyer"
  react-native-background-timer:
    :path: "../node_modules/react-native-background-timer"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-document-picker:
    :path: "../node_modules/react-native-document-picker"
  react-native-geolocation-service:
    :path: "../node_modules/react-native-geolocation-service"
  react-native-image-picker:
    :path: "../node_modules/react-native-image-picker"
  react-native-image-resizer:
    :path: "../node_modules/react-native-image-resizer"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-netinfo:
    :path: "../node_modules/@react-native-community/netinfo"
  react-native-pager-view:
    :path: "../node_modules/react-native-pager-view"
  react-native-render-html:
    :path: "../node_modules/react-native-render-html"
  react-native-restart:
    :path: "../node_modules/react-native-restart"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-video:
    :path: "../node_modules/react-native-video"
  react-native-vlc-media-player:
    :path: "../node_modules/react-native-vlc-media-player"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  rn-fetch-blob:
    :path: "../node_modules/rn-fetch-blob"
  RNAudioRecorderPlayer:
    :path: "../node_modules/react-native-audio-recorder-player"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCMaskedView:
    :path: "../node_modules/@react-native-masked-view/masked-view"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFastImage:
    :path: "../node_modules/react-native-fast-image"
  RNFBApp:
    :path: "../node_modules/@react-native-firebase/app"
  RNFBMessaging:
    :path: "../node_modules/@react-native-firebase/messaging"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNI18n:
    :path: "../node_modules/react-native-i18n"
  RNPermissions:
    :path: "../node_modules/react-native-permissions"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSound:
    :path: "../node_modules/react-native-sound"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  AppsFlyerFramework: b3de9a49c6af8a8e38c44603e468b5e207f22466
  boost: 7dcd2de282d72e344012f7d6564d024930a6a440
  BVLinearGradient: 880f91a7854faff2df62518f0281afb1c60d49a3
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 5189b271737e1565bdce30deb4a08d647e3f5f54
  FBLazyVector: a31ac2336aea59512b5b982f8e231f65d7d148e1
  FBReactNativeSpec: 0976da6bc1ebd3ea9b3a65d04be2c0117d304c4c
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  FirebaseAnalytics: a2731bf3670747ce8f65368b118d18aa8e368246
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 06c414a21b122396a26847c523d5c370f8325df5
  Flipper: c7a0093234c4bdd456e363f2f19b2e4b27652d44
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 2dc99b02f658daf147069aad9dbd29d8feb06d30
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 70c50ce58ddaf67dc35180db05f191692570f446
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  FlipperKit: 37525a5d056ef9b93d1578e04bc3ea1de940094f
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 04b94705f318337d7ead9e6d17c019bd9b1f6b1b
  Google-Maps-iOS-Utils: 3343332b18dfd5be8f1f44edd7d481ace3da4d9a
  GoogleAppMeasurement: bb3c564c3efb933136af0e94899e0a46167466a8
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMaps: 20d7b12be49a14287f797e88e0e31bc4156aaeb4
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  hermes-engine: e89344b9e9e54351c3c5cac075e0275148fb37ba
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  MobileVLCKit: 144d5f565512d1147d63b0fa1379231b3fd66535
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  Permission-Camera: 9b70902f34a83c10e198d2d01f0e453e58842776
  Permission-LocationAlways: 0a0de4637662cfdf3603d6933779da2bdd657306
  Permission-LocationWhenInUse: 31f52ebddef50c306a585b5a82ca16c8ff582dec
  Permission-Notifications: 817390e18898f34adad940cd4141e771e77086ea
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  RCT-Folly: 424b8c9a7a0b9ab2886ffe9c3b041ef628fd4fb1
  RCTRequired: b6cea797b684c6d8d82ba0107cef58cbb679afdb
  RCTTypeSafety: d2eb5e0e8af9181b24034f5171f9b659994b4678
  React: e5aafc4c18040e8fbe0870a1f6df890d35f5af1d
  React-callinvoker: d345fd762faa4a3d371aedf40332abb09746ca03
  React-Codegen: 821ca0b8a9fb023eef3faab61afd2390658c8f1c
  React-Core: 6e27275ea4a91992f488bcc9c8575ffb564b504b
  React-CoreModules: 6cb0798606e69b33e8271a9da526e3d674bedb2c
  React-cxxreact: 63436ba2c7811121ca978ce60d49aa8786322726
  React-debug: b29cfcf06c990f0ea4b3d6430996baa71dd676af
  React-hermes: 077b82c248fe8e698820717a1d240c8502150c31
  React-jsi: 42edc74ef0479952c32c8659563ab9cd62353a75
  React-jsiexecutor: 95bdf0ab46024ca9849e08739b6abd8fe489cd33
  React-jsinspector: 8e291ed0ab371314de269001d6b9b25db6aabf42
  React-logger: d4010de0b0564e63637ad08373bc73b5d919974b
  react-native-appsflyer: 7f0d749eb1d2b22b96e8525ea8c66e99f0783888
  react-native-background-timer: 17ea5e06803401a379ebf1f20505b793ac44d0fe
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-document-picker: 516b1ec6a1fdf178af8fc478eb3abb4c20ae54e6
  react-native-geolocation-service: 608e1da71a1ac31b4de64d9ef2815f697978c55b
  react-native-image-picker: 3269f75c251cdcd61ab51b911dd30d6fff8c6169
  react-native-image-resizer: d9fb629a867335bdc13230ac2a58702bb8c8828f
  react-native-maps: 667f9b975549c6fa9b1631bf859440f68ebd3b8f
  react-native-netinfo: 48c5f79a84fbc3ba1d28a8b0d04adeda72885fa8
  react-native-pager-view: f8f8d74881aeaf2180b9ea2f4acf6255540555b8
  react-native-render-html: 984dfe2294163d04bf5fe25d7c9f122e60e05ebe
  react-native-restart: 7595693413fe3ca15893702f2c8306c62a708162
  react-native-safe-area-context: 141eca0fd4e4191288dfc8b96a7c7e1c2983447a
  react-native-video: c26780b224543c62d5e1b2a7244a5cd1b50e8253
  react-native-vlc-media-player: 0f9860bded57cd6764ac55b5c0e97347069cb40a
  react-native-webview: fb899c18b478ea412bf20d4bda7a527fa0b03545
  React-NativeModulesApple: 694679e4193a49c09f0a76ee27ec09b2c466d59c
  React-perflogger: 63606aeab27683112e1bd4ef25bd099ec1cb03f8
  React-RCTActionSheet: 5b39fc2b479d47325e5ac95193c482044bfebbc6
  React-RCTAnimation: d684a1de0e20c53e31376738839d1cda56b60486
  React-RCTAppDelegate: 3099e9aebf2f821503e65432e09a9423a37d767a
  React-RCTBlob: 0dcf271322ba0c0406fcd4a3f87cd7d951dfcc37
  React-RCTImage: b8bfa9ed1eecc7bb96d219f8a01f569d490f34fc
  React-RCTLinking: 32c9b7af01937d911010d8ab1963147e31766190
  React-RCTNetwork: c58ad73a25aa6b35258b6c59c0a24018c329fe96
  React-RCTSettings: 87eb46d6ca902981f9356a6d4742f9a453aa8fae
  React-RCTText: 1fc9f2052720a6587964721b9c4542c9a0e984c0
  React-RCTVibration: ff75e7530a22dc80a27fffdc07a2785d6bdf4f8e
  React-rncore: 52247442683082756b2fb3de145fb8149f15d1f6
  React-runtimeexecutor: 1c5219c682091392970608972655001103c27d21
  React-runtimescheduler: 8aea338c561b2175f47018124c076d89d3808d30
  React-utils: 9a24cb88f950d1020ee55bddacbc8c16a611e2dc
  ReactCommon: 76843a9bb140596351ac2786257ac9fe60cafabb
  rn-fetch-blob: f065bb7ab7fb48dd002629f8bdcb0336602d3cba
  RNAudioRecorderPlayer: 308940de4f9d1448a064874fd9d83479ae47c7a7
  RNCAsyncStorage: ec53e44dc3e75b44aa2a9f37618a49c3bc080a7a
  RNCClipboard: 41d8d918092ae8e676f18adada19104fa3e68495
  RNCMaskedView: fbf0642002ce7f1b6cb4e4822a58667f41392ff7
  RNCPushNotificationIOS: 64218f3c776c03d7408284a819b2abfda1834bc8
  RNDateTimePicker: 4f3c4dbd4f908be32ec8c93f086e8924bd4a2e07
  RNDeviceInfo: 59344c19152c4b2b32283005f9737c5c64b42fba
  RNFastImage: 5c9c9fed9c076e521b3f509fe79e790418a544e8
  RNFBApp: 9b25191f7a5e72c185b7e43fffb0d906869c4659
  RNFBMessaging: 99621af036e0aa85506390f77d79c9e6353aacca
  RNGestureHandler: e4be65a030ba7bb6499b820d95572db44f78d974
  RNI18n: e2f7e76389fcc6e84f2c8733ea89b92502351fd8
  RNPermissions: 4e3714e18afe7141d000beae3755e5b5fb2f5e05
  RNScreens: 88479f360ba7c848027cdf5ffdf425b550985e44
  RNSound: 6c156f925295bdc83e8e422e7d8b38d33bc71852
  RNSVG: af3907ac5d4fa26a862b75a16d8f15bc74f2ceda
  SDWebImage: a7f831e1a65eb5e285e3fb046a23fcfbf08e696d
  SDWebImageWebPCoder: 908b83b6adda48effe7667cd2b7f78c897e5111d
  SocketRocket: f32cd54efbe0f095c4d7594881e52619cfe80b17
  Yoga: 87e59f6d458e5061d2421086c5de994b3f7cd151

PODFILE CHECKSUM: c3e4e3e7d771f0cd004428be8aca9bb68b773eec

COCOAPODS: 1.16.2

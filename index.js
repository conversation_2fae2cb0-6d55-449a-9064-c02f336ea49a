/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';
import { name as appName } from './app.json';
import messaging from '@react-native-firebase/messaging';
import PushNotification from 'react-native-push-notification';
import Sound from 'react-native-sound';

PushNotification.configure({
    onNotification: function (notification) {
        console.log('NOTIFICATION:', notification);
    },
    popInitialNotification: true,
    requestPermissions: true,
});

// Register background handler
messaging().setBackgroundMessageHandler(async remoteMessage => {
    let sound = new Sound('notificationsound.mp3', Sound.MAIN_BUNDLE, (error) => {
        if (error) {
            console.log('failed to load the sound', error);
        } else {
            sound.play(); // have to put the call to play() in the onload callback
        }
    });
    sound.play((success) => {
        if (success) {
            console.log('Sound played successfully');
        } else {
            console.error('Failed to play the sound');
        }
    });

    console.log('Message handled in the background!', remoteMessage);
    PushNotification.localNotification({
        /* Android Only Properties */
        channelId: 'default-channel-id', // (required) channelId, if the channel doesn't exist, it will be created
        largeIcon: 'ic_launcher', // (optional) default: "ic_launcher". Use "" for no large icon.
        smallIcon: 'ic_notification', // (optional) default: "ic_notification" with fallback for "ic_launcher". Use "" for default small icon.
        color: 'red', // (optional) default: system default
        vibrate: true, // (optional) default: true
        vibration: 300, // vibration length in milliseconds, ignored if vibrate=false, default: 1000

        /* iOS and Android properties */
        title: remoteMessage.notification.title, // (optional)
        message: remoteMessage.notification.body, // (required)
        playSound: true, // (optional) default: true
        soundName: 'default', // (optional) Sound to play when the notification is shown. Value of 'default' plays the default sound.
    });

});
AppRegistry.registerComponent(appName, () => App);

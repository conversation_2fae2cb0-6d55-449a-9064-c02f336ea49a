#!/bin/bash

# <PERSON><PERSON>t to fix namespace issues in React Native packages for newer Android Gradle Plugin

echo "Fixing namespace issues in React Native packages..."

# Function to add namespace to a build.gradle file
add_namespace_manual() {
    local file="$1"
    local namespace="$2"

    if [ -f "$file" ]; then
        echo "Adding namespace to $file"
        # Check if namespace already exists
        if ! grep -q "namespace" "$file"; then
            # Use perl for more reliable replacement
            perl -i.bak -pe "s/^android \{/android {\n    namespace \"$namespace\"/" "$file"
        fi
    fi
}

# Fix remaining packages that need namespaces
add_namespace_manual "node_modules/react-native-geolocation-service/android/build.gradle" "com.agontuk.RNFusedLocation"
add_namespace_manual "node_modules/react-native-image-resizer/android/build.gradle" "fr.bamlab.rnimageresizer"
add_namespace_manual "node_modules/react-native-linear-gradient/android/build.gradle" "com.BV.LinearGradient"
add_namespace_manual "node_modules/react-native-maps/android/build.gradle" "com.airbnb.android.react.maps"
add_namespace_manual "node_modules/react-native-permissions/android/build.gradle" "com.zoontek.rnpermissions"
add_namespace_manual "node_modules/react-native-restart/android/build.gradle" "com.reactnativerestart"
add_namespace_manual "node_modules/react-native-safe-area-context/android/build.gradle" "com.th3rdwave.safeareacontext"
add_namespace_manual "node_modules/react-native-screens/android/build.gradle" "com.swmansion.rnscreens"
add_namespace_manual "node_modules/react-native-splash-screen/android/build.gradle" "org.devio.rn.splashscreen"
add_namespace_manual "node_modules/react-native-svg/android/build.gradle" "com.horcrux.svg"
add_namespace_manual "node_modules/react-native-webview/android/build.gradle" "com.reactnativecommunity.webview"
add_namespace_manual "node_modules/react-native-vlc-media-player/android/build.gradle" "com.ghondar.vlcplayer"
add_namespace_manual "node_modules/@react-native-picker/picker/android/build.gradle" "com.reactnativecommunity.picker"
add_namespace_manual "node_modules/@react-native-masked-view/masked-view/android/build.gradle" "org.reactnative.maskedview"
add_namespace_manual "node_modules/@react-native-community/netinfo/android/build.gradle" "com.reactnativecommunity.netinfo"
add_namespace_manual "node_modules/@react-native-community/slider/android/build.gradle" "com.reactnativecommunity.slider"
add_namespace_manual "node_modules/react-native-gesture-handler/android/build.gradle" "com.swmansion.gesturehandler"
add_namespace_manual "node_modules/react-native-reanimated/android/build.gradle" "com.swmansion.reanimated"
add_namespace_manual "node_modules/rn-fetch-blob/android/build.gradle" "com.RNFetchBlob"

echo "Namespace fixes completed!"

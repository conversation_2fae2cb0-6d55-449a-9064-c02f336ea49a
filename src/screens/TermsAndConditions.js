import React, { useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, Black, screenHeight } from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import { ScrollView } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Loading from '../components/Loading';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

const TermsAndConditions = props => {

    const [___mobileArticles, set___mobileArticles] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const settingss = useSelector(state => state.settings.settings)
    console.log('settings', settingss);
    const dispatch = useDispatch();


    useEffect(() => {

    }, []);

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.terms_and_conditions')} drawerPress={() => { props.navigation.navigate('More') }} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section1}>
                        <View style={styles.textContanier1}>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, marginBottom: '5%' }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '50%', height: 20, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>



                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.terms_and_conditions')} drawerPress={() => { props.navigation.navigate('More') }} backPress={() => { props.navigation.goBack() }} />

                <ScrollView style={{ width: '100%', height: '100%', marginBottom: '2%' }} showsVerticalScrollIndicator={false}>

                    <View style={styles.section}>
                        {/* <View style={styles.labelContanier}>
                                    <Text style={styles.label}>{item.title}</Text>
                                </View> */}
                        <View style={styles.textContanier}>
                            <Text style={styles.body}>{settingss.terms_and_conditions}</Text>
                        </View>
                    </View>


                </ScrollView>

                {/* <MyFooter navigation={this.props.navigation} /> */}
            </View>
        )
    }
}

export default TermsAndConditions;
const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white'
    },
    section1: { width: '90%', alignSelf: 'center' },
    textContanier1: {
        width: '100%',
        marginVertical: '5%',
        alignItems: 'flex-start',
    },
    section: { width: '90%', alignSelf: "center", },
    labelContanier: { width: '100%', marginVertical: '3%', alignItems: 'flex-start' },
    textContanier: { width: '100%', marginVertical: '2%', alignItems: 'flex-start' },
    label: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 20 },
    body: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' },
});
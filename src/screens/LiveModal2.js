import React, { useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Platform, Pressable } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, Black, screenHeight, Red, Red1 } from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Loading from '../components/Loading';
import Video from 'react-native-video';
import LoadingMore from '../components/LoadingMore';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import * as farmsActions from '../../Store/Actions/farms';
import * as cartActions from '../../Store/Actions/cart';
import { VLCPlayer, VlCPlayerView } from 'react-native-vlc-media-player';

import WebView from 'react-native-webview';
import Toaster from '../components/Toaster';
import { useIsFocused } from '@react-navigation/native';
import CircularProgress3 from '../components/CircularProgress3';
const LiveModal2 = props => {
    const [selectedSheep, setSelectedSheep] = useState(0)
    const [farmImageUrl, setFarmImageUrl] = useState('')
    const [link, setLink] = useState('')
    const [linkFarm, setLinkFarm] = useState('')
    const [loadingMore, setLoadingMore] = useState(false)

    const [isLoading, setIsLoading] = useState(true);


    useEffect(() => {
        console.log('Loading started');
        const timer = setTimeout(() => {
            setIsLoading(false);
            console.log('Loading ended');
        }, Platform.OS == 'ios' ? 20000 : 6000);

        return () => clearTimeout(timer);
    }, []);
    useEffect(() => {
        let index = 0;
        for (let item of props.route.params.sheeps) {

        }

        console.log('props.route.params.status', props.route.params.status);
        console.log('props.route.params.sheep', props.route.params.sheep);
        console.log('props.route.params.status.id', props.route.params.status.id);
        console.log('setLink', link);
        // if (props.route.params.sheep && props.route.params.sheep[0] && props.route.params.sheep[0].sheep) {
        setLink(props.route.params.sheep.farm ? props.route.params.sheep.farm.camera_url : '')
        setLinkFarm(props.route.params.sheep.farm ? props.route.params.sheep.farm.buying_camera : '')
        setSelectedSheep(props.route.params.sheep.id)
        setFarmImageUrl(props.route.params.sheep.farm ? props.route.params.sheep.farm.image : '')
        // }
    }, [])

    return (

        <View style={styles.modal}>



            {props.route.params.status && props.route.params.status.name
                ?
                <View style={{ transform: [{ rotate: '90deg' }], bottom: screenHeight / 4, start: screenWidth / -9.5, position: 'absolute', zIndex: 100000, paddingHorizontal: 5, minWidth: screenHeight / 4.5, height: screenWidth / 12, backgroundColor: Red, borderRadius: 5, alignItems: 'center', justifyContent: 'center', }}>
                    <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 33, }}>{props.route.params.status.name}</Text>
                </View>
                :
                <></>
            }

            <View style={{ height: screenHeight / 1, width: screenWidth, justifyContent: 'space-between', alignItems: 'center', transform: [{ rotate: I18nManager.isRTL ? '0deg' : '0deg' }], backgroundColor: Black }}>

                {/* {camera == 1
        ? */}

                {/* :
        <View></View>
    }  */}


                <View style={{ width: screenWidth / 1, height: screenHeight / 6, flexDirection: 'row', marginTop: -screenHeight / 100, zIndex: 10000 }}>
                    <View style={{ ...styles.infoContainer, ...{ backgroundColor: null } }}>
                        {/* <View style={{ width: screenWidth / 5, alignSelf: "center", flexDirection: 'row', height: screenHeight / 30, alignItems: "center", justifyContent: "center", }}>
            <View style={{ width: 8, height: 8, borderRadius: 10, backgroundColor: 'red', alignSelf: 'center', marginHorizontal: 5, marginTop: '2%' }}></View>
            <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text>
        </View> */}
                    </View>

                    <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'flex-end', zIndex: 1000, transform: [{ rotate: '90deg' }], height: screenWidth / 3, width: screenWidth / 5, marginStart: screenWidth / 5 }}>
                        {/* <Button onPress={() => { toggleCameras(1) }} style={styles.camContainer}>
            <Image source={require('../images/modrek/cam.png')} style={styles.camImage} />
        </Button> */}
                        {/* {selectedSheepsIds.length == 0
            ?
            <View style={{ width: '18%', height: '50%', }}>
            </View>
            : */}

                    </View>

                    {/* <Button onPress={() => {
              if (link != props.link) {
                console.log('link', link);
                console.log('props.link', props.link); setLink(props.link)
              }
            }} style={[styles.camContainer3, { backgroundColor: link == props.link ? Red : White }]}>
              <Image source={require('../images/modrek/cam.png')} style={[styles.camImage, { tintColor: link == props.link ? White : Red }]} />
            </Button> */}
                </View>

                <>


                    <VLCPlayer
                        source={{
                            initType: 2,
                            hwDecoderEnabled: 1,
                            hwDecoderForced: 1,
                            uri: props.route.params.status && props.route.params.status.id == 1 ?
                                props.route.params.sheep && props.route.params.sheep.farm && props.route.params.sheep.farm.camera_url :
                                props.route.params.status && props.route.params.status.id == 2 ?
                                    props.route.params.sheep && props.route.params.sheep.farm && props.route.params.sheep.farm.buying_camera :
                                    props.route.params.status && props.route.params.status.camera_url,
                            // uri: 'rtsp://admin:<EMAIL>:4444/cam/realmonitor?channel=7&subtype=1',
                            initOptions: [
                                '--no-audio',
                                '--rtsp-tcp',
                                '--network-caching=150',
                                '--rtsp-caching=150',
                                '--no-stats',
                                '--tcp-caching=150',
                                '--realrtsp-caching=150',
                            ],
                        }}
                        style={[styles.backgroundVideo]}
                        // source={{ uri: linkk }}
                        videoAspectRatio="16:9"
                        isLive={true}
                        autoplay={true}
                        autoReloadLive={true}
                        hwDecoderEnabled={1}
                        hwDecoderForced={1}
                        mediaOptions={{
                            // ':network-caching': 0,
                            // ':live-caching': 300,
                        }}
                        onError={(err) => console.log("video error:", err)}
                        resizeMode='contain'
                    />


                    {isLoading ? (
                        <>
                            <View style={{
                                transform: [{ rotate: '90deg' }],
                                position: 'absolute', height: screenWidth, width: screenHeight / 1.5, alignItems: 'center',
                                justifyContent: 'center', top: screenHeight / 4
                            }}>
                                <Text style={{ color: White, marginBottom: 5, fontFamily: appFontBold, fontSize: screenWidth / 35 }}>{'برجاء الانتظار جاري تحميل البث...'}</Text>
                                {/* <LoadingBar duration={20000} /> */}
                                <CircularProgress3 size={100} strokeWidth={10}
                                    duration={Platform.OS == 'ios' ? 20000 : 6000} />
                                {/* <CircularLoader size={100} strokeWidth={4} duration={20000} /> */}

                            </View>

                        </>
                    ) : (
                        <></>
                    )
                    }

                    <View style={styles.infoContainer1}>
                        <View style={{ width: screenWidth / 5, alignSelf: "center", flexDirection: 'row', height: screenHeight / 30, alignItems: "center", justifyContent: "center", }}>
                            <View style={{ width: 8, height: 8, borderRadius: 10, backgroundColor: 'red', alignSelf: 'center', marginEnd: 5, marginTop: '2%' }}></View>
                            <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text>
                        </View>
                    </View>
                    {farmImageUrl
                        ?
                        <View style={styles.farmImageContainer}>
                            <Image source={{ uri: farmImageUrl }} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
                        </View>
                        :
                        <></>
                    }
                </>




                <View style={{ width: screenWidth / 1.1, height: screenHeight / 12, flexDirection: 'row', marginBottom: screenHeight / 20 }}>
                    {/* <Button onPress={() => { props.navigation.navigate('Home', { weightId: null, sheepCatId: null }) }} style={styles.backImageContainer}> */}
                    <Button onPress={() => { props.navigation.goBack() }} style={styles.backImageContainer}>
                        <Image source={require('../images/modrek/arrow.png')} style={styles.backImage} />
                    </Button>
                    {props.route.params.sheeps && props.route.params.sheeps.length >= 1
                        ?
                        <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'space-between', marginStart: screenWidth / 3.7, transform: [{ rotate: '90deg' }], flexDirection: 'row', height: screenWidth / 1.5, width: screenHeight / 15, }}>
                            <ScrollView
                                horizontal={false}
                                showsVerticalScrollIndicator={false}
                                style={{ alignSelf: "center", }}
                            >

                                {props.route.params.sheeps.map((item, index) => {
                                    return (
                                        // <TouchableOpacity
                                        //     onPress={() => { addSheepToPicked(item) }}
                                        //     style={{ ...styles.colorContainer, ...{ backgroundColor: selectedSheepsIds.find(sheep => sheep == item.id) ? item.stamp_color : White, borderWidth: 2.5, borderColor: selectedSheepsIds.find(sheep => sheep == item.id) ? null : item.stamp_color } }} >
                                        //     <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: selectedSheepsIds.find(sheep => sheep == item.id) ? White : item.stamp_color, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                                        // </TouchableOpacity>
                                        <Pressable
                                            // onPress={() => {

                                            //     if (props.route.params.sheeps.length == 1) {
                                            //     } else {
                                            //         props.navigation.replace('LiveModal2', { sheeps: props.route.params.sheeps, sheep: item.sheep, status: props.route.params.status, link: props.route.params.link })

                                            //     }

                                            // }}
                                            style={{ ...styles.colorContainer, ...{ backgroundColor: item.sheep && item.sheep.id == selectedSheep ? item.sheep.collar_color : White, borderWidth: item.sheep && item.sheep.id == selectedSheep ? 2.5 : 0, borderColor: White } }} >
                                            <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: item.sheep && item.sheep.id == selectedSheep ? White : item.sheep && item.sheep.collar_color, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                                        </Pressable>
                                    )
                                })}
                            </ScrollView>
                        </View>
                        :
                        <></>
                    }

                </View>

            </View >

        </View>
    );
}


export default LiveModal2;
const styles = StyleSheet.create({
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1,
        height: screenHeight / 1,
        position: 'absolute',
        // top: screenHeight / 18,
        alignSelf: 'center',
        backgroundColor: Black,
        // borderRadius: 10,
        // paddingVertical: '8%',
        justifyContent: 'center',
        // paddingHorizontal: '5%',
        overflow: 'hidden'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 22,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    modalbuttonContainer2: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    // backgroundVideo: {
    //   flex: 1,
    //   // backgroundColor: 'red',
    //   height: screenWidth / 1.1,
    //   width: screenHeight / 1.2,
    //   // position: 'absolute',
    //   // top: -screenHeight / 100,
    //   // left: 0,
    //   // bottom: 0,
    //   // right: 0,
    //   transform: [{ rotate: '90deg' }],
    //   // top: -screenHeight / 20,
    //   // zIndex: 1000000000
    // },
    backgroundVideo: {
        flex: 1,
        // backgroundColor: 'red',
        width: screenHeight / 1.35,
        height: screenWidth / 1,
        // position: 'absolute',
        top: -screenHeight / 100,
        // left: 0,
        // bottom: 0,
        // right: 0,
        transform: [{ rotate: '90deg' }],
        // top: -screenHeight / 20,
        // zIndex: 1000000000
    },
    farmImageContainer: {
        width: screenWidth / 8,
        height: screenWidth / 8,
        // marginLeft: screenWidth / 17,
        position: 'absolute',
        end: -screenWidth / 30,
        top: screenHeight / 30,
        // marginTop: screenHeight / 40,
        overflow: 'hidden',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Red1,
        borderRadius: 10,
    },
    // farmImageContainer: {
    //   width: screenWidth / 8,
    //   height: screenWidth / 8, marginLeft: screenWidth / 17, borderRadius: 20, overflow: 'hidden',
    //   flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
    //   alignItems: 'center',
    //   justifyContent: 'center',
    //   marginTop: screenWidth / 14, backgroundColor: White, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 100, start: 0
    // },
    infoContainer1: {
        width: screenWidth / 5,
        height: screenWidth / 15, marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: screenWidth / 14, backgroundColor: Red, borderRadius: 10, position: 'absolute', zIndex: 10000,
    },
    colorContainer: {
        width: screenHeight / 22,
        height: screenHeight / 22,
        alignSelf: 'center',
        marginHorizontal: 10,
        borderRadius: screenHeight / 10,
        borderWidth: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 20,
        marginStart: 20
    },
    camImage: {
        width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ rotate: '90deg' }]
    },
    camImage1: {
        width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ scaleX: -1 }]
    },
    camContainer2: {
        width: screenWidth / 9,
        height: screenWidth / 9,
        borderRadius: screenWidth / 15,
        backgroundColor: Red,
        position: 'absolute',
        top: screenHeight / 15,
        start: screenWidth / 5.5,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer3: {
        width: screenWidth / 9,
        height: screenWidth / 9,
        borderRadius: screenWidth / 15,
        backgroundColor: Red,
        // marginTop: screenHeight / ,
        position: 'absolute',
        top: screenHeight / 3.1,
        end: screenHeight / 100,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center', marginStart: screenWidth / 5, marginTop: screenHeight / 25,
        transform: [{ rotate: '260deg' }]
    },


    colorContainer: {
        width: screenHeight / 22,
        height: screenHeight / 22,
        alignSelf: 'center',
        marginHorizontal: 10,
        borderRadius: screenHeight / 10,
        borderWidth: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 4
    },
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    backgroundVideo: {
        flex: 1,
        // backgroundColor: 'red',
        width: screenHeight / 1.35,
        height: screenWidth / 1,
        // position: 'absolute',
        top: -screenHeight / 100,
        // left: 0,
        // bottom: 0,
        // right: 0,
        transform: [{ rotate: '90deg' }],
        // top: -screenHeight / 20,
        // zIndex: 1000000000
    },
    // backImageContainer: {
    //     width: screenWidth / 10,
    //     height: screenWidth / 10,
    //     backgroundColor: White,
    //     position: 'absolute',
    //     bottom: '3%',
    //     start: '5%',
    //     alignItems: 'center',
    //     justifyContent: 'center',
    //     transform: [{ rotate: '90deg' }]
    // },
    backImage: {
        width: "50%", height: "70%", tintColor: Red, resizeMode: "contain", alignSelf: 'center',
    },
    infoContainer: {
        width: screenWidth / 5,
        height: screenWidth / 15, marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: DarkGrey, borderRadius: 10,
    },
    infoContainer1: {
        width: screenWidth / 5,
        height: screenWidth / 15, marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: Red, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 6, start: 0
    },
    farmImageContainer: {
        width: screenWidth / 8,
        height: screenWidth / 8, marginLeft: screenWidth / 17, borderRadius: 20, overflow: 'hidden',
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: White, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 100, start: 0
    },
    reserveContainer: {
        width: screenWidth / 5,
        height: screenWidth / 11,
        // marginLeft: I18nManager.isRTL ? '5%' : '0%',
        // marginRight: I18nManager.isRTL ? '0%' : '5%',
        borderRadius: 10, borderWidth: 0, borderColor: White,
        backgroundColor: '#A30000',
        // position: 'absolute',
        // bottom: screenHeight / 5,
        // start: screenWidth / 1.35,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'flex-end',
        marginVertical: '4%'
        // borderWidth: 1,
        // borderColor: '#A30000'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer: {
        width: screenWidth / 9,
        height: screenWidth / 9,
        borderRadius: screenWidth / 15,
        backgroundColor: Red,
        // position: 'absolute',
        // bottom: screenHeight / 20,
        // start: screenWidth / 1.3,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer2: {
        width: screenWidth / 9,
        height: screenWidth / 9,
        borderRadius: screenWidth / 15,
        backgroundColor: Red,
        // position: 'absolute',
        // top: screenHeight / 15,
        // start: screenWidth / 1.3,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer3: {
        width: screenWidth / 9,
        height: screenWidth / 9,
        borderRadius: screenWidth / 15,
        backgroundColor: Red,
        // position: 'absolute',
        // top: screenHeight / 15,
        // start: screenWidth / 1.3,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center', marginStart: screenWidth / 5, marginTop: screenHeight / 25
        // transform: [{ rotate: '90deg' }]
    },
    backImageContainer: {
        width: screenWidth / 10,
        height: screenWidth / 10,
        backgroundColor: White,
        // position: 'absolute',
        // bottom: screenHeight / 30,
        // start: I18nManager.isRTL ? screenWidth / 25 : screenWidth / 1.20,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }],
        borderWidth: 1,
        borderColor: MediumGrey,
        alignSelf: 'center'
    },
    backImage: {
        width: "50%", height: "70%", tintColor: Red, resizeMode: "contain", alignSelf: 'center',
    },
    camImage: {
        width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ rotate: '90deg' }]
    },
    camImage1: {
        width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ scaleX: -1 }]
    },
});
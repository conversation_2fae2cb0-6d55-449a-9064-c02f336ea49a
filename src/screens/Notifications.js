import React, { useEffect, useState } from 'react';
import { StyleSheet, View } from "react-native";
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch } from 'react-redux';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import Notificationcontainer from '../components/Notificationcontainer';
import { MediumGrey, White, WhiteGery, screenHeight, screenWidth } from '../components/Styles';
import { strings } from './i18n';


const Notifications = props => {

    const [locale, setLocale] = useState("")
    const [fcmToken, setFcmToken] = useState('')
    const [client_id, setClient_id] = useState('')
    const [user_id, setUser_id] = useState('')
    const [session_token, setSession_token] = useState('')
    const [newNotifications, setNewNotifications] = useState([])
    const [notificatins, setNotificatins] = useState([{}, {}, {}])
    const [page, setPage] = useState(1)
    const [last_page, setLast_page] = useState(1)
    const [loading, setLoading] = useState(false)
    const [loadingMore, setLoadingMore] = useState(false)
    const dispatch = useDispatch();


    useEffect(() => {

    }, []);
    // useFocusEffect(
    //     React.useCallback(() => {
    //         const onBackPress = () => {
    //             // BackHandler.exitApp()
    //             props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
    //             return true;
    //         };

    //         BackHandler.addEventListener('hardwareBackPress', onBackPress);

    //         return () =>
    //             BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    //     }, []),
    // );

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Notifications')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>

                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Notifications')} backPress={() => { props.navigation.goBack() }} />

                {/* <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                    onScroll={({ nativeEvent }) => {
                        if (Controller.isCloseToBottom(nativeEvent)) {
                            LoadMore();
                        }
                    }}
                >
                    {notificatins.length == 0
                        ?
                        <Text style={{ fontSize: screenWidth / 18, fontFamily: appFontBold, color: 'black', marginTop: screenHeight / 3, alignSelf: 'center' }}>{strings('lang.ThereIsNoNotify')}</Text>
                        :
                        <View></View>
                    }
                    {notificatins.map((item, key) => (
                        <Notificationcontainer item={item} />
                    ))}


                </ScrollView> */}

                <FlatList
                    data={notificatins}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item }) => (
                        <Notificationcontainer item={item} />
                    )}
                    keyExtractor={item => item.id}
                    // contentContainerStyle={{ width: screenWidth / 1.1, justifyContent: 'space-between', flexDirection: 'row', flexWrap: 'wrap' }}
                    style={{ alignSelf: 'center', width: screenWidth }}
                // onEndReached={LoadMore}
                // onEndReachedThreshold={0.1} //While you scolling the offset number and your data number will increases.So endReached will be triggered earlier because our data will be too many
                />

                <View style={{ height: screenHeight / 7.5 }}></View>

                <MyFooter navigation={props.navigation} />
            </View>
        )
    }
}


export default Notifications;
const styles = StyleSheet.create({

    section: { width: '100%', alignSelf: 'center' },
    textContanier: {
        width: '100%',
        marginVertical: '2%',
        alignItems: 'flex-start',
    },

});

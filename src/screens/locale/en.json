{"lang": {"Login_Successfuly": "<PERSON>gin <PERSON>y", "Register_Successfuly": "Registration Successfuly", "EmailOrPhone": "Email or Phone", "Logout": "Logout", "Login": "<PERSON><PERSON>", "mobile_number": "Mobile number", "No_Results": "No Results", "Purepayment": "Pure payment", "Partnerlocation": "Partner location", "Splitthepriceofthecarcass": "Split the price of the carcass", "Determinetheaskingprice": "Determine the asking price", "fullpayment": "full payment", "Whatdoyouthinkoftheservice": "What do you think of the service?", "Submit": "Submit", "step1": "From here you determine the type of sheep", "step2": "From here you determine the weight", "step3": "After that, the sheep merchants' stores will be shown to you according to the type of sheep and the specific weight", "step4": "From here, the sheep are selected according to the colored collar on the sheep’s neck", "step5": "According to the arrows from right and left, you can move between the two cameras to choose clearly", "step6": "From here you can visit the stores", "step7": "From here you can add sheep from different stores in the same order", "step8": "From here, after selecting the sheep, confirm your reservation", "step9": "Confirm purchase", "step10": "Confirming the purchase of a half-carcass. It is preferable for the partner to know and agree with him. You can divide the bill between you and him, and you can pay the bill in full.", "Returntothehomepage": "Return to the home page", "Thedeliverypriceforonecarcassis29riyals": "The delivery price for one carcass is 29 SR", "Yourprice": "Your price", "Mychoice": "My choice", "Evaluationcompletedsuccessfully": "Evaluation completed successfully", "Yourorderisontheway": "Your order is on the way", "Stagesoftheslaughterhouseproductionline": "Stages of the slaughterhouse production line", "Complaintsandsuggestions": "Complaints and suggestions", "Articletitle": "Article title", "Startrequest": "Start request", "ending": "ending", "Skinningstages": "Skinning stages", "knowmore": "know more", "Addapartner": "Add a partner", "Watchtheveterinaryexamination": "Watch the veterinary examination", "Pricepayabletothepartner": "Price payable to the partner", "Thepriceisinclusiveofqtm": "The price is inclusive of qtm", "processing": "processing", "Articledetails": "Article details", "barn": "barn", "myorders": "My Orders", "Acceptance": "Acceptance", "deliverylocation": "delivery location", "Paymenttype": "Payment type", "Checkyourorder": "Check your order", "reject": "reject", "Estimateddeliverytime": "Estimated delivery time", "Transfertherequesttotheslaughterhouse": "Transfer the request to the slaughterhouse", "Ordernotes": "Order notes", "chopping": "chopping", "typeofsheep": "Type of sheep", "Numberofsheep": "Number of sheep", "Colroofsheep": "Colro of sheep", "PartnershipApplications": "Partnership Applications", "sendthelink": "send the link", "CashonDeliveryFee": "Cash on Delivery Fee", "year": "Age", "Yourpriceowed": "Your price owed", "kg": "Kg", "Partnershipapplicationdetails": "Partnership application details", "Please_insert_valid_phone": "Please insert valid phone", "Please_insert_phone": "Please insert phone", "Please_insert_New_Password": "Please insert new password", "Change_Password": "Change Password", "Old_Password": "Old password", "addaddress": "add address", "New_Password": "New Password", "Forget_Password": "Forget password", "Password": "Password", "ReType_New_Password": "Retype new Password", "Confirm": "Confirm", "Send": "Send", "Name": "Name", "Edit_Profile": "Edit Profile", "notes": "Notes", "Notes": "Notes", "Email": "E-mail", "Emailor?": "Username or phone", "Phone": "Phone", "Language": "Language", "Change": "Change", "forgot?": "Forgot your password?", "New_user": "New user? Please ", "Notifications": "Notifications", "No_Notifications": "No Notifications", "Create_Account": "Create Account", "Reset_Password": "Reset Password", "Search": "Search", "PleaseChooseCountryFirst": "Please Choose Country First", "Account": "Profile", "password": "Password", "quiz_account": "already have an account ?", "Forget_Password_?": "Forget Password ?", "back": "Back", "Date": "Date", "quiz_account_2": "Don't have account ? Please", "Please_insert_name": "Please insert name", "Please_insert_field": "Please insert field", "Please_insert_lastname": "Please insert lastname", "Please_insert_email": "Please insert email", "Please_insert_password": "Please insert password", "Phone_must_be_11_characters": "Phone must be 11 characters", "Password_must_be_more_6_characters": "Password must be more 6 characters", "Please_insert_password_confirmation": "Please insert password confirmation", "Password_confirmation_must_be_more_6_characters": "Password confirmation must be more 6 characters", "Please_insert_old_password": "Please insert old password", "old_password_must_be_more_6_characters": "old password must be more 6 characters", "password_must_be_matched": "Password must be matched", "login_continuou": "please login to continue", "something_wrong": "Something Went Wrong", "something_wrong1": "You cannot send complaint now", "setting": "Settings", "country": "Country", "City": "City", "Country": "Country", "Area": "Area", "addaD": "Add AD", "editaD": "Edit AD", "CoverPhoto": "Cover Photo", "next": "Next", "previous": "Previous", "paymentDetails": "Methods of payment and communication", "price": "price", "Installmentsavailable": "Installments available", "Delegated": "Delegated", "address": "Ad Address", "Details": "Details", "Type_of_property": "Type of property", "Choose_a_department": "Choose a department", "Overlooking": "Overlooking", "Space": "Space", "Where _is_your_property_located?": "Where is your property located?", "special_marque": "special marque", "Detailed_data_for_the_property": "Detailed data for the property", "the_rooms": "the rooms", "Floor": "Floor", "Baths": "Baths", "The_year_of_construction": "The year of construction", "Finishing_type": "Finishing type", "addnote": "Add Note", "note": "Note", "add": "Add", "cancel": "Cancel", "Customer_Service": "Customer Service", "message": "message", "osol_alalmya": "Osol Alalmya", "for_digital_investment_and_marketing": "For digital investment and marketing", "choose_language": "Choose Language", "choose_countrey": "Choose Country", "Like": "Like", "countrey": "Country", "Egypt": "Egypt", "SaudiArabia": "SaudiArabia", "Rent": "Rent", "Filter": "Filter", "Depart": "<PERSON><PERSON><PERSON>", "Call": "Call", "PoweredBy_Osol": "PoweredBy Osol", "sign_in_with": "Sign in with Social Media", "not_now": "Not now", "Registerion": "Registerion", "User_Type": "User Type", "Owner": "Owner", "Register": "Register", "I_agree_to_the_terms_and_conditions": "I agree to the terms and conditions", "terms_and_conditions": "Terms and conditions", "Do_you_want_Arabic_text_and_a_question": "Do you want Arabic text and a question?", "Home": "Home", "Share": "Share", "Deal_of_the_day": "Deal of the day", "News": "News", "Favourites": "Favourites", "Favourite": "Favourite", "Osolel3almya": "Osolel 3almya", "More": "More", "Messages": "Messages", "Reply": "Reply", "SendMessage": "SendMessage", "You": "You", "On": "On", "EGP": "EGP", "view": "view", "Off": "Off", "Contact_Us": "Contact Us", "Tell_us_what_you_think": "Tell us what you think", "About_osol_alalmya": "About osol <PERSON>", "About_osol": "About OSOL", "Profile": "Profile", "Save": "Save", "Add_Real_Estate": "Add Real Estate", "My_Real_Estates": "My Real Estates", "My_Notes": "My Notes", "Share_App": "Share App", "Exit": "Exit", "BathRooms": "BathRooms", "BedRooms": "Bed Rooms", "ADSAvailabletoday": "ADS Available today", "TodayDeals": "Today Deals", "Closed": "Closed", "MyAds": "My Ads", "MyNotes": "My Notes", "AdDetails": "Ad Details", "Location": "Location", "Rooms": "Rooms", "Edit": "Edit", "Finishing": "Finishing", "Buildingyear": "Building year", "Seller": "<PERSON><PERSON>", "SendAdvertiser": "Send Advertiser", "NewsDetails": "News Details", "CallAdvertiser": "Call Advertiser", "Intialdata": "Intial Data", "BuildingAddress": "Building Address", "data_details_for_build": "data details for build", "customertype": "Please choose customer type", "country_id": "Please choose country ", "Please_enter_note_first": "Please enter note first", "Please_update_note_first": "Please Update note first", "sign_up_with": "Sign up with", "Egp": "EGP", "Morenews": "More News", "Today": "Today", "Deal": "Deal", "My_Services": "My Services", "legal_services": "legal services", "Transfer_of_Ownership": "Transfer of Ownership", "Real_estate_tax": "Real estate tax", "Other_services": "Other services", "Real_estate_services": "Real estate services", "Selling_to_others": "Selling to others", "Real_estate_consulting": "Real estate consulting ", "Building": "Building", "loginMessage": "Please check username and password", "Done": "Done", "Reset": "Reset", "Filter_by": "Filter <PERSON>", "Ad_Date": "Ad Date", "View": "View", "Yes": "Yes", "No": "No", "News_Title": "News Title", "ViewON": "View ON", "adIsNegotiable": "Negotiable", "Description": "Description", "addressD": "Address", "mustLogin": "Please login to continue", "addtofav": "added to favourite successfully", "StausChangedSuccessfully": "Status Changed Successfully", "AdStatus": "Ad Status", "OpenLocationOnMap": "Open location on map", "Oldpasswordnotright": "Old password not right", "passwordchangedsuccessfully": "password changed successfully", "profileupdatedsuccessfully": "profile updated successfully", "Delete": "Delete", "newest": "Newest", "lowPrice": "Low Price", "highPrice": "High Price", "SAR": "SAR", "Insert_Phone": "Insert Phone", "activation_Code": "Activation Code", "Wrong_Code": "Wrong Code", "ad_Successfuly": "Your AD uploaded successfully", "Publish": "Publish", "addnotessuccess": "Note Addes successfully", "Pleasewaitad": "Adding the advertisement and uploading Images, please wait", "yourphone": "Your Phone is ", "ChooseStatus": "Choose<PERSON><PERSON><PERSON>", "Activate": "Activate", "Enter_the_activation_code": "Enter the activation code", "Enter_a_phone_number": "Enter the activation code", "Activate_the_phone_number": "Activate the phone number", "Select_your_location": "Select your location", "Locate": "Locate", "LocateTheConnection": "Locate the connection", "Shopping": "Shopping", "ChooseARegion": "Choose a region", "AddressList": "Address list", "DeliveryToAnotherAddress": "Delivery to another address", "ChooseTheLocationOnTheMap": "Choose the location on the map", "SidiBisher": "<PERSON><PERSON>", "GamalAbdelNasserST": "<PERSON><PERSON><PERSON> Abd <PERSON> St.", "AddressDetails": "Address details", "AdditionalAddress": "Additional Address", "Youraddresshasbeenaddedsuccessfully": "Your address has been added successfully", "PleaseInsertBuildingName": "Please Insert Building Name", "Please_insert_street_name": "Please Insert Street Name", "AddressType": "Address Type", "Street": "Street", "apartment": "Apartment", "AddMoreDirections": "Add More Directions", "AdditionalPhone": "Additional Phone", "SpecialMark": "Special Mark", "Optional": "(Optional)", "Processing_Request": "Processing request", "Go_To_Home": "Go to home", "Adresses": "Adresses", "Previous_Requests": "Previous requests", "Favorite_Products": "Favorite products", "Credit_Card": "Credit card", "Cart": "<PERSON><PERSON>", "Offers": "Offers", "Add_To_Cart": "Add to cart", "Call_On": "Call on", "Special_Offers": "Special offers", "The_Most_Prominent_Sections": "The most prominent sections", "CompleteRegistration": "Complete Registration", "Orders": "Requests", "Complaints": "<PERSON><PERSON><PERSON><PERSON>", "Complaint": "<PERSON><PERSON><PERSON><PERSON>", "WhoAreWe": "Whe are we", "CallUs": "Call us", "PrivacyPolicy": "Privacy policy", "YouDoNotHaveAnAccount": "You do not have an account", "RegisterNow": "Register now", "ActivateTheAccount": "Activate the account", "CompleteTheData": "Complete the data", "EnterTheActivationCode": "Enter the activation code", "Forename": "<PERSON><PERSON><PERSON>", "LastName": "Last name", "IDNumber": "ID number", "TheIDNumberIsWrongPleaseTryAgainWithAValidIDNumber": "The ID number is wrong please try again with a valid ID number", "IAgreeToTheTermsAndConditions": "I agree to the terms and conditions", "ChooseAWorker": "Choose a worker", "ChooseWorker": "Please choose worker first", "CustomRequest": "Custom request", "ReplacementRequest": "Replacement request", "FileAComplaint": "Send a complaint", "TrackEmployment": "Track employment", "ConnectiveEmployment": "Arrival workers", "ComplaintsBox": "Complaints box", "Replies": "Replies", "ComplaintDetails": "<PERSON><PERSON><PERSON><PERSON>", "YouCannotChooseFromDifferentCountries": "You cannot choose from different countries", "TheName": "The name", "Job": "Job", "Countrey": "<PERSON><PERSON>", "Age": "Age", "Order now": "OrderNow", "Religion": "Religion", "filtering": "Filtering", "Show": "Show", "ShowAll": "Show all", "CurriculumVitae": "Curriculum Vitae", "WorkerNumber": "Worker number", "MonthlySalary": "Monthly salary", "Nationality": "Nationality", "DateOfBirth": "Date of birth", "PlaceOfBirth": "Place of birth", "SocialStatus": "Social status", "NumberOfChildren": "Number of children", "MobileNumber": "Mobile", "AcademicLevel": "Academic level", "Weight": "Weight", "Length": "Length", "passport": "passport", "TheNumber": "The number", "ReleaseDate": "Release date", "ExpiryDate": "Expiry date", "placeOfIssue": "place of issue", "Experience": "Experience", "Duration": "Duration", "Languages": "Languages", "Arabic": "Arabic", "English": "English", "HomeWork": "HomeWork", "Cleaning": "Cleaning", "ChildRearing": "ChildRearing", "WashingAndIroning": "Washing and ironing", "TheCook": "The cook", "OtherSkills": "Other skills", "Pending": "Pending", "Processing": "Processing", "Expired_Canceled": "Expired/Canceled", "Canceledd": "Canceled", "Expired": "Expired", "TheSideOfTheComplainant": "The defendant", "Worker": "Worker", "Office": "Office", "TheNameOfTheEmployee": "The name of the employee", "AttachAFile": "Attach a file", "ItDoesNotPerformTheRequiredTasks": "It does not perform the required tasks", "UncooperativeInCarryingOutHisDuties": "Uncooperative in carrying out His duties", "Laziness": "Laziness", "TalkingOnTheMobilePhonePermanently": "Talking on the mobile phone permanently", "Naughty": "Naughty", "LackOfAttentionToOrders": "Lack of attention to orders", "Others": "Others", "ComplaintsBox1": "No title for this complaint", "AlternativeRequests": "Alternative requests", "ReasonForReporting": "Reason for reporting", "Report": "Report", "Visas": "Visa", "VisaNumber": "Visa number", "visaType": "visa type", "visaNumber": "visa number", "VisaDate": "Visa date", "Addition": "Addition", "TrackYourWorkers": "Track your workers", "MedicalExamination": "Medical examination", "Authorization": "Authorization", "Contract": "Contract", "School": "School", "LabourOffice": "Labour office", "ImplementationOf": "ImplementationOf", "Ticket": "Ticket", "PersonalData": "Personal data", "Status": "Status", "OrderNumber": "Order number", "Airline": "Airline", "VoucherInformation": "Voucher information", "DownloadTheContract": "Download the contract", "SpecialRequests": "Special requests", "Melady": "<PERSON><PERSON>", "Hijri": "<PERSON><PERSON><PERSON>", "Free": "Free", "WorkerAccessInformation": "Worker access information", "ArrivalTime": "Arrival time", "AnAirport": "An airport", "Destination": "Destination", "FutureName": "Future name", "FlightNumber": "Flight number", "CarrierLines": "Carrier lines", "Gender": "Gender", "Male": "Male", "Female": "Female", "Muslim": "Muslim", "Christian": "<PERSON>", "Average": "Average", "AboveAverage": "Above average", "High": "High", "M_A": "M.A.", "PhD": "PhD", "TheShape": "TheShape", "ArrivalStation": "Arrival station", "Condition1": "Condition 1", "Condition2": "Condition 2", "Condition3": "Condition 3", "Confirmation": "Confirmation", "ArrivingWorkers": "Arriving workers", "DateOfArrival": "Date of arrival", "AirportName": "Airport name", "ProfilePersonly": "Profile personly", "OldPassword": "Old password", "NewPassword": "New password", "ConfirmNewPassword": "Confirm New password", "PleaseEnterNewPassword": "Please enter new password", "TermsAndConditions": "Terms and conditions", "ApplicationPolicy": "Application policy", "ApplicationPermissions": "Application permissions", "ApplicationUpdates": "Application updates", "TheNameOfTheOffice": "The name of the office", "Address": "Address", "Region": "Region", "District": "District", "UnifiedNumber": "Unified number", "Topic": "Topic", "Map": "Map", "edit": "Edit", "track": "Track", "pleaseCkeckData": "Please ckeck all data", "pleaseEnterData": "Please enter all data", "pleaseEnterWorker": "Please choose worker", "wrongCode": "Wrong code", "addDone": "Added successfully", "ThereIsNoOrdes": "There is no ordes", "ThereIsNoComplaints": "There is no complaints", "ThereIsNoNotify": "There is no notifications", "ThereIsNoWorkers": "There is no workers", "ActivateAccount": "Activate account", "CompleteData": "Complete data", "termsConditions": "Please agree to terms and conditions", "DuplicatedIdentityNumber": "Duplicated identity number", "10Num": "Identity number must be 10 digit", "NO_DATA_FOR_THIS_CHOICES": "There are no workers", "NO_DATA_FOR_THIS_CHOICES2": "There are no contracts", "displayReplies": "<PERSON><PERSON><PERSON> replies", "downloaded": "Stored in files", "PermissionDenied": "Download permission denied", "chooseAgain": "<PERSON>sse again", "licenseNumber": "license number", "officeData": "office data", "Resendcode": "Resend code", "verification": "verification", "Ordersandcontracts": "Orders and contracts", "Householdlaborwithmonthlyrental": "Household labor with monthly rental", "laborsector": "labor sector", "Intermediateinrecruitment": "Intermediate in recruitment", "medicalsector": "medical sector", "Driverswithmonthlyrental": "Drivers with monthly rental", "Individualsector": "Individual sector", "Laborsectordemand": "Labor sector demand", "send": "send", "Rentalapplicationsandcontracts": "Rental applications and contracts", "paid": "paid", "Unpaid": "Unpaid", "payingoff": "paying off", "NumberofWorkers": "Number of Workers", "thedetails": "Details", "theamount": "the amount", "Package": "Package", "Thecontractperiod": "The contract period", "Paymentmethod": "Payment method", "serviceprovider": "service provider", "Orderdetails": "Order details", "Signature": "Signature", "Packages": "Packages", "Packageprice": "Package price", "totaltax": "total tax", "Contractstartdate": "Contract start date", "Skillsandhomework": "Skills and homework", "otherskills": "other skills", "Previousexperience": "Previous experience", "Recruit": "Recruit", "Packagedetails": "Package details", "taxprice": "tax price", "Total": "Total", "Transportingtheworkertotheresidence": "Transporting the worker to the residence", "SR": "ريال", "detectlocation": "detect location", "Pleasereviewthetermsofthecontractbeforesigning": "Please review the terms of the contract before signing", "Iagree": "I agree to", "Termsofthecontract": "Terms of the contract", "Pay": "Pay", "mytickets": "my complaints", "newticket": "new complaint", "Closedtickets": "Closed complaintس", "Complaint'sname": "<PERSON><PERSON><PERSON><PERSON>'s name", "complaintnumber": "complaint number", "Servicename": "Service name", "change": "change", "fullname": "full name", "Accountdetails": "Account details", "Paymentdata": "Payment data", "Bankaccount": "Bank account", "Intermediaterequests": "Intermediate requests", "Chooseemployment": "Choose employment", "Specialrequest": "Special request", "educationalqualification": "educational qualification", "Othercertificates": "Other certificates", "Scientificexpertise": "Scientific expertise", "Personalskills": "Personal skills", "Subjectname": "Subject name", "Subjectdetails": "Subject details", "testText": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.", "testDate": "10 minutes ago", "package": "subscription", "month": "Month", "from": "from", "noofproviders": "no. of providers", "contractnotavailable": "A contract is not available until the availability of labor", "familyNo": "number of family", "mediationinrecruitment": "Mediation inrecruitment", "choosePacFirst": "please choose package first", "chooseCouFirst": "please choose countrey first", "chooseWorrkerFirst": "please choose workers first", "chooseAddressFirst": "please choose address first", "acceptTerms": "please accept Terms and conditions first", "noWorkers": "There is no workers", "Paid": "<PERSON><PERSON> successfully", "Payment": "Payment", "Username": "User name", "Forgotyourpassword": "Forgot your password?", "Donthaveanaccountpleasedo": "Don't have an account? please do", "Signup": "Sign up", "Skip": "<PERSON><PERSON>", "Confirmpassword": "Confirm password", "Iagreetothetermsandconditions": "I agree to the terms and conditions", "Pleaseenterthe4digitcodesenttoyou": "Please enter the 4-digit code sent to you", "Resendthecodewithin": "Resend the code within", "Seconds": "Seconds", "Deliverytimes": "Delivery times", "Choosedeliverytime": "Choose delivery time", "PM": "PM", "AM": "AM", "Choosetheday": "Choose the day", "Whatareyoulookingfor": "What are you looking for?", "Deliveryto": "Delivery to", "Sections": "Sections", "SpecialOffers": "Special Offers", "MyAccount": "My account", "All": "All", "Sort": "Sort", "Discount": "Discount", "Addtocart": "Add to cart", "Theproduct": "The product", "Thesize": "The size", "Evaluation": "Rates", "Quantity": "Quantity", "Addtocartforfixeddelivery": "Add to cart for fixed delivery", "Pillprice": "Pill price", "Cartonprice": "Carton price", "Add": "Add", "Constant": "Constant", "Basket": "Basket", "Subtotal": "Subtotal", "Productsweremindyouof": "Products we remind you of", "Continueshopping": "Continue shopping", "Nextone": "Next one", "Completetheapplication": "Complete order", "Titleanddate": "Title and date", "Addatitle": "Add a title", "Choosethecity": "Choose the city", "Specialmarque": "Special mark", "Totalproductsinclusiveofqqt": "Total products inclusive of qqt", "Delivery": "Delivery", "Discountvalue": "Discount value", "Finaltotal": "Final total", "Portfolio": "Wallet", "Itsinthewallet": "It's in the wallet", "Point": "Point", "Replacewith": "Replace with", "Redeempoints": "Redeem points", "Enterthediscountcode": "Enter the discount code", "Enter": "Enter", "Areyouaspecialcustomer": "Are you a special customer?", "Chooseyourpaymentmethod": "Choose your payment method", "Paymentbymada": "Payment by mada", "ApplepayPayby": "Apple pay Pay by", "PaybyVisa": "Pay by Visa", "Stcpayby": "Pay by stc", "Paiementwhenrecieving": "Payment when recieving", "Currentbalance": "Current balance", "Takeadvantageoftheexcellencediscount": "Take advantage of the excellence discount", "Pleaseenterthemobilenumberofthecustomer": "Please enter the mobile number of the customer", "Resendthecode": "Resend the code", "Deliverytime": "Delivery time", "Byweight": "By weight", "ByOption": "By option", "ExpectedProducts": "Expected products", "Youhave": "You have", "Breakevenpoint": "Breakeven point", "Walletbalance": "Wallet balance", "Ordertracking": "Order tracking", "reOrder": "Re-Order", "Orderconfirmed": "Order confirmed", "Buyingsucceeded": "Buying succeeded", "Onmyway": "On my way", "Sentdeliveredhanded": "<PERSON><PERSON> delivered handed", "Canceled": "Canceled", "Delivered": "Delivered", "Productrating": "Product rating", "Areyousuretocanceltheorder": "Are you sure to cancel the order", "Cancellingorder": "Cancelling order", "Reorder": "Reorder", "Areyousureaboutreconnecting": "Are you sure about reconnecting", "Baskets": "Baskets", "Totalnumberofproducts": "Total number of products", "Products": "Products", "Basketprice": "Basket price", "Onetimepurchase": "One time purchase", "Subscriptions": "Subscriptions", "Subscribenow": "Subscribe now", "SubscriptionBaskets": "Subscription Baskets", "Youdonthaveabasket": "You don't have a basket", "Createanewbasket": "Create a new basket", "Subscriptionvaliduntil": "Subscription valid until", "Newbasket": "New basket", "Basketname": "Basket name", "DeliveryFrequency": "Delivery Frequency", "Everyday": "Every day", "Everyweek": "Every week", "Permonth": "Per month", "Contact": "Contact", "Reviews": "Reviews", "pleaseputyourrating": "please put your rating", "whatssapp": "whatssapp", "Filterbymodel": "Filter by model", "new": "new", "thefurthest": "the furthest", "lowestprice": " lowest price", "themostexpensive": "the most expensive", "leastrated": " least rated", "toprated": "top rated", "theclosest": "the closest", "Application": "Application ", "old": "old", "Areyousuretodeletetheproduct": "Are you sure to delete the product", "OrdersTracking": "Order Tracking", "FixedConnectionProducts": "Fixed Connection Products", "Directconnection": "Direct connection", "Changepassword": "Change password", "Cancel": "Cancel", "Putyourrating": "Put your rating", "Searchbybarncode": "Search by barn code", "customersservice": "customers service", "Myorder": "Myorder", "Direct": "Direct", "farmdetails": "farm details", "Determiningthetimeofentrytotheslaughterhouse": "Determining the time of entry to the slaughterhouse", "Cuttingandprocessing": "Cutting and processing", "hi": "hi", "hardware": "hardware", "Advertisers": "Advertisers", "Services": "Services", "Cranes": "cranes", "cranes": "cranes", "Laurie": "<PERSON>", "Changethelanguage": "Change the language", "Transfer": "Transfer", "rate": "rate", "profiledetails": "ProfileDetails", "Attachments": "Attachments", "Sendorder": "Send order", "Unavailable": "Unavailable", "Advertiser": "Advertiser", "Moreabouttheadvertiser": "More about the advertiser", "writeyourmessage": "write your message", "writeyourOrder": "write your order", "Communicateviasocialmedia": "Communicate via social media", "snapchat": "snap chat", "Instagram": "Instagram", "Twitter": "Twitter", "Urdulanguage": "Urdu", "englishLanguage": "English", "arabicLanguage": "Arabic", "withoutaAramcolicense": "without a Aramco license", "Aramcolicense": "Aramco license", "withoutalicense": "without a license", "license": "License", "Averagerentpricefortheday": "Average rent price for the day", "Averagerentpriceforthemonth": "Average rent price for the month", "Priceisnotnegotiable": "Price is not negotiable", "Priceisnegotiable": "Price is negotiable", "driver": "Driver", "Adnumber": "Ad number", "Typeoflicense": "Type of license", "stomachmodel": "stomach model", "stomachsize": "stomach size", "Mycurrentsubscription": "My current subscription", "PreviousSubscriptions": "Previous Subscriptions", "Bill": "Bill", "numberofads": "number of ads", "Packagetype": "Package type", "Subscriptionnumber": "Subscription number", "typeofmassage": "Type of massage", "myreviews": "my reviews", "maintenancerequest": "maintenance request", "Holidaytype": "Holiday type", "ChooseHolidays": "Choose Holidays", "Anindividual": "An individual", "TaxNumber": "Tax Number", "CommercialRegistrationNo": "Commercial Registration No", "acompany": "a company", "Youdonothaveanyactiveads": "You do not have any active ads", "Addequipment": "Add equipment", "welcometoshawl": "welcome to shawl", "massage1": "You can start adding your own equipmentSubscribe to the package that best suits your needs", "massage2": "Please subscribe and activate your ads Favorites so that customers can view them", "communication": "communication", "stomachname": "stomach name", "available": "available", "other": "other", "Averagedailyrent": "Average daily rent", "Averagerent": "Average rent price", "Averagemonthlyrent": "Average monthly rent", "isnotnegotiable": "is not negotiable", "isnegotiable": "is negotiable", "pricediscount": "price discount", "Aramco": "Aramco", "stomachimage": "stomach image", "massage3": "In the event that a picture is not uploaded, an avatar picture will be automatically placed for the stomach", "Choosethestomach": "Choose the stomach", "stomachcondition": "stomach condition", "whatsappnumber": "whatsapp number", "Writethesizeofthestomach": "Write the size of the stomach", "Choosestomachsize": "Choose stomach size", "Choosetheequipmentmodel": "Choose the equipment model", "Notavailableuntil": "Not available until", "Subscribedsuccessfully": "Subscribed successfully", "Enabled": "Enabled", "themonth": "the month", "Notenabled": "Not enabled", "Fourthpackage": "Fourth package", "Thirdpackage": "Third package", "secondpackage": "second package", "firstpackage": "first package", "bank": "Payment by bank transfer", "socialist": "socialist", "PaymentOptions": "Payment Options", "Accessories": "Accessories", "currentPassowrd": "Current Password", "HolidayDetails": "Holiday Details", "DailyRent": "Daily rent", "Monthly": "Monthly", "FinishedAd": "Finished ad", "NotAvailableTill": "Not available till", "messageCode": "Please enter the 4-digit code that was sent to the number", "Sec": "Seconds", "Continue": "Continue", "freePackages": "Free Packages", "freePackage": "Free Package", "days": "days", "free2": "Free", "freePackageDesc1": "Allows you to advertise", "freePackageDesc2": "equipments for the duration of the bundle", "goldenPackages": "Golden Packages", "inMonth": "In Month ", "diamondPackages": "Diamond Packages", "save": "Save", "buy": "Buy", "model": "Model", "productNumber": "Product Number", "currentOrders": "Current Order", "RentFor": "rent for", "StatusOfRequest": "Status of Request", "StatusOfTask": "Status of task", "ConfirmPassword": "Confirm Password", "PleaseChooseUserType": "Please Choose UserType", "sortByNearest": "Sort by nearest", "sortByFarthest": "Sort by farthest", "sortByNewest": "Sort by newest", "sortByOldest": "Sort by oldest", "sortByTheLowestNumberOfLiters": "Sort by the lowest number of liters", "sortByTheLargestNumberOfLiters": "Sort by the largest number of liters", "missionDetails": "Mission Details", "fuelRatioRate": "Fuel ratio rate", "fuelconsumptionRate": "Fuel consumption rate", "percentageOfFuelRemainingInTheTank": "Percentage of fuel remaining in the tank", "remainingLiters": "Remaining liters", "theAmountOfFuelInTheTank": "The amount of fuel in the tank", "generatorInformaion": "Generator information", "tankInformaion": "Tank information", "tankType": "Tank type", "generatorType": "Generator type", "generatorSize": "Generator size", "motorType": "Motor type", "amountOfFuelFilled": "Amount of fuel filled", "amountOfOldFuel": "Amount of fuel before", "attachaVideo": "attach a video", "attachaPicture": "attach a picture", "missionAccomplished": "Mission accomplished", "missionUpdated": "Mission updated successfully", "missionReported": "Mission reported successfully", "pleaseGoToTheSiteToCompleteTheTask": "Please go to the site to complete the task", "Close": "Close", "missionCompletedSuccessfully": "Mission completed successfully", "theAttachedVideo": "The attached video", "theAttachedPhoto": "The attached photo", "litter": "Litter", "writeYourNotes": "Write your notes", "fuelFillingMissions": "Fuel Filling Missions", "extraMissions": "Extra missions", "finishedMissions": "Finished missions", "newMissions": "New missions", "oldMissions": "Old missions", "Towers": "Towers", "PicturesOfDieselMeasurementsAvailableInTheTank": "Picture of diesel measurements available in the tank", "Day": "day", "AreyousuretoLogout": "Are you sure to logout", "ThereIsAnUpdate": "There is an update available in the store now", "ThereIsAnMaintananc": "There is an error please try later", "UpdateNow": "Update now", "Retry": "Retry", "Edit_Task": "Edit task", "CheckInternet": "Please check your internet connection and try again", "messageLater": "You don't have internet now. Do you want to save the data and try later?", "messageOverFlowTank": "Please fill in less than the tank capacity", "messageSaved": "The data has been saved and will be updated automatically when you connect to the Internet", "messageSavedMisson": "You have an outdated task so wait for it to be updated", "technical": "Technical", "ReasonForReturning": "Reason for returning", "ReasonForCancel": "Reason for cancel", "KM": "KM", "Distance": "Distance", "APictureOfCounterBefore": "Picture of the counter before refueling", "APictureOfCounterAfter": "Picture of the counter after refueling", "PictureWhile": "Picture while refueling", "APictureOfQuantityAndMeasurementBefore": "Picture of the quantity and measurement before refueling", "APictureOfQuantityAndMeasurementAfter": "Picture of the quantity and measurement after refueling", "message1": "You cannot modify the mission data. Please be in a space", "message2": "meters from the site", "validEmail": "Please enter valid email", "Passwordnotright": "Password must be matched", "message10": "Please select an address", "message11": "Please select a time", "message12": "Please select a payment method", "message13": "The request has expired, please try again", "message14": "Please enter the coupon", "message15": "The coupon has been added successfully", "message16": "The coupon has been removed successfully", "COD": "COD", "sentsuccesfully": "<PERSON><PERSON> succesfully", "editImageSuccessfully": "Edite image successfully", "Averagemarketprices": "Average market prices", "Trader": "Trader", "messageLive2": "Choose from the merchant’s stores to start the live broadcast, and if you like something, click on the option to go to book from the merchant’s sheep.", "AllContent": "All content", "Movethesheep": "Move the sheep", "Subscripe": "Subscripe", "stores": "Stores", "openthephoto": "Open the photo", "head": "Head", "messageShare1": "There is a customer who has reserved half of this sheep. If you want to participate in the other half, go to the reservation", "messageShare2": "There is a customer who has reserved a quarter of this sheep. If you want to participate in the quarter, go to the reservation", "Confirmthereservationofacompletecarcass": "Confirm the reservation of a complete carcass", "Transfertoreservation": "Transfer to reservation", "Confirmreservationofhalfacarcass": "Confirm reservation of half a carcass", "Confirmationofreservationofaquarterofaslaughteredanimal": "Confirmation of reservation of a quarter of a slaughtered animal", "messageWarning": "Dear customers, we receive orders from 6 am to 8 pm", "Sheepisnotavailable": "Sheep is not available", "Subscriped": "Subscriped", "messageTime": "Entry of the mutant sheep tomorrow at 8 a.m. and delivery tomorrow from 11 a.m. to 2 p.m.", "Averagelength": "Average length", "Mediumfromneckto": "Medium from neck to", "Theageforsalenow": "The age for sale now", "sealcolor": "seal color", "client": "Client", "check": "Check", "Halfasacrifice": "Half a sacrifice", "Aquarterofasacrifice": "A quarter of a sacrifice", "timeMessage": "Entry of the sheep into the slaughterhouse", "Thisrequestcannotbefound": "This request cannot be found", "Addthecode": "Add the code", "Scanthecode": "Scan the code", "collarcolor": "collar color", "moveMessage": "You have the Shepherd Moving Sheep feature three times per day", "referencenumber": "reference number", "sheepAvailable": "Available sheeps", "Gotobooking": "Go to booking", "Gotobookingg": "Move to booking", "AllContentTrader": "All content trader", "Gotothemarket": "Go to the market", "Twistinginthemarket": "Twisting in the market", "Addareservation": "Add a reservation", "MoveToAddareservation": "Move to add reservation", "reservationconfirmation": "Reservation confirmation", "Therequestmustbeconfirmed": "The request must be confirmed", "min": "minute", "Bookingdetails": "Booking details", "Advancebookingconfirmation": "Advance booking confirmation", "reservedquantity": "Reserved quantity", "message17": "In case of return, the cart will be unloaded from the sheep", "Therequestwasaccepted": "Request has been accepted", "Therequestwasrejected": "Request has been rejected", "Therequesthasbeenrejected": "The request has been rejected", "Confirmthepurchaseofhalfacarcass": "Confirm the purchase of half a carcass", "message19": "Expected arrival time from", "to": "to", "message20": "The partner must download the application in order for the application to be accepted", "watch": "watch", "rate1": "Rating of the taste of the carcass", "Choosetocutandpreparefirst": "Choose to cut and prepare first", "rate2": "Rating of the Modrek platform service", "messageFinshTime": "The application must be completed within the specified time period", "messageFinshTime2": "The order reservation must be confirmed within the specified time period", "checkyourorder": "Check your order", "Types": "Types", "Standarddelivery": "Standard delivery", "Youwillreceiveyourorderwithin48hours": "You will receive your order within 48 hours", "Expediteddelivery": "Expedited delivery", "Youwillreceiveyourorderonthesameday": "You will receive your order on the same day", "Choosethedeliverymethod": "Choose the delivery method"}}
import React, { useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Pressable } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor2, appColor1, Black, Red, Red1, WhiteBlue, DarkBlue, MediumBlue } from '../components/Styles';
import { Button } from 'native-base';
import { strings } from './i18n';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import LinearGradient from 'react-native-linear-gradient';
import Carousel, { Pagination, ParallaxImage } from 'react-native-snap-carousel';
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';


const ArticleDetails = props => {

    const [entries, setEntries] = useState(['https://hatrabbits.com/wp-content/uploads/2017/01/random.jpg', 'https://helpx.adobe.com/content/dam/help/en/photoshop/using/convert-color-image-black-white/jcr_content/main-pars/before_and_after/image-before/Landscape-Color.jpg', 'https://www.pixsy.com/wp-content/uploads/2021/04/ben-sweet-2LowviVHZ-E-unsplash-1.jpeg']);
    const [DATA, setDATA] = useState({});
    const [farm, setFarm] = useState({});
    const [cart, setCart] = useState({});
    const [buttonId, setButtonId] = useState('0');
    const [data, setData] = useState({});
    const [selectedSheeps, setSelectedSheeps] = useState([]);
    const [selectedSheepsIds, setSelectedSheepsIds] = useState([]);
    const [subTotal, setSubTotal] = useState(0);
    const [time, setTime] = useState({});
    const [seconds, setSeconds] = useState(60);
    const [disable, setDisable] = useState(false);
    const refRbSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();


    useEffect(() => {
        setLoading(true)
        let item = props.route.params.item
        setData(item)
        console.log('mm', item);
        setLoading(false)

    }, []);
    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header title={strings('lang.Articledetails')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 3,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 40, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '40%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '90%',
                                    height: screenHeight / 2.5,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header title={strings('lang.Articledetails')} backPress={() => { props.navigation.goBack() }} />
                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%', height: screenHeight, alignSelf: 'center' }}
                >
                    <View style={{ height: screenHeight / 3, width: '100%', backgroundColor: White, marginBottom: '4%' }}>
                        <Image source={{ uri: data.image }} style={{ resizeMode: 'contain', width: '100%', height: '100%', }} />
                    </View>
                    <Text numberOfLines={1} style={{ fontSize: screenWidth / 20, fontFamily: appFontBold, color: Red, alignSelf: 'center' }}>{data.title}</Text>
                    <View style={{ width: '90%', minHeight: screenHeight / 2, backgroundColor: MediumGrey, borderTopEndRadius: 15, borderTopStartRadius: 15, alignSelf: 'center', padding: 5, paddingBottom: screenHeight / 8 }}>
                        <Text style={{ fontSize: screenWidth / 27, fontFamily: appFontBold, color: Black, marginHorizontal: '2%', alignSelf: 'flex-start' }}>
                            {data.body}
                        </Text>
                    </View>

                </ScrollView>

                <View style={{ height: screenHeight / 10 }}></View>

                <MyFooter current={'Cart'} navigation={props.navigation} />

            </View>
        )
    }
}


export default ArticleDetails;
const styles = StyleSheet.create({
    activeButton: { height: 80, width: screenWidth / 2.4, backgroundColor: MediumBlue, borderWidth: 1, borderColor: Blue, borderRadius: 15, alignItems: 'center', justifyContent: 'center' },
    button: { height: 80, width: screenWidth / 2.4, backgroundColor: MediumGrey, borderRadius: 15, alignItems: 'center', justifyContent: 'center', },
    rbsheetText: { fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black, marginVertical: '5%' },
    textBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, textDecorationLine: 'line-through', textDecorationStyle: 'dashed', textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },

    priceGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 28 },
    priceGreenBig: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 24 },
    priceBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28 },
    priceBlackBig: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 24 },

    activeConfirm: {
        backgroundColor: Red,
        width: screenWidth / 1.4,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 10,
        marginTop: '10%'
    },
    disapleConfirm: {
        backgroundColor: Red,
        width: screenWidth / 1.4,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 10,
        marginTop: '10%',
        opacity: .5
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },

    imageContainer: {
        flex: 1,
        marginBottom: Platform.select({ ios: 0, android: 1 }), // Prevent a random Android rendering issue
        backgroundColor: White,
        borderRadius: 0,
        justifyContent: "center",
        borderWidth: .8,
        borderColor: MediumGrey
        // overflow:'hidden'
    },
    image: {
        // ...StyleSheet.absoluteFillObject,
        resizeMode: "cover",
        height: '100%',
        width: '100%',
        alignSelf: "center",
    },
});
import { <PERSON><PERSON>, Textarea } from 'native-base';
import React, { useEffect, useState } from 'react';
import { I18nManager, Image, Keyboard, Linking, StyleSheet, Text, View } from "react-native";
import { ScrollView } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import * as settingAction from '../../Store/Actions/settings';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import { Black, DarkGrey, MediumGrey, Red, Red1, White, WhiteGreen, appFontBold, screenHeight, screenWidth } from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';

const ComplaintsAndSuggestions = props => {

  const [___mobileArticles, set___mobileArticles] = useState([]);
  const [review, setReview] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const dispatch = useDispatch();

  const whatssapp = useSelector(state => state.settings.whatssApp)

  useEffect(() => {
    console.log('mooo', whatssapp);
  }, []);

  const setSuggestions = async () => {
    let suggestion = await dispatch(settingAction.suggestions(review));
    console.log('suggestion', suggestion);
    if (suggestion.success == true) {
      Toaster(
        'top',
        'success',
        WhiteGreen,
        (strings('lang.sentsuccesfully')),
        White,
        1500,
        screenHeight / 15,
      );
      props.navigation.navigate('Home', { weightId: null, sheepCatId: null, link: '' })
    } else {
      Toaster(
        'top',
        'danger',
        Red1,
        suggestion.message,
        White,
        1500,
        screenHeight / 15,
      );
    }

    setLoadingMore(false);

  }


  const [keyboardIsOpen, setKeyboardIsOpen] = React.useState(false);
  Keyboard.addListener("keyboardDidShow", () => {
    setKeyboardIsOpen(true);
  });
  Keyboard.addListener("keyboardDidHide", () => {
    setKeyboardIsOpen(false);
  });

  return (
    <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
      <Header cart cartPress={() => { props.navigation.push('Cart') }} title={strings('lang.Complaintsandsuggestions')} drawerPress={() => { props.navigation.navigate('More') }} backPress={() => { props.navigation.goBack() }} />

      <ScrollView style={{ width: '100%', height: '100%', }} showsVerticalScrollIndicator={false}>
        <View style={{ width: '90%', height: screenHeight / 2, alignSelf: 'center', marginVertical: '2.5%' }}>
          <Textarea
            onChangeText={text => setReview(text)}
            value={review}
            placeholder={strings('lang.Tell_us_what_you_think')}
            placeholderTextColor={MediumGrey}
            style={{
              width: '100%',
              alignSelf: 'center',
              borderRadius: 15,
              paddingHorizontal: '5%',
              borderColor: MediumGrey,
              color: DarkGrey,
              borderWidth: 1,
              fontFamily: appFontBold,
              height: '100%',
              textAlignVertical: 'top',
              fontSize: screenWidth / 30,
              textAlign: I18nManager.isRTL ? 'right' : 'left',
            }}
          />
        </View>
        <View style={{ height: screenHeight / 16, width: '100%', marginTop: '5%' }}>
          <Button
            onPress={() => { setSuggestions() }}
            style={styles.buttonContainer}>
            <Text style={styles.buttonText}>{strings('lang.send')}</Text>
          </Button>
        </View>

        <View style={{ height: screenHeight / 16, width: '100%', marginTop: '3%' }}>
          <Button
            onPress={() => { Linking.openURL(`whatsapp://send?text=&phone=${whatssapp}`) }}
            style={styles.buttonWhats}>
            <Image source={require('../images/modrek/whatss.png')}
              style={{
                width: '10%',
                height: '70%',
                resizeMode: 'contain'
              }}
            />
            <Text style={[styles.buttonText, { color: Black }]}>{strings('lang.whatssapp')}</Text>
          </Button>
        </View>
      </ScrollView>

      {!keyboardIsOpen &&
        <>
          <View style={{ height: screenHeight / 7 }} />
          <MyFooter navigation={props.navigation} />
        </>
      }


    </View>
  )
}


export default ComplaintsAndSuggestions;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white'
  },
  buttonContainer: {
    backgroundColor: Red,
    width: '90%',
    height: screenHeight / 18,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    borderRadius: 25,

  },
  buttonWhats: {
    backgroundColor: White,
    width: '90%',
    height: screenHeight / 18,
    alignItems: "center",
    justifyContent: "center",
    alignSelf: "center",
    borderRadius: 25,
    borderWidth: 1,
    borderColor: MediumGrey,
    flexDirection: 'row',
  },
  buttonText: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28
  },
});
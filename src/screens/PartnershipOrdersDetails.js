import React, { useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Alert } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor1, Black, appColor2, LightGreen, Red, Red1 } from '../components/Styles';
import { Button, Input, Textarea, Toast } from 'native-base';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
import { KeyboardAwareScrollView, ScrollableComponent } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import LoadingMore from '../components/LoadingMore';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as ordersActions from '../../Store/Actions/orders';
import Toaster from '../components/Toaster';
import LiveModal from '../components/LiveModal';
import LiveModal1 from '../components/LiveModal1';

const PartnershipOrdersDetails = props => {

    const [PartnershipOrders, setPartnershipOrders] = useState([]);
    const [address, setAddress] = useState({});
    const [discount, setDiscount] = useState('');
    const [methodId, setMethodId] = useState(0);
    const [review, setReview] = useState('');
    const refRBSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [item, setItem] = useState(props.route.params.item);
    const [orderStatuses, setOrderStatuses] = useState([]);
    const [order, setOrder] = useState({});
    const dispatch = useDispatch();
    const [ModalVisible, setModalVisible] = useState(false);
    const [ModalVisible1, setModalVisible1] = useState(false);

    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const getCurrentOrder = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getOrder(props.route.params.item.id));
                if (response.success == true) {
                    setOrder(response.data)
                    setOrderStatuses(response.data.statuses)
                    setPartnershipOrders(response.data.sheep)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getCurrentOrder();
    }, []);

    useEffect(() => {
        console.log('Loading started');
        const timer = setTimeout(() => {
            setIsLoading(false);
            console.log('Loading ended');
        }, Platform.OS == 'ios' ? 20000 : 6000);

        return () => clearTimeout(timer);

    }, []);

    const submit = async (status) => {
        try {
            setLoadingMore(true)
            // let response = await dispatch(ordersActions.AcceptOrRejectOrder(props.route.params.item.id, status));
            // if (response.success == true) {
            if (status == 0) {
                let response = await dispatch(ordersActions.AcceptOrRejectOrder(props.route.params.item.id, 0));
                Toaster(
                    'top',
                    'danger',
                    Red,
                    strings('lang.Therequestwasrejected'),
                    White,
                    1500,
                    screenHeight / 15,
                );
                props.navigation.push('MyOrders')
            }
            else {
                // Toaster(
                //     'top',
                //     'success',
                //     DarkGreen,
                //     strings('lang.Therequestwasaccepted'),
                //     White,
                //     1500,
                //     screenHeight / 15,
                // );
                props.navigation.push('CompleteOrder0', { shared: true, order: order })
            }
            // }
            // else {
            //     if (response.message) {
            //         Toaster(
            //             'top',
            //             'danger',
            //             Red,
            //             response.message,
            //             White,
            //             1500,
            //             screenHeight / 15,
            //         );
            //     }
            // }
            setLoadingMore(false);
        } catch (err) {
            console.log('err', err)
            setLoadingMore(false);
        }
    };

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Partnershipapplicationdetails')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 12,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 5,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 7,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 9,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 10
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 9,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 10
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 9,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 10
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 9,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                    marginStart: 10
                                }}
                            />
                        </SkeletonPlaceholder>


                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '100%', justifyContent: 'space-between' }}>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 3,
                                    height: screenHeight / 15,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 3,
                                    height: screenHeight / 15,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,

                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Partnershipapplicationdetails')} backPress={() => { props.navigation.goBack() }} />

                {loadingMore
                    ?
                    <LoadingMore />
                    :
                    <></>
                }

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                >


                    <FlatList
                        data={PartnershipOrders}
                        showsVerticalScrollIndicator={false}
                        renderItem={({ item }) =>
                            <View style={{ flexDirection: 'row', width: screenWidth / 1.1, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start', paddingVertical: 10 }}>
                                <View style={{ width: screenWidth / 8, height: screenWidth / 8, backgroundColor: item.sheep && item.sheep.collar_color, alignItems: 'center', justifyContent: 'center', marginEnd: 8, marginStart: 4, alignSelf: 'center', borderRadius: screenWidth / 60, marginVertical: 8 }}>
                                    <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>{item.sheep && item.sheep.id}</Text>
                                </View>

                                <View style={{ flexDirection: 'column', width: screenWidth / 2 }}>
                                    <Text style={{ color: DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 28, alignSelf: 'flex-start', textAlign: 'left' }}>{`${item.sheep && item.sheep.sheep_category && item.sheep.sheep_category.name} / ${item.sheep && item.sheep.weight && item.sheep.weight.from}KG - ${item.sheep && item.sheep.weight && item.sheep.weight.to}KG `}</Text>
                                    <Text style={{ color: DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 28, alignSelf: 'flex-start', textAlign: 'left' }}>{item.sheep && item.sheep.farm && item.sheep.farm.code}</Text>
                                    {/* <Text style={{ color: DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 28, alignSelf: 'flex-start', textAlign: 'left' }}>{`${item.sheep && item.sheep.sheep_category && item.sheep.sheep_category.name} / ${item.sheep && item.sheep.weight && item.sheep.weight.from}KG - ${item.sheep && item.sheep.weight && item.sheep.weight.to}KG / ${item.sheep.farm.code} `}</Text> */}
                                    {order.payment_type == "Full"
                                        ?
                                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, alignSelf: 'flex-start' }}>{'0'} {strings('lang.SR')}</Text>

                                        :
                                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, alignSelf: 'flex-start' }}>{order.shared_value && order.shared_value} {strings('lang.SR')}</Text>

                                    }
                                </View>

                                <View style={{ alignItems: 'center', justifyContent: 'center', flexDirection: 'column', width: screenWidth / 4.2, height: screenWidth / 8 }}>
                                    <Button onPress={() => { setModalVisible(!ModalVisible); setIsLoading(true) }} style={{ width: '90%', alignSelf: "center", height: screenHeight / 23, backgroundColor: Red1, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20, marginVertical: screenHeight / 15 }}>
                                        <Image source={require('../images/modrek/vedio.png')} style={{ resizeMode: 'contain', width: '15%', height: '100%', alignItems: 'center', tintColor: White, marginHorizontal: 5 }} />
                                        <Text style={{ fontSize: screenWidth / 33, fontFamily: appFontBold, color: White, }}>{strings('lang.Show')}</Text>
                                    </Button>
                                </View>
                            </View>
                        }
                        keyExtractor={item => item.id}
                        style={{ alignSelf: "center", width: screenWidth / 1.1, overflow: "hidden", flexDirection: "column", flexWrap: "wrap", marginVertical: 20 }}
                    // onEndReached={() => this.LoadMore()}
                    // onEndReachedThreshold={0.1}
                    // numColumns={2}
                    />


                    <View style={{ width: '95%', alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '5%' }}>
                        <View style={{ width: '75%', }}>
                            <Text style={styles.textGrey1}>{strings('lang.Totalproductsinclusiveofqqt')}</Text>
                            <Text style={styles.textGrey1}>{strings('lang.Delivery')}</Text>
                            {order.cod ?
                                <Text style={styles.textGrey1}>{strings('lang.CashonDeliveryFee')}</Text>
                                :
                                <></>
                            }
                            {/* <Text style={styles.textBlack}>{strings('lang.Discountvalue')}</Text> */}
                            <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: 5 }}></View>
                            <Text style={styles.textGrey1}>{strings('lang.Finaltotal')}</Text>
                        </View>
                        <View style={{ width: '25%', alignItems: "flex-end" }}>
                            <Text style={styles.textBlack0}> {order.sub_total} {strings('lang.SAR')}</Text>
                            <Text style={styles.textBlack0}> {order.delivery_fees} {strings('lang.SAR')}</Text>
                            {order.cod ?
                                <Text style={styles.textBlack0}> {order.cod} {strings('lang.SAR')}</Text>
                                :
                                <></>
                            }
                            <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: 5 }}></View>
                            <Text style={styles.textRed1}> {order.total}  {strings('lang.SAR')}</Text>
                        </View>
                    </View>

                    <Text style={{ ...styles.textBlack3, ...{ marginVertical: 10 } }}>{strings('lang.Yourpriceowed')}</Text>
                    <View style={{ width: '95%', alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '2%' }}>
                        <View style={{ width: '75%', }}>
                            <Text style={styles.textGrey1}>{strings('lang.Thepriceisinclusiveofqtm')}</Text>
                        </View>
                        <View style={{ width: '25%', alignItems: "flex-end" }}>
                            <Text style={styles.textGreen1}> {order.payment_type == "Full" ? '0' : order.shared_value}  {strings('lang.SAR')}</Text>
                        </View>
                    </View>

                    {item.is_accepted == false
                        ?
                        <Text style={{ fontSize: screenWidth / 24, fontFamily: appFontBold, color: Red1, alignSelf: 'center', marginTop: screenHeight / 50 }}>{strings('lang.Therequesthasbeenrejected')}</Text>
                        :
                        <View style={{ flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse', alignItems: 'flex-end', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, marginVertical: '10%' }}>
                            <Button onPress={() => { submit(1) }} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: Red, alignItems: "center", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                                {/* <Image source={I18nManager.isRTL ? require('../images/Silal/svgexport-66.png') : require('../images/Silal/svgexport-67.png')} style={{ tintColor: Black, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} /> */}
                                <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.Acceptance')}</Text>
                            </Button>
                            <Button onPress={() => { submit(0) }} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: Red1, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10 }}>
                                {/* <Image source={require('../images/modrek/right.png')} style={{ tintColor: White, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} /> */}
                                <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.reject')}</Text>
                            </Button>
                        </View>
                    }
                    {order.sheep ?
                        <LiveModal
                            isLoading={isLoading}
                            sheep={order.sheep} status={{}} link2={order.sheep[0].sheep && order.sheep[0].sheep && order.sheep[0].sheep.farm.camera_url2}
                            link={order.sheep[0].sheep && order.sheep[0].sheep && order.sheep[0].sheep.farm.camera_url} ModalVisible={ModalVisible}
                            modal2Press={() => { setModalVisible(ModalVisible) }}
                            modalPress={() => { setModalVisible(!ModalVisible); setIsLoading(false) }} />
                        :
                        <></>
                    }
                    {/* {order.sheep ?
                        <LiveModal1 sheep={order.sheep} status={{}} link2={order.sheep[0].sheep && order.sheep[0].sheep && order.sheep[0].sheep.farm.camera_url2}
                            link={order.sheep[0].sheep && order.sheep[0].sheep && order.sheep[0].sheep.farm.camera_url} ModalVisible={ModalVisible}
                            modal2Press={() => { setModalVisible1(ModalVisible1) }}
                            modalPress={() => { setModalVisible1(!ModalVisible1) }} />
                        :
                        <></>
                    } */}

                </ScrollView>

                <View style={{ height: screenHeight / 8, }}></View>
                <MyFooter navigation={props.navigation} />
            </View>
        )
    }
}

export default PartnershipOrdersDetails;
const styles = StyleSheet.create({
    textBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start' },
    textBlack0: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-end' },
    textBlack3: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start', marginStart: '2.5%' },
    textBlack1: { fontFamily: appFont, color: Black, fontSize: screenWidth / 24, alignSelf: 'flex-start' },
    textAddress: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26 },
    textRed1: { fontFamily: appFontBold, color: Red, fontSize: screenWidth / 24 },
    textGreen1: { fontFamily: appFontBold, color: 'green', fontSize: screenWidth / 24 },
    textGrey: { fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 26, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey2: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },
    textGrey1: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 },
    textWhite1: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 24 },
    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    squareGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: 2, marginEnd: '3%' },
    circleGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: screenHeight / 100, marginStart: '20%', marginEnd: '3%' },
});
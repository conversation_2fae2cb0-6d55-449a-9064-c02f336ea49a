import React, { Component, useEffect, useState } from 'react';
import { Image, View, Text, StyleSheet, I18nManager, BackHandler, } from "react-native";
import { screenHeight, DarkBlue, screenWidth, DarkGrey, White, appFont, appFontBold, DarkGreen, Red, Green, appColor1, appColor2, Red1, } from "../components/Styles";

import { Input, Container, Toast, Button, } from 'native-base';
import { ScrollView, TouchableOpacity, } from 'react-native-gesture-handler';
import { strings } from './i18n'
import I18n from 'react-native-i18n';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Header from '../components/Header';
import LoadingMore from '../components/LoadingMore';
import { useDispatch } from 'react-redux';
import Toaster from '../components/Toaster';
import * as authActions from '../../Store/Actions/auth';


const ForgetPassword = props => {

    const [phone, setPhone] = useState('')
    const [error_phone, setError_phone] = useState('')
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();


    useEffect(() => {

    }, []);

    const submit = async () => {
        if (
            phone == ''
        ) {
            if (phone == '') {
                setError_phone(strings('lang.Please_insert_phone'))
            }
        }
        else {
            try {
                setLoadingMore(true)
                let ForgotPassword = await dispatch(authActions.ForgotPassword(phone));
                if (ForgotPassword.success == true) {
                    props.navigation.navigate('ConfirmationCode', { phone: phone, forget: true })
                    // props.navigation.navigate('ForgetPasswordChange', { phone: phone, forget: true })
                    setLoadingMore(false);
                } else {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        ForgotPassword.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoadingMore(false);
            }
            catch (err) {
                console.log('err', err)
                setLoadingMore(false);
            }
        }
    }
    return (
        <Container>
            <Header title={strings('lang.Changepassword')} backPress={() => { props.navigation.goBack() }} />
            {loadingMore ? <LoadingMore /> : <></>}

            <KeyboardAwareScrollView>


                <View style={{ width: '60%', height: screenHeight / 4, alignSelf: "center", alignItems: "center", justifyContent: "center", marginBottom: screenHeight / 40 }}>
                    <Image source={require('../images/Silal/lock.png')} style={{ width: '100%', height: '80%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                </View>

                <View style={styles.inputsContainer}>

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.MobileNumber')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={styles.input}
                            value={phone} onChangeText={(text) => { setPhone(text); setError_phone('') }}
                            placeholderTextColor={'#707070'} placeholder={'05XXXXXXXX'}
                        />
                    </View>
                    {error_phone ?
                        <Text style={styles.loginError}>{error_phone}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                </View>


                <Button onPress={() => { submit() }} style={styles.buttonContainer}>
                    <Text style={styles.buttonText}>{strings('lang.Confirm')}</Text>
                </Button>

                <View style={{ height: 20 }}></View>

            </KeyboardAwareScrollView>
        </Container>


    );
}

export default ForgetPassword;
const styles = StyleSheet.create({
    text: {
        fontFamily: appFont,
        fontSize: screenWidth / 26,
        marginStart: '2%'
    },
    input: {
        height: 40, borderWidth: .5, borderRadius: 15, borderColor: DarkGrey, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 28, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    texttitile: {
        marginVertical: 10,
        fontFamily: appFont,
        fontSize: screenWidth / 25,
        textDecorationStyle: 'solid',
        color: appColor1,
        textDecorationLine: "underline"
    },
    inputsContainer: {
        width: '90%', alignSelf: "center", justifyContent: "center",
    },
    star: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 20,
        color: Red1
    },
    labelContainer: { flexDirection: 'row', alignItems: "center", marginBottom: 5 },
    loginError: {
        color: Red1, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '90%',
        height: 40,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '10%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
});

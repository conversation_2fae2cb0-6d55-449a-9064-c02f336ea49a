import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, BackHandler, FlatList, RefreshControl, StyleSheet, Text, View } from "react-native";
import { ScrollView } from 'react-native-gesture-handler';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch, useSelector } from 'react-redux';
import * as ordersActions from '../../Store/Actions/orders';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import OrdersContainer from '../components/OrdersContainer';
import { Black, MediumGrey, Red, Red1, White, WhiteGery, appFontBold, screenHeight, screenWidth } from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';

const MyOrders = props => {

    const [buttonId, setButtonId] = useState('0');
    const [currentOrders, setCurrentOrders] = useState([]);
    const [lastOrders, setLastOrders] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingMore1, setLoadingMore1] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [lastPageNumber, setLastPageNumber] = useState(1);
    const [pageNumber1, setPageNumber1] = useState(1);
    const [lastPageNumber1, setLastPageNumber1] = useState(1);
    const [Reload, setReload] = useState(false);
    const IsFocused = useIsFocused();

    const userId = useSelector(state => state.auth.user.id)

    const dispatch = useDispatch();


    useEffect(() => {
        const getCurrentOrders = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getCurrentOrders(1));
                if (response.success == true) {
                    setCurrentOrders(response.data.items)
                    setPageNumber(1)
                    setLastPageNumber(response.data.last_page)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getFinishedOrders = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getFinishedOrders(1));
                if (response.success == true) {
                    setLastOrders(response.data.items)
                    setPageNumber1(1)
                    setLastPageNumber1(response.data.last_page)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getCurrentOrders();
        getFinishedOrders();
    }, [IsFocused, Reload]);

    const onRefresh = async () => {
        try {
            setLoading(true)
            let response = await dispatch(ordersActions.getCurrentOrders(1));
            if (response.success == true) {
                setCurrentOrders(response.data.items)
                setPageNumber(1)
                setLastPageNumber(response.data.last_page)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
        try {
            setLoading(true)
            let response = await dispatch(ordersActions.getFinishedOrders(1));
            if (response.success == true) {
                setLastOrders(response.data.items)
                setPageNumber1(1)
                setLastPageNumber1(response.data.last_page)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    }

    const LoadMore = async () => {
        console.log('pageNumber', pageNumber);
        console.log('lastPageNumber', lastPageNumber);
        if (pageNumber < lastPageNumber) {
            setLoadingMore(true)
            try {
                let response = await dispatch(ordersActions.getCurrentOrders(pageNumber + 1));

                if (response.success == true) {
                    setCurrentOrders([...currentOrders, ...response.data.items]);
                    setPageNumber(pageNumber + 1);
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false);
                }
            } catch (err) {
                setLoadingMore(false);
            }
        }
        else {
        }
    }

    const LoadMore1 = async () => {
        console.log('pageNumber1', pageNumber1);
        console.log('lastPageNumber1', lastPageNumber1);
        if (pageNumber1 < lastPageNumber1) {
            setLoadingMore1(true)
            try {
                let response = await dispatch(ordersActions.getFinishedOrders(pageNumber1 + 1));

                if (response.success == true) {
                    setLastOrders([...lastOrders, ...response.data.items]);
                    setPageNumber1(pageNumber1 + 1);
                    setLoadingMore1(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore1(false);
                }
            } catch (err) {
                setLoadingMore1(false);
            }
        }
        else {
        }
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.myorders')} backPress={() => {
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '95%', alignSelf: 'center', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 4.8,
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 4.8,
                                    height: screenHeight / 18,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'MyOrders'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.myorders')}
                    backPress={() => {
                        props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                    }}
                />

                <View style={{ alignItems: "center", width: '100%', justifyContent: 'center', alignSelf: "center", flexDirection: 'row' }}>
                    {buttonId == '0'
                        ?
                        <Button style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 33, fontFamily: appFontBold, color: White, }}>{strings('lang.currentOrders')}</Text>
                        </Button>
                        :
                        <Button
                            onPress={() => setButtonId('0')}
                            style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 33, fontFamily: appFontBold, color: Black, }}>{strings('lang.currentOrders')}</Text>
                        </Button>
                    }
                    {/* {buttonId == '1'
                        ?
                        <Button style={{ width: '48%', alignSelf: "center", height: 45, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 33, fontFamily: appFontBold, color: White, }}>{strings('lang.PartnershipApplications')}</Text>
                        </Button>
                        :
                        <Button
                            onPress={() => { setButtonId('1') }}
                            style={{ width: '48%', alignSelf: "center", height: 45, backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Text style={{ fontSize: screenWidth / 33, fontFamily: appFontBold, color: Black, }}>{strings('lang.PartnershipApplications')}</Text>
                        </Button>
                    } */}
                </View>

                {buttonId == '0' && currentOrders.length == 0
                    ?
                    <Text
                        style={{
                            fontSize: screenWidth / 18,
                            fontFamily: appFontBold,
                            color: Red1,
                            marginTop: screenHeight / 20,
                            alignSelf: 'center',
                        }}
                    >
                        {strings('lang.No_Results')}
                    </Text>
                    :
                    <></>
                }
                {buttonId == '0' &&
                    <>
                        <FlatList
                            data={currentOrders}
                            refreshControl={
                                <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                            }
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item, key }) =>
                                <OrdersContainer item={item} TrackingPress={() => { props.navigation.push('OrderTracking', { item: item }) }} />
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", overflow: "hidden", width: '100%', backgroundColor: White, paddingVertical: 5, }}
                            onEndReached={() => LoadMore()}
                            onEndReachedThreshold={0.1}
                        // numColumns={2}

                        />
                        {loadingMore ? <ActivityIndicator size={40} color={Red} /> : <></>}
                    </>

                }

                {buttonId == '1' && lastOrders.length == 0
                    ?
                    <Text
                        style={{
                            fontSize: screenWidth / 18,
                            fontFamily: appFontBold,
                            color: Red1,
                            marginTop: screenHeight / 20,
                            alignSelf: 'center',
                        }}
                    >
                        {strings('lang.No_Results')}
                    </Text>
                    :
                    <></>
                }
                {buttonId == '1' &&
                    <>
                        <FlatList
                            data={lastOrders}
                            refreshControl={
                                <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                            }
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item, key }) =>
                                <OrdersContainer
                                    item={item}
                                    TrackingPress={() => {
                                        console.log(item.user_id);
                                        console.log(userId);
                                        if (item.confirmed_by_partner != true && item.user_id != userId) {
                                            props.navigation.push('PartnershipOrdersDetails', { item: item })
                                        }
                                        else {
                                            props.navigation.push('OrderTracking', { item: item })
                                        }
                                    }}
                                />
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", overflow: "hidden", width: '100%', backgroundColor: White, paddingVertical: 5, }}
                            onEndReached={() => LoadMore1()}
                            onEndReachedThreshold={0.1}
                        // numColumns={2}
                        />
                        {loadingMore1 ? <ActivityIndicator size={40} color={Red} /> : <></>}
                    </>
                }


                <View style={{ height: screenHeight / 9 }}></View>



                <MyFooter current={'MyOrders'} navigation={props.navigation} />
            </View>
        )
    }
}
export default MyOrders;
const styles = StyleSheet.create({
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modal2: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 3.5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 22,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    modalbuttonContainer2: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    textBlackCenter: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 29, },
    textBlackCenter2: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 27, },
});
import React, { useState, useEffect } from 'react';
import { Image, View, Text, StyleSheet, I18nManager, BackHandler, } from "react-native";
import { screenHeight, DarkBlue, screenWidth, DarkGrey, White, appFont, appFontBold, DarkGreen, Red, Green, appColor1, appColor2, <PERSON>1, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>G<PERSON>, } from "../components/Styles";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Input, Container, Toast, Button, } from 'native-base';
import { ScrollView, TouchableOpacity, } from 'react-native-gesture-handler';
import { strings } from './i18n'
import I18n from 'react-native-i18n';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
// import CheckBox from '@react-native-community/checkbox';
import Header from '../components/Header';
import LoadingMore from '../components/LoadingMore';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as authActions from '../../Store/Actions/auth';
import { getUniqueId } from 'react-native-device-info';
import Toaster from '../components/Toaster';

const Regestration = props => {
    const [error_userName, seterror_userName] = useState('')
    const [userName, setuserName] = useState('')
    const [email, setEmail] = useState('')
    const [error_email, seterror_email] = useState('')
    const [phone, setPhone] = useState('')
    const [error_phone, seterror_phone] = useState('')
    const [password, setPassword] = useState('')
    const [error_password, seterror_password] = useState('')
    const [confirmPassword, setconfirmPassword] = useState('')
    const [error_confirmPassword, seterror_confirmPassword] = useState('')
    const [conditions, setconditions] = useState()
    const [activeText, setActiveText] = useState(0);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();


    useEffect(() => {

    }, [])

    async function submit() {
        let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/;
        if (userName == '') {
            seterror_userName(strings('lang.Please_insert_field'))
        }
        // if (email == '') {
        //     setEmail([phone + '@gmail.com'])
        // }
        // } 
        // else if (reg.test(email) === false) {
        //     seterror_email(strings('lang.validEmail'))
        // }
        if (phone == '') {
            seterror_phone(strings('lang.Please_insert_phone'))
        }
        if (password == '') {
            seterror_password(strings('lang.Please_insert_password'))

        } else if (password.length < 6) {
            seterror_password(strings('lang.Password_must_be_more_6_characters'))

        }
        if (confirmPassword == '') {
            seterror_confirmPassword(strings('lang.Please_insert_password'))
        } else if (confirmPassword.length < 6) {
            seterror_confirmPassword(strings('lang.Password_must_be_more_6_characters'))
        } else if (confirmPassword != password) {
            seterror_confirmPassword(strings('lang.Passwordnotright'))
        }
        else {
            setLoading(true)
            let deviceId = await getUniqueId();

            let Register = await dispatch(authActions.Register(deviceId, userName, email, phone, password, confirmPassword));
            console.log('Register', Register);

            if (Register.success == true) {
                Toaster(
                    'top',
                    'success',
                    'green',
                    (strings('lang.Register_Successfuly')),
                    White,
                    1500,
                    screenHeight / 15,
                );
                props.navigation.navigate('ConfirmationCode', { phone: phone, forget: false })
                setLoading(false)
            }
            else {
                setLoading(false)
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    Register.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
        }
    }


    return (
        <Container>
            <Header title={strings('lang.Registerion')} backPress={() => { props.navigation.goBack() }} />
            {loading ? <LoadingMore /> : <></>}

            <KeyboardAwareScrollView>
                <View style={{ width: '60%', height: screenHeight / 6, alignSelf: "center", alignItems: "center", justifyContent: "center" }}>
                    <Image source={require('../images/modrek/loggoo.png')} style={{ width: '100%', height: '60%', resizeMode: "contain", alignSelf: "center" }} />
                </View>
                <View style={styles.inputsContainer}>

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.Username')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 1 ? styles.activeInput : styles.input}
                            value={userName} onChangeText={(text) => {
                                setuserName(text);
                                seterror_userName('')
                            }
                            }
                            onFocus={() => setActiveText(1)}
                            onBlur={() => { setActiveText(0) }}
                        // placeholderTextColor={'#707070'} placeholder={strings('lang.Username')}
                        />
                    </View>
                    {error_userName ?
                        <Text style={styles.loginError}>{error_userName}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.Email')}</Text>
                        <Text numberOfLines={1} style={[styles.textRed, {
                            color: MediumGrey, fontSize: screenWidth / 35, marginStart: 5
                        }]}>{[`(${strings('lang.Mychoice')})`]}</Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 2 ? styles.activeInput : styles.input}
                            value={email} onChangeText={(text) => {
                                setEmail(text);
                                seterror_email('')
                            }
                            }
                            onFocus={() => setActiveText(2)}
                            onBlur={() => { setActiveText(0) }}
                        // placeholderTextColor={'#707070'} placeholder={strings('lang.Email')}
                        />
                    </View>
                    {/* {error_email ?
                            <Text style={styles.loginError}>{error_email}</Text>
                            :
                            <View style={{ height: 0 }}></View>
                        } */}

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.MobileNumber')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 3 ? styles.activeInput : styles.input}
                            blurOnSubmit autoCapitalize='none' autoCorrect={false}
                            value={phone} onChangeText={(text) => {

                                setPhone(text);
                                seterror_phone('')
                            }
                            }
                            onFocus={() => setActiveText(3)}
                            onBlur={() => { setActiveText(0) }}
                            // placeholderTextColor={'#707070'} placeholder={strings('lang.MobileNumber')}
                            keyboardType={'number-pad'}
                        />
                    </View>
                    {error_phone ?
                        <Text style={styles.loginError}>{error_phone}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.Password')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 4 ? styles.activeInput : styles.input}
                            secureTextEntry={true} value={password} onChangeText={(text) => {
                                setPassword(text);
                                seterror_password('')
                            }
                            }
                            onFocus={() => setActiveText(4)}
                            onBlur={() => { setActiveText(0) }}
                        // placeholderTextColor={'#707070'} placeholder={strings('lang.Password')}
                        />
                    </View>
                    {error_password ?
                        <Text style={styles.loginError}>{error_password}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.Confirmpassword')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 5 ? styles.activeInput : styles.input}
                            secureTextEntry={true}
                            value={confirmPassword} onChangeText={(text) => {
                                setconfirmPassword(text);
                                seterror_confirmPassword('')
                            }}
                            onFocus={() => setActiveText(5)}
                            onBlur={() => { setActiveText(0) }}
                        // placeholderTextColor={'#707070'} placeholder={strings('lang.Confirmpassword')}
                        />
                    </View>
                    {error_confirmPassword ?
                        <Text style={styles.loginError}>{error_confirmPassword}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }
                </View>

                {/* <View style={{ flexDirection: 'row', width: '90%', alignSelf: "center", alignItems: 'center', paddingStart: '3%', marginTop: 10 }}>
                    <CheckBox
                        style={{ width: 20, height: 20, alignSelf: "center", marginEnd: 7 }}
                        tintColors={{ true: appColor1, false: '#535353', width: 15, height: 10, fontSize: 12 }}
                        disabled={false}
                        value={conditions}
                        onValueChange={(newValue) => setconditions(newValue)}
                    />
                    <TouchableOpacity style={{ marginStart: 7 }} onPress={() => { props.navigation.navigate('TermsAndConditions') }} >
                        <Text style={styles.texttitile}>{strings('lang.Iagreetothetermsandconditions')}</Text>
                    </TouchableOpacity>
                </View> */}

                <Button onPress={() => { submit() }} style={styles.buttonContainer}>
                    <Text style={styles.buttonText}>{strings('lang.Register')}</Text>
                </Button>

                <View style={{ height: 20 }}></View>

            </KeyboardAwareScrollView>
        </Container>


    );
}

export default Regestration;
const styles = StyleSheet.create({
    text: {
        fontFamily: appFont,
        fontSize: screenWidth / 30,
        marginStart: '2%',
        color: Black
    },
    input: {
        height: 45, borderWidth: 1, borderRadius: 20, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 30, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    activeInput: {
        height: 45, borderWidth: 1, borderRadius: 20, borderColor: Red, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 30, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    texttitile: {
        marginVertical: 10,
        fontFamily: appFont,
        fontSize: screenWidth / 25,
        textDecorationStyle: 'solid',
        color: appColor1,
        textDecorationLine: "underline"
    },
    inputsContainer: {
        width: '90%', alignSelf: "center", justifyContent: "center",
    },
    star: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 20,
        color: Red1
    },
    labelContainer: { flexDirection: 'row', alignItems: "center", marginBottom: 0 },
    loginError: {
        color: Red1, fontFamily: appFont, alignSelf: 'flex-end', fontSize: screenWidth / 33
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '90%',
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '15%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
});
import React, { useState, useRef, useCallback } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Pressable, Modal, BackHandler } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor2, appColor1, Black, Red, Red1, WhiteBlue, DarkBlue, MediumBlue } from '../components/Styles';
import { Button, Input } from 'native-base';
import { strings } from './i18n';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import LinearGradient from 'react-native-linear-gradient';
import Carousel, { Pagination, ParallaxImage } from 'react-native-snap-carousel';
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as cartActions from '../../Store/Actions/cart';
import Toaster from '../components/Toaster';
import { useFocusEffect } from '@react-navigation/native';
import {
    CopilotProvider,
    CopilotStep,
    copilot,
    walkthroughable,
} from "react-native-copilot";
import MyComponent from '../components/MyComponent';
import AsyncStorage from '@react-native-async-storage/async-storage';
import RNRestart from 'react-native-restart';
import BackgroundTimer from 'react-native-background-timer';
import appsFlyer from 'react-native-appsflyer';

const Cart = props => {

    const [entries, setEntries] = useState(['https://hatrabbits.com/wp-content/uploads/2017/01/random.jpg', 'https://helpx.adobe.com/content/dam/help/en/photoshop/using/convert-color-image-black-white/jcr_content/main-pars/before_and_after/image-before/Landscape-Color.jpg', 'https://www.pixsy.com/wp-content/uploads/2021/04/ben-sweet-2LowviVHZ-E-unsplash-1.jpeg']);
    const [DATA, setDATA] = useState({});
    const [farm, setFarm] = useState({});
    const [buttonId, setButtonId] = useState('0');
    const [selectedSheeps, setSelectedSheeps] = useState([]);
    const [selectedSheepsIds, setSelectedSheepsIds] = useState([]);
    const [time, setTime] = useState({});
    const [seconds, setSeconds] = useState(60);
    const [disable, setDisable] = useState(false);
    const refRbSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const minss = useSelector(state => state.cart.minsCart)
    const secss = useSelector(state => state.cart.secsCart)
    const cartt = useSelector(state => state.cart.cart)
    const [cart, setCart] = useState(cartt);
    const [cartItems, setCartItems] = useState(cartt.sheep);
    const [phone, setPhone] = useState('');
    const [error_phone, setError_phone] = useState('');
    const [ModalVisible, setModalVisible] = useState(false);
    const [mins, setMins] = useState(minss)
    const [secs, setSecs] = useState(secss)
    const dispatch = useDispatch();
    const intervalRef = useRef(null); // Store interval ID in a ref


    const [timeLeft, setTimeLeft] = useState(1 * 60); // 10 minutes in seconds
    const timerRef = useRef(null);
    const [startTime, setStartTime] = useState(null);

    // // Start Timer Function
    // const startTimer = () => {
    //     if (timerRef.current) return; // Avoid starting multiple timers
    //     timerRef.current = setInterval(() => {
    //         if (cartItems.length == 0) {
    //             dispatch(cartActions.clearCart())
    //             props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
    //             // RNRestart.Restart();
    //         }
    //         setTimeLeft((prev) => {
    //             if (prev <= 1) {
    //                 clearInterval(timerRef.current);
    //                 timerRef.current = null;
    //                 handleTimerEnd()
    //                 props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
    //                 return 0;
    //             }
    //             return prev - 1;
    //         });
    //     }, 1000);
    // };
    // const handleTimerEnd = () => {
    //     Toaster(
    //         'top',
    //         'danger',
    //         Red1,
    //         strings('lang.message13'),
    //         White,
    //         4500,
    //         screenHeight / 15,
    //     );
    //     dispatch(cartActions.clearCart())
    // };
    // // Stop Timer Function
    // const stopTimer = () => {
    //     if (timerRef.current) {
    //         clearInterval(timerRef.current);
    //         timerRef.current = null;
    //     }
    // };

    // // Reset Timer to 10 minutes
    // const resetTimer = () => {
    //     setTimeLeft(1 * 60);
    // };

    // // Start timer when screen is focused & Reset when leaving
    // useFocusEffect(
    //     useCallback(() => {
    //         resetTimer(); // Reset every time you open the screen
    //         startTimer();

    //         // Cleanup when screen is unfocused (leaving screen)
    //         return () => {
    //             stopTimer();
    //             resetTimer(); // Reset when leaving the screen
    //         };
    //     }, [])
    // );

    // // Format time as mm:ss
    // const formatTime = (seconds) => {
    //     const minutes = Math.floor(seconds / 60);
    //     const remainingSeconds = seconds % 60;
    //     return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    // };

    useEffect(() => {
        // setTimeout(() => {
        console.log('cartItems', cartItems);
        console.log('cart', cart);
        for (let item of cartItems) {
            console.log('item', item);
            const logAddToCartEvent = () => {
                const eventName = 'af_add_to_cart';
                const eventValues = {
                    af_content_id: cart.id.toString(),
                    // af_content_type: 'product',
                    af_currency: 'SAR',
                    af_price: item.sheep.price,
                };

                appsFlyer.logEvent(eventName, eventValues,
                    (res) => {
                        console.log('Event sent successfully:', res);
                    },
                    (err) => {
                        console.error('Event failed to send:', err);
                    }
                );
            };
            logAddToCartEvent()
        }
        // }, 100);



    }, []);


    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };

    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };

    const resetTimer = () => {
        setTimeLeft(1 * 60);
    };

    useFocusEffect(
        useCallback(() => {
            resetTimer();
            startTimer();

            return () => {
                stopTimer();
                resetTimer();
            };
        }, [])
    );

    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart());
    };

    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    const removeItem = async (id) => {
        setLoadingMore(true)
        try {
            let response = await dispatch(cartActions.removeItem(id));
            if (response.success == true) {
                setCart(response.data)
                setCartItems(response.data.sheep)
                if (response.data.sheep.length == 0) {
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                }
            }
            else {
            }
            setLoadingMore(false)
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false)
        }
    }

    const clearCart = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(cartActions.clearCart());
            if (response.success == true) {
                setModalVisible(!ModalVisible)
                back();
            }
            else {
            }
            setLoadingMore(false)
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false)
        }
    }

    // const performTaskAndStopTimer = () => {
    //     if (intervalRef.current) {
    //         clearInterval(intervalRef.current); // Stop the interval
    //         intervalRef.current = null; // Reset the interval reference
    //         console.log('Interval stopped automatically.');
    //     }
    // };
    const nav = async () => {
        setLoadingMore(true)
        let response = await dispatch(cartActions.clearPaymentType());
        if (response.success == true) {
            props.navigation.push('CompleteOrder0', { shared: false, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: 10 * 60 })
            setLoadingMore(false)
        }
        else {
            setLoadingMore(false)
            Toaster(
                'top',
                'danger',
                Red1,
                response.message,
                White,
                3000,
                screenHeight / 15,
            );
        }
    }

    const navParthner = async () => {

        if (phone.length == 0) {
            refRbSheet.current.close();
            Toaster(
                'top',
                'danger',
                Red1,
                strings('lang.Please_insert_field'),
                White,
                3000,
                screenHeight / 15,
            );
        }
        else {
            setLoadingMore(true)
            let response = await dispatch(cartActions.selectPaymentType(buttonId, phone));
            if (response.success == true) {
                setLoadingMore(false)
                refRbSheet.current.close();
                props.navigation.push('CompleteOrder0', { shared: false, order: {}, sheepTypeId: props.route.params.sheepTypeId, timeLeft: 10 * 60 })
            }
            else {
                setLoadingMore(false)
                refRbSheet.current.close();
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    response.message,
                    White,
                    3000,
                    screenHeight / 15,
                );
            }
        }

        // props.navigation.navigate('CompleteOrder0')
    }

    const back = async () => {
        setLoadingMore(true)

        await dispatch(cartActions.clearCart())
        dispatch(cartActions.timerCart(1, 0));
        // props.navigation.navigate('Home');
        clearInterval(intervalRef.current);
        props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });

        setLoadingMore(false)
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                if (cartItems.length > 0) {
                    setModalVisible(!ModalVisible)
                }
                else {
                    props.navigation.navigate('Home', { weightId: null, sheepCatId: null, link: '' });
                }
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    const CopilotText = walkthroughable(Text);
    const CopilotButton = walkthroughable(Button);
    const CopilotScroll = walkthroughable(ScrollView);
    const CopilotView = walkthroughable(View);

    const [showCopilot, setShowCopilot] = useState(false);

    useEffect(() => {
        const checkIfFirstLaunch = async () => {
            try {
                const hasLaunched2 = await AsyncStorage.getItem('hasLaunched2');
                if (!hasLaunched2) {
                    setShowCopilot(true);
                    await AsyncStorage.setItem('hasLaunched2', 'true');
                }
            } catch (error) {
                console.error('Failed to check first launch status:', error);
            }
        };

        checkIfFirstLaunch();
    }, []);

    useEffect(() => {
        if (showCopilot) {
            props.copilotEvents.on();
            props.start();
        }
    }, [showCopilot]);

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header title={''} backPress={() => { if (cartItems.length > 0) { setModalVisible(!ModalVisible) } else { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }); } }} />


                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 100 }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 11,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginBottom: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 11,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '95%', justifyContent: 'space-between', alignSelf: 'center' }}>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: 150,
                                    height: screenHeight / 18,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: 150,
                                    height: screenHeight / 15,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,

                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                {/* <MyFooter current={'Home'} navigation={props.navigation} /> */}
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header title={strings('lang.Cart')}
                    backPress={() => { back() }} />

                {loadingMore
                    ?
                    <LoadingMore />
                    :
                    <></>
                }

                {/* <View style={{ alignSelf: "center", justifyContent: "center", height: screenHeight / 4, marginTop: 0, width: '100%' }}>
                    <Button onPress={() => { }} style={{ width: screenWidth / 5, alignSelf: "center", height: 30, backgroundColor: Red, borderColor: Red, borderWidth: 1, alignItems: "center", justifyContent: "center", borderRadius: 3, position: 'absolute', end: '2%', top: '5%' }}>
                        <Image source={require('../images/modrek/vedio.png')} style={{ resizeMode: 'contain', width: '15%', height: '50%', tintColor: White, marginHorizontal: '5%' }} />
                        <Text style={{ fontSize: screenWidth / 45, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text>
                    </Button>

                    <View style={{ flexDirection: 'row', height: screenHeight / 20, backgroundColor: Black, width: screenWidth, position: 'absolute', bottom: '0%', opacity: .5, alignSelf: 'center', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 25, marginStart: '5%' }}>assa</Text>
                        <View style={{ height: '100%', width: '10%' }}>
                            <TouchableOpacity onPress={() => { props.navigation.navigate('Live') }} style={{ width: '50%', height: '100%', alignItems: 'center', justifyContent: 'center', alignSelf: 'center', }}>
                                <Image source={require('../images/modrek/zoom.png')} style={{ resizeMode: 'contain', width: '100%', height: '100%', tintColor: White, }} />
                            </TouchableOpacity>
                        </View>
                    </View>
                </View> */}
                {cartItems && cartItems.length > 0
                    ?
                    <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 3, borderColor: WhiteGery, backgroundColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 20, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginTop: screenHeight / 50, marginBottom: 10 }}>
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 35, }}>{strings('lang.messageFinshTime2')} </Text>
                        <Image source={require('../images/newIcon/watch.png')} style={{ resizeMode: 'contain', width: '10%', height: '30%', tintColor: Red1 }} />
                        {/* <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{mins}:{secs < 10 && 0}{secs}</Text> */}
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{formatTime(timeLeft)}</Text>
                    </View>
                    :
                    <></>
                }

                {cartItems && cartItems.length == 0
                    ?
                    <View style={{ width: screenWidth / 1.1, height: screenHeight / 1.3, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginBottom: '4%', marginTop: '4%' }}>
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 20, }}>{strings('lang.No_Results')}</Text>
                    </View>
                    :
                    <></>
                }


                <FlatList
                    data={cartItems}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, key }) =>
                        <View style={{ flexDirection: 'row', width: screenWidth / 1.1, paddingVertical: 5, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between', }}>
                            <View style={{ width: screenWidth / 8, height: screenWidth / 8, backgroundColor: item.sheep && item.sheep.collar_color, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60 }}>
                                <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>{item.sheep && item.sheep.id}</Text>
                            </View>
                            <View style={{ width: '65%', flexDirection: 'column', paddingStart: '3%', alignItems: 'flex-start' }}>
                                {item.sheep.sharing_type
                                    ?
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, textAlign: 'left' }}>{`${item.sheep.sheep_category.name} / ${item.sheep.sharing_type.count == 4 ?
                                        (item.sheep.farm.weight.from / 4) :
                                        item.sheep.sharing_type.count == 2 ?
                                            (item.sheep.farm.weight.from / 2) :
                                            item.sheep.farm.weight.from
                                        }كيلو - ${item.sheep.sharing_type.count == 4 ?
                                            (item.sheep.farm.weight.to / 4) :
                                            item.sheep.sharing_type.count == 2 ?
                                                (item.sheep.farm.weight.to / 2) :
                                                item.sheep.farm.weight.to
                                        }كيلو / ${item.sheep.farm.code} `}</Text>
                                    :
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, textAlign: 'left' }}>{`${item.sheep.sheep_category.name} / ${item.sheep.farm.weight.from
                                        }كيلو - ${item.sheep.farm.weight.to
                                        }كيلو / ${item.sheep.farm.code} `}</Text>
                                }

                                {/* <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{`${item.sheep.farm.code} / ${item.sheep.sheep_category.name} / ${item.sheep.farm.weight.from} - ${item.sheep.farm.weight.to}`}</Text> */}
                                <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '5%' }}>{item.sheep.price} {strings('lang.SR')}</Text>
                            </View>
                            <View style={{ width: '20%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'space-between', }}>
                                <View style={{ width: '100%', alignItems: 'center', justifyContent: 'center', }}>
                                    <TouchableOpacity onPress={() => { removeItem(item.sheep.id) }} style={{ width: 50 }} >
                                        <Image source={require('../images/modrek/x.png')} style={{ width: '25%', resizeMode: 'contain', alignSelf: 'center', tintColor: Red1 }} />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        </View>
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", width: screenWidth / 1.1, overflow: "hidden", flexDirection: "column", marginTop: 10, flexWrap: "wrap", }}
                // onEndReached={() => this.LoadMore()}
                // onEndReachedThreshold={0.1}
                // numColumns={2}
                />
                {cartItems && cartItems.length == 0
                    ?
                    <></>
                    :
                    <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 3, borderColor: WhiteGery, backgroundColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', marginBottom: '4%', marginTop: '2%' }}>
                        <Text style={{ color: Black, fontFamily: appFont, fontSize: screenWidth / 26, marginStart: '2%' }}>{strings('lang.Subtotal')}</Text>
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 22, marginEnd: '2%' }}>{cart.sub_total} {strings('lang.SR')}</Text>
                    </View>
                }
                {cartItems && cartItems.length == 0
                    ?
                    <></>
                    :
                    <>
                        {/* <View 
                        style={{ alignItems: 'flex-end', backgroundColor: Black, justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, }}> */}
                        <CopilotStep
                            text={strings('lang.step9')}
                            order={1}
                            name="firstStep">
                            <CopilotView
                                style={{
                                    alignItems: 'center',
                                    justifyContent: "center", width: screenWidth / 1.1,
                                    alignSelf: 'center', height: screenHeight / 19,
                                }}>
                                <Button onPress={() => { nav(); }} style={{ width: '100%', alignSelf: "center", height: '90%', backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10 }}>
                                    <Image source={require('../images/modrek/arrowLeft.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: White, resizeMode: 'contain', width: '15%', height: '65%', alignItems: 'center' }} />
                                    <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.reservationconfirmation')}</Text>
                                </Button>
                            </CopilotView>
                        </CopilotStep>
                        {/* </View> */}
                        {/* <View
                            style={{ alignItems: 'flex-end', marginBottom: '2%', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, }}>
                            <Button onPress={() => { refRbSheet.current.open() }} style={{ width: '100%', alignSelf: "center", height: '90%', backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10, marginTop: '3%' }}>
                                <Image source={require('../images/modrek/arrowLeft.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: White, resizeMode: 'contain', width: '15%', height: '65%', alignItems: 'center' }} />
                                <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.Confirmreservationofhalfacarcass')}</Text>
                            </Button>
                        </View>
                        <View
                            style={{ alignItems: 'flex-end', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, }}>
                            <Button onPress={() => { refRbSheet.current.open() }} style={{ width: '100%', alignSelf: "center", height: '90%', backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10, marginTop: '3%' }}>
                                <Image source={require('../images/modrek/arrowLeft.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: White, resizeMode: 'contain', width: '15%', height: '65%', alignItems: 'center' }} />
                                <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.Confirmationofreservationofaquarterofaslaughteredanimal')}</Text>
                            </Button>
                        </View> */}
                    </>

                }

                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: 'center',
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {},
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey,
                        },
                    }}
                    // closeOnDragDown={true}
                    closeOnPressMask={false}
                >
                    {loadingMore
                        ?
                        <LoadingMore />
                        :
                        <></>
                    }
                    <View style={{ flexDirection: 'row', height: '10%', width: '90%', alignItems: 'center', justifyContent: 'space-between', marginTop: 10 }}>
                        <Text style={{ fontSize: screenWidth / 30, fontFamily: appFontBold }}> {strings('lang.Determinetheaskingprice')}</Text>
                        <Pressable onPress={() => refRbSheet.current.close()} style={{ width: screenWidth / 12, height: screenWidth / 12, alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/modrek/x.png')} style={{ width: '40%', height: '40%', resizeMode: 'contain', }} />
                        </Pressable>
                    </View>
                    {/* <Text style={styles.rbsheetText}>{strings('lang.')}</Text> */}
                    <View style={{ height: screenHeight / 9, flexDirection: 'row', width: '90%', justifyContent: 'space-between', alignItems: 'center', marginVertical: 8 }}>
                        {buttonId == '1'
                            ?
                            <Pressable style={styles.activeButton}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black }}>{strings('lang.fullpayment')}</Text>
                            </Pressable>
                            :
                            <Pressable style={styles.button} onPress={() => setButtonId('1')}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black }}>{strings('lang.fullpayment')}</Text>
                            </Pressable>
                        }
                        {buttonId == '2'
                            ?
                            <Pressable style={styles.activeButton}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black }}>{strings('lang.Splitthepriceofthecarcass')}</Text>
                            </Pressable>
                            :
                            <Pressable style={styles.button} onPress={() => setButtonId('2')}>
                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black }}>{strings('lang.Splitthepriceofthecarcass')}</Text>
                            </Pressable>
                        }
                    </View>

                    {/* {buttonId == '2'
                        ?
                        <> */}
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, alignSelf: 'flex-start', marginStart: '5%', marginBottom: 5 }}>{strings('lang.MobileNumber')}</Text>
                    <View style={styles.input} >
                        <Input value={phone} onChangeText={(text) => { setPhone(text); setError_phone('') }}
                            // placeholderTextColor={'#707070'} placeholder={strings('lang.MobileNumber')}
                            style={styles.inputText}
                        />
                    </View>
                    {error_phone ?
                        <Text style={styles.loginError}>{error_phone}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }
                    {/* </>
                        :
                        <></>
                    } */}
                    <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: Red1, marginTop: screenHeight / 80 }}>{strings('lang.message20')}</Text>

                    <Button disabled={buttonId == '0' ? true : false} onPress={() => { navParthner() }} style={buttonId == '0' ? styles.disapleConfirm : styles.activeConfirm}>
                        <Text style={styles.buttonText}>{strings('lang.sendthelink')} </Text>
                    </Button>
                </RBSheet>

                <Modal
                    transparent={true}
                    animationType="fade"
                    visible={ModalVisible}
                >

                    <Button transparent onPress={() => setModalVisible(!ModalVisible)} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                    </Button>

                    <View style={styles.modal}>
                        <Text style={styles.modaltext}>{strings('lang.message17')}</Text>

                        <View style={{ flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                            <Button style={styles.modalbuttonContainer} onPress={() => { setModalVisible(!ModalVisible) }}>
                                <Text style={styles.modalbuttonText}>{strings('lang.No')}</Text>
                            </Button>
                            <Button style={styles.modalbuttonContainer2} onPress={() => { clearCart(); }}>
                                <Text style={styles.modalbuttonText2}>{strings('lang.Yes')}</Text>
                            </Button>
                        </View>

                    </View>
                </Modal>

                <View style={{ height: screenHeight / 8 }}></View>
                {/* <MyFooter current={'Cart'} navigation={props.navigation} /> */}
            </View >
        )
    }
}


export default copilot({
    tooltipComponent: MyComponent, // Set the custom tooltip component
    overlay: 'view',
    animated: true,
    // backdropColor: Black,
    androidStatusBarVisible: true,
    stopOnOutsideClick: false,
    // verticalOffset: 20
})(Cart);
const styles = StyleSheet.create({
    activeButton: { height: 70, width: screenWidth / 2.4, backgroundColor: MediumBlue, borderWidth: 1, borderColor: Blue, borderRadius: 20, alignItems: 'center', justifyContent: 'center' },
    button: { height: 70, width: screenWidth / 2.4, backgroundColor: MediumGrey, borderRadius: 20, alignItems: 'center', justifyContent: 'center', },
    rbsheetText: { fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black, marginVertical: '5%' },
    textBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, textDecorationLine: 'line-through', textDecorationStyle: 'dashed', textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },

    priceGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 28 },
    priceGreenBig: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 24 },
    priceBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28 },
    priceBlackBig: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 24 },

    activeConfirm: {
        backgroundColor: Red,
        width: screenWidth / 1.4,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 20,
        marginTop: '10%'
    },
    disapleConfirm: {
        backgroundColor: Red,
        width: screenWidth / 1.4,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 20,
        marginTop: '10%',
        opacity: .5
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },

    imageContainer: {
        flex: 1,
        marginBottom: Platform.select({ ios: 0, android: 1 }), // Prevent a random Android rendering issue
        backgroundColor: White,
        borderRadius: 0,
        justifyContent: "center",
        borderWidth: .8,
        borderColor: MediumGrey
        // overflow:'hidden'
    },
    image: {
        // ...StyleSheet.absoluteFillObject,
        resizeMode: "cover",
        height: '100%',
        width: '100%',
        alignSelf: "center",
    },
    input: {
        height: 45, borderWidth: 1, borderRadius: 20, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "90%", marginBottom: 10,
    },
    inputText: {
        textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFontBold,
        fontSize: screenWidth / 30, alignSelf: 'center'
    },
    loginError: {
        color: 'red', fontFamily: appFont, alignSelf: 'flex-end', fontSize: screenWidth / 35, marginHorizontal: '5%'
    },
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 5,
        position: 'absolute',
        top: screenHeight / 2.5,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 22,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    modalbuttonContainer2: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
});
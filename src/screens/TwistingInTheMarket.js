import React, { useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Platform, Linking, BackHandler, Pressable, RefreshControl, ActivityIndicator, Keyboard, Touchable } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, Black, appColor1, appColor2, Red, DarkYellow, Red1, LightGreen, DarkBlue } from '../components/Styles';
import { FlatList, ScrollView, TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import MyFooter from '../components/MyFooter';
import HomeHeader from '../components/HomeHeader';
import { strings } from './i18n';
import FarmContainer1 from '../components/FarmContainer1';
import { Button, Input } from 'native-base';
import StarRating from 'react-native-star-rating';
import RBSheet from 'react-native-raw-bottom-sheet';
import * as farmesActions from '../../Store/Actions/farms';
import * as authActions from '../../Store/Actions/auth';
import { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import Toaster from '../components/Toaster';
import * as cartActions from '../../Store/Actions/cart';
import * as ordersActions from '../../Store/Actions/orders';
import * as addressesActions from '../../Store/Actions/addresses';
import * as settingsActions from '../../Store/Actions/settings';
import ImageViewer from 'react-native-image-zoom-viewer';
import Header from '../components/Header';
import WebView from 'react-native-webview';
import { VLCPlayer } from 'react-native-vlc-media-player';
import CircularProgress3 from '../components/CircularProgress3';


const TwistingInTheMarket = props => {
    const [checked, setChecked] = useState(false);
    const [search, setSearch] = useState('');
    const [farmId, setFarmId] = useState(props.route.params.farmId ? props.route.params.farmId : 0);
    const [weightRangeId, setWeightRangeId] = useState(null)
    const [weightRange, setWeightRange] = useState([]);
    const [sheepcategories, setSheepcategories] = useState([]);
    const [sheepcategoriesId, setSheepcategoriesId] = useState(0);
    const [page, setPage] = useState(1);
    const [has_more_pages, setHas_more_pages] = useState(false);
    const [farms, setFarms] = useState([]);
    const [item, setItem] = useState({});
    const [sheep, setSheep] = useState({});
    const [market_avg_price, setMarket_avg_price] = useState('');
    const [whatsapp, setWhatsapp] = useState('');
    const [noResult, setNoResult] = useState('');
    const [pageNumber, setPageNumber] = useState(1);
    const [lastPageNumber, setLastPageNumber] = useState(1);
    const [Reload, setReload] = useState(false);
    const [loadingMoreFarms, setLoadingMoreFarms] = useState(false)
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [noResault, setNoResault] = useState('');
    const refRbSheet = useRef();
    const refRbSheet1 = useRef();
    const [iddd, setIddd] = useState(0);
    const [viewId, setViewId] = useState(0);
    const minss = useSelector(state => state.cart.mins)
    const secss = useSelector(state => state.cart.secs)
    const cartt = useSelector(state => state.cart.cart)
    const [cart, setCart] = useState(cartt);
    const [cartItems, setCartItems] = useState(cartt.sheep ? cartt.sheep : []);
    const [mins, setMins] = useState(minss)
    const [secs, setSecs] = useState(secss)
    const dispatch = useDispatch();
    const IsFocused = useIsFocused();
    const [entries, setEntries] = useState([
        {
            url: 'https://iiif.wellcomecollection.org/image/A0000824/full/full/0/default.jpg'
        },
        {
            url: 'https://previews.123rf.com/images/hjginter/hjginter1601/hjginter160100070/51356039-mouth-and-teeth-of-a-sheep.jpg'
        },
    ]);
    const [imageUrl, setImageUrl] = useState('https://iiif.wellcomecollection.org/image/A0000824/full/full/0/default.jpg');
    const [image, setImage] = useState('');
    const [link, setLink] = useState(props.route.params.link)
    const [sheepImages, setSheepImages] = useState([]);


    // const timer = useCallback(() => {
    //     const timerId = setInterval(async () => {
    //         setCart(cartt)
    //         setCartItems(cartt.sheep)
    //         if (cartItems.length == 0) {
    //             setMins(10)
    //             setSecs(0)
    //             dispatch(cartActions.timer(9, 59));
    //         }
    //         else {
    //             console.log('secs', secs);
    //             if (secs <= 0) {
    //                 console.log('mins', mins);
    //                 if (mins <= 0) {
    //                     Toaster(
    //                         'top',
    //                         'danger',
    //                         Red1,
    //                         strings('lang.message13'),
    //                         White,
    //                         4500,
    //                         screenHeight / 15,
    //                     );
    //                     clearInterval(timerId);
    //                     dispatch(cartActions.timer(9, 59));
    //                     dispatch(cartActions.clearCart())
    //                     props.navigation.navigate('Home', { weightId: null, sheepCatId: null });
    //                 }
    //                 else {
    //                     // setMins(m => m - 1)
    //                     setSecs(59)

    //                     dispatch(cartActions.timer(mins - 1, 59));
    //                 }
    //             }
    //             else {
    //                 setSecs(s => s - 1)
    //                 dispatch(cartActions.timer(mins, secs - 1));
    //             }
    //         }

    //         // if (secs <= 0) {
    //         //     console.log('secs', secs);
    //         //     if (mins <= 0) {
    //         //         console.log('mins', mins);
    //         //         Toaster(
    //         //             'top',
    //         //             'danger',
    //         //             Red1,
    //         //             strings('lang.message13'),
    //         //             White,
    //         //             4500,
    //         //             screenHeight / 15,
    //         //         );
    //         //         clearInterval(timerId);
    //         //         dispatch(cartActions.timer(10, 0));
    //         //         dispatch(cartActions.clearCart())
    //         //         props.navigation.navigate('Home', { weightId: null, sheepCatId: null });
    //         //     }
    //         //     else {
    //         //         console.log('minss', mins);

    //         //         setMins(m => m - 1)
    //         //         setSecs(59)
    //         //         dispatch(cartActions.timer(mins - 1, 59));
    //         //     }
    //         // }
    //         // else {
    //         //     console.log('secss', secs);
    //         //     setSecs(s => s - 1)
    //         //     dispatch(cartActions.timer(mins, secs - 1));
    //         // }
    //     }, 1000)
    //     return () => clearInterval(timerId);
    // }, [mins, secs])


    useEffect(() => {
        // timer();
        console.log('props.route.params.item', props.route.params.item);
        const getSheepCategories = async () => {
            setLoading(true)
            let response = await dispatch(farmesActions.getSheepCategories());
            if (response.success == true) {
                setSheepcategories(response.data)
                getSheepWeights(props.route.params.sheepCategory)
                setSheepcategoriesId(props.route.params.sheepCategory)
                setLoading(true)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        };

        const getSheepWeights = async (sheepcategoriesId) => {
            setLoading(true)
            let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
            if (response1.success == true) {
                setWeightRange(response1.data)
                setWeightRangeId(props.route.params.weightId)
                getFarms(sheepcategoriesId, props.route.params.weightId);
            }
            else {
                if (response1.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response1.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        };


        const getFarms = async (sheepcategoriesId, weightRangeId) => {
            try {
                setLoading(true)
                let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, 1, search));
                if (response.success == true) {
                    if (response.data.items.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setFarms([]);
                        setMarket_avg_price(0);
                        setLastPageNumber(1)
                    }
                    else {
                        setMarket_avg_price(response.data.market_avg_price)
                        // setWhatsapp(response.data.whatsapp)
                        setFarms(response.data.items)
                        setLastPageNumber(response.data.last_page)
                        setNoResault('');
                    }
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        const getCart = async () => {
            try {
                setLoading(true)
                let response = await dispatch(cartActions.getCart());
                if (response.success == true) {
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };


        if (cartItems.length != 0) {
            setMins(props.route.params.min)
            setSecs(props.route.params.sec)
            dispatch(cartActions.timer(props.route.params.min, props.route.params.sec));
            // timer();
        }

        getSheepCategories();
        getSheepWeights();
        getFarms();
        getCart();

    }, []);

    // useEffect(() => {
    //     // console.log('cartt', cartt);
    //     setCart(cartt)
    //     setCartItems(cartt.sheep)
    //     const timerId = setInterval(async () => {
    //         if (cartt.sheep.length == 0) {
    //             setMins(10)
    //             setSecs(0)
    //             dispatch(cartActions.timer(10, 0));
    //         }
    //         else {
    //             if (secs <= 0) {
    //                 if (mins <= 0) {
    //                     if (props.route.params.reservation == true) {
    //                         Toaster(
    //                             'top',
    //                             'danger',
    //                             Red1,
    //                             strings('lang.message13'),
    //                             White,
    //                             4500,
    //                             screenHeight / 15,
    //                         );
    //                         clearInterval(timerId);
    //                         dispatch(cartActions.timer(10, 0));
    //                         dispatch(cartActions.clearCart())
    //                         props.navigation.navigate('Home', { weightId: null, sheepCatId: null });
    //                     }

    //                 }
    //                 else {
    //                     setMins(m => m - 1)
    //                     setSecs(59)

    //                     dispatch(cartActions.timer(mins - 1, 59));
    //                 }
    //             }
    //             else {
    //                 setSecs(s => s - 1)

    //                 dispatch(cartActions.timer(mins, secs - 1));
    //             }
    //         }
    //     }, 1000)
    //     return () => clearInterval(timerId);
    // }, [secs, mins]);

    // const getFarmByCategoryId = async (id, page) => {
    //     try {
    //         setLoadingMore(true)
    //         let response = await dispatch(farmesActions.getFarms(id, weightRangeId, page, search));
    //         if (response.success == true) {
    //             if (page == 1) {
    //                 if (response.data.items.length == 0) {
    //                     setFarms([])
    //                     setNoResault(strings('lang.No_Results'));
    //                     setMarket_avg_price(0)
    //                 }
    //                 else {
    //                     setFarms(response.data.items)
    //                     setMarket_avg_price(response.data.market_avg_price)
    //                     setNoResault('');
    //                 }
    //             }
    //             else {
    //                 if (response.data.items.length == 0) {
    //                     setFarms([])
    //                     setNoResault(strings('lang.No_Results'));
    //                     setMarket_avg_price(0)
    //                 }
    //                 else {
    //                     setFarms([...farms, ...response.data.items])
    //                     setMarket_avg_price(response.data.market_avg_price)
    //                     setNoResault('');
    //                 }
    //             }
    //             setWhatsapp(response.data)
    //             setLastPageNumber(response.data.last_page)
    //         } else {
    //             if (response.message) {
    //                 Toaster(
    //                     'top',
    //                     'danger',
    //                     Red,
    //                     response.message,
    //                     White,
    //                     1500,
    //                     screenHeight / 15,
    //                 );
    //             }
    //         }
    //         setLoadingMore(false)
    //     } catch (err) {
    //         console.log('err', err)
    //         setLoadingMore(false)
    //     }
    // };
    const getSheepCategories = async () => {
        setLoading(true)
        let response = await dispatch(farmesActions.getSheepCategories());
        if (response.success == true) {
            setSheepcategories(response.data)
            getSheepWeights(props.route.params.sheepCategory)
            setSheepcategoriesId(props.route.params.sheepCategory)
            setLoading(true)
        }
        else {
            if (response.message) {
                Toaster(
                    'top',
                    'danger',
                    Red,
                    response.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
        }
        setLoading(false);
    };

    const getSheepWeights = async (sheepcategoriesId) => {
        setLoading(true)
        let response1 = await dispatch(farmesActions.getWeights(sheepcategoriesId));
        if (response1.success == true) {
            setWeightRange(response1.data)
            setWeightRangeId(response1.data[0].id)
            getFarmsWeight(sheepcategoriesId, response1.data[0].id, 1);
        }
        else {
            if (response1.message) {
                Toaster(
                    'top',
                    'danger',
                    Red,
                    response1.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
        }
        setLoading(false);
    };

    const getFarmsWeight = async (sheepcategoriesId, weightRangeId, page) => {
        try {
            setLoadingMore(true)
            let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, page, search));
            if (response.success == true) {
                let farm = []
                if (page == 1) {
                    if (response.data.items.length == 0) {
                        setFarms([])
                        setNoResault(strings('lang.No_Results'));
                        setMarket_avg_price(0)
                    }
                    else {
                        setFarms(response.data.items)
                        setMarket_avg_price(response.data.market_avg_price)
                        setNoResault('');
                    }
                }
                else {
                    if (response.data.items.length == 0) {
                        setFarms([])
                        setNoResault(strings('lang.No_Results'));
                        setMarket_avg_price(0)
                    }
                    else {
                        setFarms([...farms, ...response.data.items])
                        setMarket_avg_price(response.data.market_avg_price)
                        setNoResault('');
                    }
                }
                setWhatsapp(response.data)
                setLastPageNumber(response.data.last_page)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoadingMore(false)
            setLoading(false);
        }
    };

    const getFarmsSearch = async (search, page) => {
        setSearch(search)
        try {
            setLoadingMore(true)
            let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, page, search));
            if (response.success == true) {
                let farm = []
                if (page == 1) {
                    setFarms(response.data.items)
                }
                else {
                    setFarms([...farms, ...response.data.items])
                }
                setWhatsapp(response.data)
                setLastPageNumber(response.data.last_page)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoadingMore(false)
            setLoading(false);
        }
    };

    const onRefresh = async () => {
        setPage(1)
        console.log('reload');
        try {
            setLoading(true)
            let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, 1, search));
            if (response.success == true) {
                if (response.data.items.legnth == 0) {
                    setNoResault(strings('lang.No_Results'));
                } else {
                    setWhatsapp(response.data.whatsapp)
                    setFarms(response.data.items)
                    setLastPageNumber(response.data.last_page)
                }
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    }

    const LoadMore = async () => {
        console.log('pageNumber', pageNumber);
        console.log('lastPageNumber', lastPageNumber);
        if (pageNumber < lastPageNumber) {
            setLoadingMoreFarms(true)
            try {
                let response = await dispatch(farmesActions.getFarms(sheepcategoriesId, weightRangeId, pageNumber + 1, search));

                if (response.success == true) {
                    setFarms([...farms, ...response.data.items]);
                    setPageNumber(pageNumber + 1);
                    setLoadingMoreFarms(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMoreFarms(false);
                }
            } catch (err) {
                setLoadingMoreFarms(false);
            }

        }
        else {
        }
    }

    const [keyboardIsOpen, setKeyboardIsOpen] = React.useState(false);
    Keyboard.addListener("keyboardDidShow", () => {
        setKeyboardIsOpen(true);
    });
    Keyboard.addListener("keyboardDidHide", () => {
        setKeyboardIsOpen(false);
    });

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                props.navigation.navigate('Live', { item: props.route.params.item, link: props.route.params.link })
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    const [isLoading, setIsLoading] = useState(true);


    useEffect(() => {
        console.log('Loading started');
        const timer = setTimeout(() => {
            setIsLoading(false);
            console.log('Loading ended');
        }, Platform.OS == 'ios' ? 20000 : 6000);

        return () => clearTimeout(timer);
    }, []);

    return (
        <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>

            {/* <Header noMargin title={props.route.params.reservation ? strings('lang.Addareservation') : strings('lang.Twistinginthemarket')} backPress={() => { setMins(-1); props.navigation.push('Live', { item: props.route.params.item, link: props.route.params.link }) }} /> */}
            <Header noMargin title={props.route.params.reservation ? strings('lang.Addareservation') : strings('lang.Twistinginthemarket')} backPress={() => { setMins(-1); props.navigation.goBack() }} />
            {loading ? <LoadingMore /> : <></>}

            <View style={{ ...styles.backgroundVideo, ...{ backgroundColor: Black } }}>
                {/* <Text style={{ color: White, fontSize: screenWidth / 25, fontFamily: appFontBold, alignSelf: 'center' }}>{strings('lang.message18')}</Text> */}
                {/* {farms.map((item, index) => {
                    return (
                        // <TouchableOpacity
                        //     onPress={() => { addSheepToPicked(item) }}
                        //     style={{ ...styles.colorContainer, ...{ backgroundColor: selectedSheepsIds.find(sheep => sheep == item.id) ? item.stamp_color : White, borderWidth: 2.5, borderColor: selectedSheepsIds.find(sheep => sheep == item.id) ? null : item.stamp_color } }} >
                        //     <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: selectedSheepsIds.find(sheep => sheep == item.id) ? White : item.stamp_color, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                        // </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { addSheepToPicked(item) }}
                            style={{ ...styles.colorContainer, ...{ backgroundColor: selectedSheepsIds.find(sheep => sheep == item.id) ? item.collar_color : White, borderWidth: 2.5, borderColor: selectedSheepsIds.find(sheep => sheep == item.id) ? null : item.collar_color } }} >
                            <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: selectedSheepsIds.find(sheep => sheep == item.id) ? White : item.collar_color, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                        </TouchableOpacity>
                    )
                })} */}
                {

                    isLoading ? (
                        <>
                            <View style={{
                                // transform: [{ rotate: '90deg' }],
                                position: 'absolute', height: '100%', width: '100%', alignItems: 'center',
                                justifyContent: 'center',
                            }}>
                                <Text style={{ color: White, marginBottom: 5, fontFamily: appFontBold, fontSize: screenWidth / 35 }}>{'برجاء الانتظار جاري تحميل البث...'}</Text>
                                {/* <LoadingBar duration={20000} /> */}
                                <CircularProgress3 size={100} strokeWidth={10}
                                    duration={Platform.OS == 'ios' ? 20000 : 6000} />
                                {/* <CircularLoader size={100} strokeWidth={4} duration={20000} /> */}

                            </View>

                        </>
                    ) : (
                        <></>
                    )

                }
                <VLCPlayer
                    style={[styles.backgroundVideo]}
                    source={{
                        initType: 2,
                        hwDecoderEnabled: 1,
                        hwDecoderForced: 1,
                        uri: link,
                        initOptions: [
                            '--no-audio',
                            '--rtsp-tcp',
                            '--network-caching=150',
                            '--rtsp-caching=150',
                            '--no-stats',
                            '--tcp-caching=150',
                            '--realrtsp-caching=150',
                        ],
                    }} videoAspectRatio="16:9"
                    // autoAspectRatio={true}
                    isLive={true}
                    autoplay={true}
                    autoReloadLive={true}
                    initOptions={[
                        "--rtsp-tcp",
                        "--network-caching=" + 0,
                        "--rtsp-caching=" + 0,
                        "--no-stats",
                        "--tcp-caching=" + 0,
                        "--realrtsp-caching=" + 0,
                    ]}
                    hwDecoderEnabled={1}
                    hwDecoderForced={1}
                    mediaOptions={{
                        // ':network-caching': 0,
                        // ':live-caching': 300,
                    }}
                    onError={(err) => console.log("video error:", err)}
                // resizeMode='contain'
                />
                <View style={styles.infoContainer1}>

                    <View style={{ width: screenWidth / 5, alignSelf: "center", flexDirection: 'row', borderRadius: 10, height: screenHeight / 35, alignItems: "center", justifyContent: "center", backgroundColor: Red }}>
                        <View style={{ width: 8, height: 8, borderRadius: 10, backgroundColor: 'red', alignSelf: 'center', marginEnd: 5, marginTop: '2%' }}></View>
                        <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text>
                    </View>

                </View>


            </View>
            <View style={{ height: screenHeight / 20, width: '100%', alignSelf: 'flex-start', justifyContent: 'space-between', }}>

                <ScrollView
                    horizontal
                    contentContainerStyle={{ flexGrow: 1 }}
                    showsHorizontalScrollIndicator={false}
                    style={{ alignSelf: "flex-start", flexDirection: 'row', height: '100%', minWidth: screenWidth, backgroundColor: Red }}
                >
                    {/* <Pressable
                            onPress={() => { setSheepcategoriesId(0); setPageNumber(1); getFarmByCategoryId(0, 1) }}
                            style={[sheepcategoriesId == 0 ? styles.activeButton : styles.Button]} >
                            <Text style={[sheepcategoriesId == 0 ? styles.activeLabel : styles.label]}>{strings('lang.All')}</Text>
                        </Pressable> */}
                    {sheepcategories.map((item, index) => {
                        return (
                            <Pressable
                                onPress={() => { setSheepcategoriesId(item.id); setPageNumber(1); getFarmsWeight(item.id, weightRangeId, 1); getSheepWeights(item.id) }}
                                style={[item.id == sheepcategoriesId ? styles.activeButton : styles.Button]} >
                                <Text style={[item.id == sheepcategoriesId ? styles.activeLabel : styles.label]}>{item.name}</Text>
                            </Pressable>
                        )
                    })}

                </ScrollView>
            </View>

            <View style={{ height: screenHeight / 18, width: '100%', alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between', backgroundColor: WhiteGery, marginBottom: 5 }}>
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1 }}
                    style={{ alignSelf: "center", flexDirection: 'row', width: '100%', height: '50%', }}
                >
                    {weightRange.map((item, index) => {
                        return (
                            <Pressable
                                onPress={() => {
                                    if (item.id == weightRangeId) {
                                        // setWeightRangeId(null); setPageNumber(1); getFarmsWeight(null, 1)
                                    }
                                    else {
                                        setWeightRangeId(item.id); setPageNumber(1); getFarmsWeight(sheepcategoriesId, item.id, 1)
                                    }
                                }}
                                style={[item.id == weightRangeId ? styles.activeButton1 : styles.Button1]}
                            >
                                <Text style={[item.id == weightRangeId ? styles.activeLabel1 : styles.label1]}>{item.from} {strings('lang.kg')}-{item.to} {strings('lang.kg')}</Text>
                            </Pressable>

                        )
                    })}
                </ScrollView>
            </View>

            {
                cartItems && cartItems.length == 0
                    ?
                    <></>
                    :
                    props.route.params.reservation == false
                        ?
                        <></>
                        :
                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', width: screenWidth / 1.1, height: screenHeight / 25, alignSelf: 'center', marginVertical: 5, }}>
                            {/* <View style={{ width: '50%', height: '95%', }}>
                            <View style={{ flexDirection: 'row', width: '90%', height: '60%', alignSelf: 'flex-start', alignItems: 'center', }}>
                                <Image source={require('../images/modrek/alert.png')} style={{ resizeMode: 'contain', width: '20%', height: '60%', tintColor: Red }} />
                                <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, marginStart: '1%' }}>{mins}:{secs < 10 && 0}{secs}</Text>
                            </View>
                            <View style={{ flexDirection: 'row', width: '90%', height: '40%', alignSelf: 'flex-start', alignItems: 'center', }}>
                                <Image source={require('../images/modrek/Groupfd.png')} style={{ resizeMode: 'contain', width: '10%', height: '50%', }} />
                                <Text style={{ color: DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 40, alignSelf: 'flex-start' }}>{strings('lang.Therequestmustbeconfirmed')}{' '}{mins}{' '}{strings('lang.min')}</Text>
                            </View>
                        </View> */}
                            <View style={{ width: '100%', height: '95%', alignItems: 'flex-end', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <TouchableOpacity onPress={() => { refRbSheet.current.open() }} style={{ backgroundColor: Red1, height: screenHeight / 25, width: screenWidth / 2.5, overflow: 'hidden', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 45, color: White, }}>{strings('lang.Bookingdetails')}</Text>
                                </TouchableOpacity>
                                <TouchableOpacity onPress={() => { props.navigation.push('Cart', { shared: false }) }} style={{ backgroundColor: Red, height: screenHeight / 25, width: screenWidth / 2.5, overflow: 'hidden', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', }}>
                                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 45, color: White, }}>{strings('lang.Advancebookingconfirmation')}</Text>
                                </TouchableOpacity>
                            </View>
                        </View>
            }


            {/* <View style={{ flexDirection: 'row', width: '100%', height: screenHeight / 30, alignItems: 'center', alignSelf: 'center', marginBottom: screenHeight / 150 }}>
                <View style={{ height: '100%', width: '100%', flexDirection: 'row', alignItems: 'center', borderColor: WhiteGery, paddingHorizontal: '2.5%' }} >
                    <View style={{ width: 5, height: '80%', backgroundColor: Red, marginEnd: 5 }}></View>
                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black }}>{strings('lang.Averagemarketprices')}</Text>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Red1, marginHorizontal: '3%' }}>{market_avg_price} {strings('lang.SR')}</Text>
                </View>
            </View> */}




            {
                loadingMore
                    ?
                    <LoadingMore />
                    :
                    <View></View>
            }

            {/* {
                    noResault ? (
                        <Text
                            style={{
                                fontSize: screenWidth / 18,
                                fontFamily: appFontBold,
                                color: Red,
                                marginTop: screenHeight / 5,
                                alignSelf: 'center',
                            }}
                        >
                            {strings('lang.No_Results')}
                        </Text>
                    ) : (
                        <View></View>
                    )
                } */}
            <ScrollView style={{ width: '100%', alignSelf: 'center' }} showsVerticalScrollIndicator={false}>


                <FlatList
                    data={farms}
                    showsVerticalScrollIndicator={false}
                    // refreshControl={
                    //     <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    // }
                    renderItem={({ item, index }) =>
                        <FarmContainer1
                            reservation={props.route.params.reservation}
                            item={item}
                            detailsPress={() => {
                                setFarmId(item.id);
                                if (index == 0) {
                                    if (props.route.params.reservation) {
                                        props.navigation.replace('TwistingInTheMarket', { weightId: weightRangeId, sheepCategory: sheepcategoriesId, farmId: item.id, reservation: true, item: item, link: item.camera_url, min: mins, sec: secs })
                                    }
                                    else {
                                        props.navigation.replace('TwistingInTheMarket', { weightId: weightRangeId, sheepCategory: sheepcategoriesId, farmId: item.id, reservation: false, item: item, link: item.camera_url, min: mins, sec: secs })
                                    }
                                }
                                else {
                                    if (props.route.params.reservation) {
                                        props.navigation.replace('TwistingInTheMarket', { weightId: weightRangeId, sheepCategory: sheepcategoriesId, farmId: item.id, reservation: true, item: item, link: item.camera_url, min: mins, sec: secs })
                                    }
                                    else {
                                        props.navigation.replace('TwistingInTheMarket', { weightId: weightRangeId, sheepCategory: sheepcategoriesId, farmId: item.id, reservation: false, item: item, link: item.camera_url, min: mins, sec: secs })
                                    }
                                }
                            }}
                            farmId={farmId}
                            livePress={() => { props.navigation.push('Live', { item: item, link: item.camera_url }) }}
                            modalPress={async () => { await setItem(item); refRbSheet1.current.open() }}
                        />
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, }}
                    onEndReachedThreshold={0.1}
                    onEndReached={LoadMore}
                // numColumns={2}
                />
                {loadingMoreFarms ? <ActivityIndicator style={{}} /> : <View></View>}


                {/* </ScrollView> */}
                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: 'center',
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {},
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey,
                        },
                    }}
                // closeOnDragDown={true}
                >
                    <View style={{ height: '100%', width: '90%', justifyContent: 'space-between', marginVertical: 8 }}>

                        <View style={{ flexDirection: 'row', height: screenHeight / 18, width: '100%', alignItems: 'center', justifyContent: 'space-between', }}>
                            <Text style={styles.text}>{strings('lang.reservedquantity')}{' '}{cartItems ? cartItems.length : ''}{' '}{cartItems ? strings('lang.head') : ''}</Text>
                            <Button transparent onPress={() => refRbSheet.current.close()} style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={require('../images/modrek/x.png')} style={{ width: '40%', height: '40%', resizeMode: 'contain', }} />
                            </Button>
                        </View>

                        {/* <ScrollView style={{ width: screenWidth / 1.1 }} showsVerticalScrollIndicator={false}>

                            <View style={{ flexDirection: 'row', width: screenWidth / 1.15, height: screenHeight / 13, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between' }}>
                                <View style={{ width: screenWidth / 8, height: screenWidth / 8, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60, backgroundColor: 'red' }}>
                                    <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>{1}</Text>
                                </View>
                                <View style={{ width: screenWidth / 1.4, flexDirection: 'column', paddingStart: '3%', alignItems: 'flex-start', }}>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, alignSelf: 'flex-start' }}> {strings('lang.Trader') + ' M10'} {'-'} {'حري'} {'-'} {25} {strings('lang.kg')}</Text>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '5%', alignSelf: 'flex-start' }}>{1200} {strings('lang.SR')}</Text>
                                </View>
                            </View>
                            <View style={{ flexDirection: 'row', width: screenWidth / 1.15, height: screenHeight / 13, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between' }}>
                                <View style={{ width: screenWidth / 8, height: screenWidth / 8, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60, backgroundColor: 'red' }}>
                                    <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>{1}</Text>
                                </View>
                                <View style={{ width: screenWidth / 1.4, flexDirection: 'column', paddingStart: '3%', alignItems: 'flex-start', }}>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, alignSelf: 'flex-start' }}> {'M12'} {'-'} {'حري'} {'-'} {25} {strings('lang.kg')}</Text>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '5%', alignSelf: 'flex-start' }}>{1200} {strings('lang.SR')}</Text>
                                </View>
                            </View>
                            <View style={{ flexDirection: 'row', width: screenWidth / 1.15, height: screenHeight / 13, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between' }}>
                                <View style={{ width: screenWidth / 8, height: screenWidth / 8, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60, backgroundColor: 'red' }}>
                                    <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>{1}</Text>
                                </View>
                                <View style={{ width: screenWidth / 1.4, flexDirection: 'column', paddingStart: '3%', alignItems: 'flex-start', }}>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, alignSelf: 'flex-start' }}> {'M12'} {'-'} {'حري'} {'-'} {25} {strings('lang.kg')}</Text>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '5%', alignSelf: 'flex-start' }}>{1200} {strings('lang.SR')}</Text>
                                </View>
                            </View>
                        </ScrollView> */}

                        <FlatList
                            data={cartItems}
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item, key }) =>
                                <View style={{ flexDirection: 'row', width: screenWidth / 1.1, height: screenHeight / 11, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between' }}>
                                    <View style={{ width: screenWidth / 8, height: screenWidth / 8, backgroundColor: item.sheep.collar_color ? item.sheep.collar_color : Black, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60 }}>
                                        <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>{item.sheep.id}</Text>
                                    </View>
                                    <View style={{ width: '82%', flexDirection: 'column', paddingStart: '3%', alignItems: 'flex-start' }}>
                                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, }}> {item.sheep.farm.code} / {item.sheep.farm.weight.from} {'-'} {item.sheep.farm.weight.to} {strings('lang.kg')}</Text>
                                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '5%' }}>{item.sheep.price} {strings('lang.SR')}</Text>
                                    </View>
                                </View>
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", width: screenWidth / 1.1, overflow: "hidden", flexDirection: "column", marginTop: 10, flexWrap: "wrap", }}
                        // onEndReached={() => this.LoadMore()}
                        // onEndReachedThreshold={0.1}
                        // numColumns={2}
                        />

                        <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 3, borderColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', marginBottom: screenHeight / 30 }}>
                            <Text style={{ color: Black, fontFamily: appFont, fontSize: screenWidth / 26, marginStart: '2%' }}>{strings('lang.Subtotal')}</Text>
                            <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 22, marginEnd: '2%' }}>{cart.sub_total} {strings('lang.SR')}</Text>
                        </View>

                    </View>


                </RBSheet>

                <RBSheet
                    ref={refRbSheet1}
                    height={screenHeight / 1.5}
                    openDuration={250}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderTopEndRadius: 25,
                            borderTopStartRadius: 25,
                            width: '100%',
                            alignSelf: 'center',
                            // bottom: '25%'
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey
                        }
                    }}
                    closeOnDragDown={true}
                    onClose={() => { setViewId(0); setIddd(0); setSheep({}); setSheepImages([]) }}
                >
                    <View style={{ flexDirection: 'column', width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', paddingBottom: '1%' }}>

                        <View style={{ flexDirection: 'row', height: screenHeight / 18, width: '100%', alignItems: 'center', justifyContent: 'space-between', }}>
                            <Text style={styles.text}> {strings('lang.thedetails')}</Text>
                            <Button transparent onPress={() => { refRbSheet1.current.close(); setViewId(0); setIddd(0); setSheep({}); setSheepImages([]) }} style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={require('../images/modrek/x.png')} style={{ width: '50%', height: '50%', resizeMode: 'contain', }} />
                            </Button>
                        </View>

                        <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                        <View style={{ height: screenHeight / 18, flexDirection: "row", alignItems: 'center', alignSelf: 'flex-start' }}>
                            <StarRating
                                disabled={true}
                                maxStars={5}
                                starSize={screenHeight / 40}
                                starStyle={{ marginEnd: 2, alignSelf: 'flex-start' }}
                                rating={item.rating}
                                fullStarColor={DarkYellow}
                            />
                            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{`(${item.rating_count})`}</Text>
                        </View>

                        <View style={{ width: '110%', height: 3, backgroundColor: WhiteGery, }}></View>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', marginBottom: 3 }}>{strings('lang.sheepAvailable')}</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ minHeight: screenHeight / 16, alignSelf: 'flex-start', }}>
                            {item.sheep && item.sheep.map((item, index) => (
                                <Pressable
                                    onPress={() => {
                                        setViewId(1);
                                        setIddd(item.id);
                                        setSheep(item);
                                        let sheepImages = [];
                                        for (let itemm of item.images) {
                                            sheepImages = [...sheepImages, { url: itemm }]
                                        };
                                        setSheepImages(sheepImages)
                                    }}
                                    style={{ backgroundColor: iddd == item.id ? LightGreen : null, width: screenWidth / 9, height: screenHeight / 16, borderTopEndRadius: 10, borderTopStartRadius: 10, paddingTop: 5, alignItems: 'center' }}
                                >
                                    <View style={{ width: screenWidth / 13, height: screenWidth / 13, borderRadius: screenWidth / 26, backgroundColor: item.collar_color ? item.collar_color : Black, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}>{index + 1}</Text>
                                    </View>
                                </Pressable>
                            ))}
                        </ScrollView>
                        {viewId == 1
                            ?
                            <>
                                <View style={{ backgroundColor: LightGreen, width: '100%', borderRadius: 0, paddingTop: 10, flexDirection: 'row', minHeight: screenHeight / 5 }}>
                                    <View style={{ width: '20%', height: '100%', alignItems: 'center' }}>
                                        <Image source={require('../images/modrek/qr.png')} style={{ width: '100%', height: screenHeight / 13, resizeMode: 'contain', }} />
                                        <Pressable onPress={() => { setViewId(2); console.log('sheep.images', sheepImages); }} style={{ width: screenWidth / 6.5, height: screenHeight / 13, resizeMode: 'contain', }}>
                                            <Image source={require('../images/modrek/Rectangle-7.png')} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', }} />
                                            <Image source={require('../images/modrek/scanner2.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', position: 'absolute', top: '15%', start: '15%' }} />
                                        </Pressable>
                                    </View>
                                    <View style={{ width: '80%', height: '100%', alignItems: 'center', paddingEnd: 10, paddingBottom: 10 }}>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Weight')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{`${item.weight && item.weight.from} kg -${item.weight && item.weight.to} kg`}</Text>
                                            </View>
                                        </View>
                                        {/* <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.OrderNumber')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{'2'}</Text>
                                        </View>
                                    </View> */}
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Gender')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{item.sheep_category && item.sheep_category.name}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>

                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.sealcolor')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.stamp_color}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.collarcolor')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.collarـcolor}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.referencenumber')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.id}</Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>

                                <Button
                                    onPress={() => { refRbSheet1.current.close(); props.navigation.push('Live', { item: item, link: item.camera_url }) }}
                                    style={styles.buttonContainer}>
                                    <Text style={styles.buttonText}>{strings('lang.Gotobooking')}</Text>
                                </Button>
                            </>
                            :
                            viewId == 2
                                ?
                                <View style={{ width: '100%', height: screenHeight / 3, }}>
                                    <Button transparent onPress={() => { setViewId(1) }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                                        <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                                    </Button>
                                    {/* <Image source={require('../images/modrek/Rectangle-7.png')} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', }} /> */}

                                    <ImageViewer onChange={(imageIndex) => { setImageUrl(sheepImages[imageIndex].url) }} enableSwipeDown={false} onSwipeDown={() => { setModalImageVisible(false) }} backgroundColor='white' style={{ backgroundColor: 'White', maxHeight: '75%', borderRadius: 10, }} imageUrls={sheepImages} />
                                    <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', backgroundColor: White, zIndex: 100, padding: 5 }} >
                                        {sheepImages.map((item, index) => {
                                            return (
                                                <Button transparent onPress={() => { console.log('1'); setImageUrl(sheepImages[index].url) }} style={styles.imageActive}>
                                                    <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', }} />
                                                </Button>
                                            )
                                        })}
                                    </ScrollView>
                                </View>
                                :
                                <></>
                        }
                    </View>

                </RBSheet>


            </ScrollView>

            {/* {
                !keyboardIsOpen &&
                <>
                    <View style={{ height: screenHeight / 10 }}></View>
                    <MyFooter current={'Home'} navigation={props.navigation} />
                </>
            } */}
        </View >

    )
}

export default TwistingInTheMarket;
const styles = StyleSheet.create({
    container: {
        flexDirection: 'row',
        alignSelf: 'flex-start',
        justifyContent: 'space-between',
        marginVertical: '1.5%'
    },

    title: {
        fontFamily: appFont,
        fontSize: screenWidth / 28,
        color: Black
    },
    title2: {
        fontFamily: appFont,
        fontSize: screenWidth / 28,
        color: Black,
    },
    text: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        color: Black
    },
    title1: {
        marginStart: '15%',
        fontFamily: appFont,
        fontSize: screenWidth / 28
    },

    colorContainer: {
        width: screenHeight / 22,
        height: screenHeight / 22,
        alignSelf: 'center',
        marginHorizontal: 10,
        borderRadius: screenHeight / 10,
        borderWidth: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 4
    },

    image: {
        // ...StyleSheet.absoluteFillObject,
        resizeMode: "cover",
        height: '100%',
        width: '100%',
        alignSelf: "center",
    },
    activeButton: { height: '95%', minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: White, borderBottomWidth: 2, backgroundColor: Red },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 35, color: 'white', },
    Button: { height: '95%', minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: Red, borderBottomWidth: 1, backgroundColor: Red },
    label: { fontFamily: appFont, fontSize: screenWidth / 35, color: 'white', },

    activeButton1: { height: '60%', backgroundColor: Red, borderRadius: 8, minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', marginHorizontal: 5, },
    activeLabel1: { fontFamily: appFont, fontSize: screenWidth / 35, color: White, marginHorizontal: 10 },
    Button1: { height: '60%', backgroundColor: MediumGrey, borderRadius: 8, minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', marginHorizontal: 5, borderColor: WhiteGery, borderWidth: 1 },
    label1: { fontSize: screenWidth / 35, color: 'black', marginHorizontal: 10 },
    label2: { fontFamily: appFont, fontSize: screenWidth / 35, color: 'black', end: "90%", marginVertical: '7%' },

    Button2: { height: '80%', width: screenWidth / 2.5, flexDirection: 'row', backgroundColor: WhiteGery, borderTopRightRadius: 20, borderBottomRightRadius: 20, alignItems: 'flex-start', justifyContent: 'flex-start', alignSelf: 'flex-start', borderColor: WhiteGery, borderWidth: 1 },
    headercontainer: {
        backgroundColor: Red, height: screenHeight / 8, alignSelf: 'flex-start', alignItems: 'center', flexDirection: "row", width: '100%', paddingTop: Platform.OS == 'ios' ? screenHeight / 18 : 10
    },
    farmImageContainer: {
        width: screenWidth / 8,
        height: screenWidth / 8,
        marginLeft: screenWidth / 20,
        marginTop: screenHeight / 55,
        overflow: 'hidden',
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: White,
        borderRadius: 10,
    },

    title: {
        fontFamily: appFontBold, color: Black, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    title2: {
        fontFamily: appFontBold, color: Red, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    imgContainer: {
        width: '100%', height: '100%', justifyContent: "center"
    },
    backButton: {
        width: '33%', height: '100%', justifyContent: "center",
    },
    backImage: {
        width: "80%", height: "90%", resizeMode: "contain", marginBottom: '20%'
    },
    drawerImage: {
        width: "90%", height: "70%", resizeMode: "contain", alignSelf: 'center'
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '100%',
        height: screenHeight / 18,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '5%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    backgroundVideo: {
        width: screenWidth,
        height: screenHeight / 3.8,
        justifyContent: 'center',
        // position: 'absolute',
        // top: -10,
        // left: 0,
        // bottom: 0,
        // right: 0,
        zIndex: 10000,
    },
    icon: { width: '50%', height: '50%', resizeMode: 'contain', },
    imageActive: { height: 80, width: screenWidth / 6, borderColor: MediumGrey, borderRadius: 10, overflow: 'hidden', alignSelf: 'center', marginHorizontal: 5 },
    imageUnactive: { height: 75, width: screenWidth / 6, borderWidth: 1, borderColor: MediumGrey, borderRadius: 10, overflow: 'hidden', alignSelf: 'center', marginHorizontal: 5 },
    infoContainer1: {
        width: screenWidth / 3,
        height: screenHeight / 35,
        marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute', zIndex: 100000, top: 10, end: 10
    },
});
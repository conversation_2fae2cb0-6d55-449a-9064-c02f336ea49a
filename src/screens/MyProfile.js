import { Button, Input } from 'native-base';
import React, { useEffect, useState } from 'react';
import { I18nManager, Image, ScrollView, StyleSheet, Text, View } from "react-native";
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import { Black, DarkGreen, DarkGrey, MediumGrey, Red, Red1, White, WhiteGery, appFont, appFontBold, screenHeight, screenWidth } from '../components/Styles';
// import { ScrollView } from 'react-native-gesture-handler';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { launchImageLibrary } from 'react-native-image-picker'; // Migration from 2.x.x to 3.x.x => showImagePicker API is removed.
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch, useSelector } from 'react-redux';
import * as authActions from '../../Store/Actions/auth';
import LoadingMore from '../components/LoadingMore';
import Toaster from '../components/Toaster';
import { strings } from './i18n';

const MyProfile = (props) => {

    const [UserProfile, setUserProfile] = useState({})
    const [loading, setLoading] = useState(false)
    const [loadingMore, setLoadingMore] = useState(false);
    const [edit, setEdit] = useState(false);
    const [email, setEmail] = useState('')
    const [error_email, seterror_email] = useState('')
    const dispatch = useDispatch();

    const user = useSelector(state => state.auth.user)

    // useFocusEffect(
    //     React.useCallback(() => {
    //         const onBackPress = () => {
    //             // BackHandler.exitApp()
    //             props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
    //             return true;
    //         };

    //         BackHandler.addEventListener('hardwareBackPress', onBackPress);

    //         return () =>
    //             BackHandler.removeEventListener('hardwareBackPress', onBackPress);
    //     }, []),
    // );


    useEffect(() => {
        setUserProfile(user)
        setEmail(user.email)
    }, []);

    const chooseFile = async () => {
        let options = {
            quality: 0.5,
            maxWidth: 500,
            maxheight: 400,
            title: 'Select Image',
            customButtons: [
                {
                    name: 'customOptionKey',
                    title: 'Choose Photo from Custom Option',
                },
            ],
            storageOptions: {
                skipBackup: true,
                path: 'images',
            },
        };
        launchImageLibrary(options, response => {
            console.log('Response = ', response);

            if (response.didCancel) {
                console.log('User cancelled image picker');
            } else if (response.error) {
                console.log('ImagePicker Error: ', response.error);
            } else if (response.customButton) {
                console.log('User tapped custom button: ', response.customButton);
                alert(response.customButton);
            } else {
                // let cover = response.assets[0];
                // You can also display the image using data:
                // let source = {
                //   uri: 'data:image/jpeg;base64,' + response.data
                // };
                let cover = {
                    uri: response.assets[0].uri,
                    type: response.assets[0].type,
                    name: response.assets[0].fileName,
                    link: '',
                };
                editProfileImage(cover);
            }
        });
    };

    const editProfileImage = async (cover) => {
        setLoadingMore(true)
        try {
            let response = await dispatch(authActions.editProfileImage(cover));

            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    strings('lang.editImageSuccessfully'),
                    White,
                    1500,
                    screenHeight / 50,
                );
                setLoadingMore(false)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                    console.log(response);
                    setLoadingMore(false)
                }
            }
        } catch (err) {
            console.log(err);
            setLoadingMore(false)
        }
    };


    const updateEmail = async () => {
        if (edit == false) {
            setEdit(true)
        }
        else if (edit == true) {

            let reg = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w\w+)+$/;
            if (email == '') {
                seterror_email(strings('lang.Please_insert_field'))
            }
            else if (reg.test(email) === false) {
                seterror_email(strings('lang.validEmail'))
            }
            else {
                try {
                    setLoadingMore(true)
                    let UpdateEmail = await dispatch(authActions.UpdateEmail(email));
                    if (UpdateEmail.success == true) {
                        Toaster(
                            'top',
                            'success',
                            'green',
                            (strings('lang.profileupdatedsuccessfully')),
                            White,
                            1500,
                            screenHeight / 15,
                        );
                        setEdit(false)
                        setUserProfile(UpdateEmail.data.user)
                    } else {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            UpdateEmail.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                    setLoadingMore(false);
                }
                catch (err) {
                    console.log('err', err)
                    setLoadingMore(false);
                }
            }

        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Profile')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 4,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 5,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 7,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 7,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={''} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Profile')} backPress={() => { props.navigation.goBack() }} />

                {loadingMore ? <LoadingMore /> : <></>}

                <ScrollView showsVerticalScrollIndicator={false}>
                    <TouchableOpacity onPress={() => { chooseFile() }} style={{ alignSelf: 'center', width: screenWidth / 1.1, borderRadius: 10, height: screenHeight / 4, overflow: 'hidden', alignItems: 'center', justifyContent: 'center', marginBottom: screenHeight / 40 }}>
                        <Image source={UserProfile.image ? { uri: UserProfile.image } : require('../images/user.png')} style={{ width: '50%', height: '50%', resizeMode: 'contain', }} />
                    </TouchableOpacity>
                    <View style={{ width: '95%', alignSelf: 'center', alignItems: 'flex-start' }}>
                        <Text style={{ textAlign: 'left', fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26, }}>{UserProfile.name}</Text>
                        {/* <Text style={{ fontFamily: appFontBold, color: Gold, fontSize: screenWidth / 26, }}>عميل ذهبي</Text> */}
                        {/* <Text style={{ fontFamily: appFont, color: DarkGrey, fontSize: screenWidth / 30, }}>
                            هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم
                            توليد هذا النص من مولد النص العربى، حيث يمكنك أن تولد مثل هذا
                        </Text> */}
                    </View>

                    <View style={{ backgroundColor: MediumGrey, height: 1, width: '95%', marginVertical: 5, marginTop: 10, alignSelf: 'center' }} />

                    <View style={styles.sectionContainer}>
                        <View style={styles.iconContainer}>
                            <Image source={require('../images/Silal/call.png')} style={{ tintColor: Red, width: '70%', height: '25%', resizeMode: 'contain', marginTop: '25%', scaleX: -1 }} />
                        </View>
                        <View style={styles.dataContainer}>
                            <Text numberOfLines={1} style={styles.label}>{strings('lang.Phone')}</Text>
                            <Text numberOfLines={1} style={styles.labelGrey}>{UserProfile.phone}</Text>
                        </View>
                        {/* <View style={styles.btnContainer}>
                            <Button style={styles.button}>
                                <Text numberOfLines={1} style={styles.labelWhite}>{strings('lang.Edit')}</Text>
                            </Button>
                        </View> */}
                    </View>
                    <View style={styles.sectionContainer}>
                        <View style={styles.iconContainer}>
                            <Image source={require('../images/Silal/message.png')} style={styles.icon} />
                        </View>
                        <View style={styles.dataContainer}>
                            <Text numberOfLines={1} style={styles.label}>{strings('lang.Email')}</Text>
                            {edit == false
                                ?
                                <Text numberOfLines={1} style={styles.labelGrey}>{UserProfile.email}</Text>
                                :
                                <>
                                    <Input
                                        style={{ borderWidth: 1, borderColor: MediumGrey, borderRadius: 10, margin: 5, fontFamily: appFontBold, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'right' : 'left' }}
                                        value={email} onChangeText={(text) => {
                                            setEmail(text);
                                            seterror_email('')
                                        }
                                        }
                                    />
                                    {error_email ?
                                        <Text style={styles.loginError}>{error_email}</Text>
                                        :
                                        <View style={{ height: 0 }}></View>
                                    }
                                </>
                            }
                        </View>
                        <View style={styles.btnContainer}>
                            <Button
                                onPress={updateEmail}
                                style={styles.button}>
                                <Text numberOfLines={1} style={styles.labelWhite}>{edit == false ? strings('lang.Edit') : strings('lang.Confirm')}</Text>
                            </Button>

                            {edit
                                ?
                                <Button onPress={() => setEdit(false)} style={[styles.button, { marginTop: 5, backgroundColor: Red1 }]}>
                                    <Text numberOfLines={1} style={styles.labelWhite}>{strings('lang.Cancel')}</Text>
                                </Button>
                                :
                                <></>
                            }

                        </View>
                    </View>

                    <View style={styles.sectionContainer}>
                        <View style={styles.iconContainer}>
                            <Image source={require('../images/Silal/lock.png')} style={styles.icon} />
                        </View>
                        <View style={styles.dataContainer}>
                            <Text numberOfLines={1} style={styles.label}>{strings('lang.Password')}</Text>
                            <Text numberOfLines={1} style={styles.labelGrey}>........</Text>
                        </View>
                        <View style={styles.btnContainer}>
                            <Button onPress={() => { props.navigation.navigate('ChangePassword') }} style={styles.button}>
                                <Text numberOfLines={1} style={styles.labelWhite}>{strings('lang.Change')}</Text>
                            </Button>
                        </View>
                    </View>

                </ScrollView>

                <View style={{ height: screenHeight / 8 }}></View>

                <MyFooter navigation={props.navigation} />
            </View>
        )
    };
};

export default MyProfile;
const styles = StyleSheet.create({
    labelContainer: { width: '90%', alignSelf: 'center', height: screenHeight / 18, justifyContent: 'center', paddingHorizontal: 5, borderBottomColor: DarkGrey, borderBottomWidth: 1 },
    itemContainer: { width: '90%', alignSelf: 'center', height: screenHeight / 20, justifyContent: 'space-between', flexDirection: 'row', alignItems: 'center' },
    label: { fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black, alignSelf: 'flex-start' },
    labelWhite: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: White },
    labelGrey: { fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, textAlign: I18nManager.isRTL ? 'right' : 'left', alignSelf: 'flex-start' },
    button: {
        width: '100%',
        height: 35,
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 20,
        alignItems: 'center',
        padding: 5,
        marginBottom: 0,
        zIndex: 3
    },
    sectionContainer: { width: screenWidth / 1.1, alignSelf: 'center', flexDirection: 'row', borderBottomColor: MediumGrey, marginVertical: 5, borderBottomWidth: 1 },
    btnContainer: { width: '20%', justifyContent: 'center' },
    dataContainer: { width: '70%', },
    iconContainer: { width: '10%', height: screenHeight / 10, alignItems: 'center', },
    icon: { width: '70%', height: '20%', resizeMode: 'contain', marginTop: '25%', tintColor: Red },
    loginError: {
        color: Red1, fontFamily: appFont, alignSelf: 'flex-start', fontSize: screenWidth / 33
    },
});

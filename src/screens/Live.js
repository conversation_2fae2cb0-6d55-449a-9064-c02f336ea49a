import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import {
    BackHandler,
    I18nManager,
    Image,
    Modal,
    Platform,
    StyleSheet,
    Text,
    View,
    ImageBackground
} from "react-native";
import {
    copilot,
    walkthroughable
} from "react-native-copilot";
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import RBSheet from 'react-native-raw-bottom-sheet';
import { VLCPlayer } from 'react-native-vlc-media-player';
import { useDispatch, useSelector } from 'react-redux';
import * as cartActions from '../../Store/Actions/cart';
import * as moveSheepRequestsActions from '../../Store/Actions/moveSheepRequests';
import CircularProgress3 from '../components/CircularProgress3';
import Loading from '../components/Loading';
import MyComponent from '../components/MyComponent';
import { Black, DarkGreen, DarkGrey, MediumGrey, Red, Red1, White, appFontBold, appFontTajawal, screenHeight, screenWidth } from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';
import appsFlyer from 'react-native-appsflyer';

const Live = props => {
    const refRbSheet = useRef();

    const [item, setItem] = useState({})
    const [farms, setFarms] = useState([])
    const [sheep, setSheep] = useState([])
    const [selectedSheeps, setSelectedSheeps] = useState([])
    const [selectedSheepsIds, setSelectedSheepsIds] = useState([])
    const [selectedSheepsId, setSelectedSheepsId] = useState([])
    const [selectedSheep, setSelectedSheep] = useState({})
    const [linkk, setLinkk] = useState('rtsp://admin:<EMAIL>:4444/cam/realmonitor?channel=17&subtype=1')
    const [link, setLink] = useState(props.route.params.link)
    const [link1, setLink1] = useState('rtsp://rtsp.stream/pattern')
    const [camera, setCamera] = useState(0)
    const [moveCount, setMoveCount] = useState(3)
    const [sharingId, setSharingId] = useState('')
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [cameraId, setCameraId] = useState(true)
    const dispatch = useDispatch();
    const IsFocused = useIsFocused();
    const refLivePlayer = useRef();
    const [isLoading, setIsLoading] = useState(true);
    const [ModalVisible, setModalVisible] = useState(false);
    const [ModalVisibleSheep, setModalVisibleSheep] = useState(false);
    const [ModalVisibleShareType, setModalVisibleShareType] = useState(false);
    const [ModalVisibleShareType2, setModalVisibleShareType2] = useState(false);
    const [is_confirmed, setIs_confirmed] = useState(false);

    const today = useSelector((state) => state.moveSheepRequests.today);


    useEffect(() => {

        console.log('Loading started');
        console.log('moveSheepRequeststoday', today);
        console.log('props.route.params.sheepTypeId', props.route.params.sheepTypeId);
        setMoveCount(moveCount - today.length)
        setIs_confirmed(today[0] && today[0].is_confirmed)
        const timer = setTimeout(() => {
            setIsLoading(false);
            console.log('Loading ended');
        }, Platform.OS == 'ios' ? 20000 : 6000);

        return () => clearTimeout(timer);
    }, []);

    useEffect(() => {
        // const getSheep = async () => {
        //     let item = props.route.params.item
        //     try {
        //         setLoading(true)
        //         let response = await dispatch(farmsActions.getSheep(item.id));
        //         if (response.success == true) {
        //             setSheep(response.data)
        //         } else {
        //             if (response.message) {
        //                 Toaster(
        //                     'top',
        //                     'danger',
        //                     Red,
        //                     response.message,
        //                     White,
        //                     1500,
        //                     screenHeight / 15,
        //                 );
        //             }
        //         }
        //         setLoading(false);
        //     } catch (err) {
        //         console.log('err', err)
        //         setLoading(false);
        //     }
        // };
        // getSheep()


        let item = props.route.params.item
        setFarms(item.sheep)
        setItem(item)
        const id = item.id.toString();
        console.log('props.route.params.link', props.route.params.link);
        // console.log('props.route.params.item', item);
        console.log('props.route.params.item', item.id.toString());
        console.log('props.route.params.item', id);

        const logViewEvent = () => {
            const eventName = 'af_content_view';
            const eventValues = {
                af_content_id: id,
                // af_content_type: 'product',
                af_currency: 'SAR',
                // af_price: 49.99,
            };

            appsFlyer.logEvent(eventName, eventValues,
                (res) => {
                    console.log('Event sent successfully:', res);
                },
                (err) => {
                    console.error('Event failed to send:', err);
                }
            );
        };

        logViewEvent()
    }, []);

    const toggleCameras = (id) => {
        setLoadingMore(true)
        if (id == 1) {
            setLoading(true)
            console.log(id)
            setLinkk('rtsp://admin:<EMAIL>:4444/cam/realmonitor?channel=12&subtype=1')
            setLoadingMore(false)
            setCamera(0)
            setLoading(false)
        }
        if (id == 2) {
            setLoading(true)
            console.log(id)
            setLinkk('rtsp://admin:<EMAIL>:4444/cam/realmonitor?channel=7&subtype=1')
            // this.setState({ link: 'rtsp://admin:admin123@37.224.97.61:554/cam/realmonitor?channel=2&subtype=0' });
            setLoadingMore(false)
            setCamera(1)
            setLoading(false)
        }
    }

    const addSheepToPicked = async (item) => {
        setLoadingMore(true)
        let nextPickedSheeps = [...selectedSheeps];
        const pickedSheep = selectedSheeps.find(x => x.id === item.id);
        const pickedSheepIndex = selectedSheeps.findIndex(x => x.id === item.id);
        if (pickedSheep) {
            // try {
            //     let response = await dispatch(cartActions.removeItem(item.id));
            // if (response.success == true) {
            nextPickedSheeps.splice(pickedSheepIndex, 1);
            setSelectedSheeps(nextPickedSheeps)
            // }
            // else {
            // }
            // }
            // catch (err) {
            //     console.log('err', err)
            // }
            // console.log(nextPickedSheeps);
        }
        else {
            // try {
            //     let response = await dispatch(cartActions.addItem(item.id));
            // if (response.success == true) {
            nextPickedSheeps = [...nextPickedSheeps, item];
            setSelectedSheeps(nextPickedSheeps)
            // }
            // else {
            // }
            // }
            // catch (err) {
            //     console.log('err', err)
            // }
            // console.log(nextPickedSheeps);
        }
        let pickedSheepsIDs = []
        let pickedSheepsID = []
        let pickedSheeps = {}
        for (const item1 of nextPickedSheeps) {
            console.log('sss', item1);
            if (props.route.params.sheepTypeId == 1) {
                if (item1.sharing_type && item1.sharing_type.count == 2) {
                    pickedSheepsIDs = []
                    pickedSheepsID = [item1.id]
                } else if (item1.sharing_type && item1.sharing_type.count == 4) {
                    pickedSheepsIDs = []
                    pickedSheepsID = []
                    pickedSheepsID = [item1.id]
                }

                else {
                    pickedSheepsID = []
                    pickedSheepsIDs = [...pickedSheepsIDs, item1.id]
                }
            }
            else if (props.route.params.sheepTypeId == 2) {
                if (item1.sharing_type && item1.sharing_type.count == 4) {
                    pickedSheepsIDs = []
                    pickedSheepsID = []
                    pickedSheepsID = [item1.id]
                }

                else {
                    pickedSheepsID = []
                    // pickedSheepsIDs = [...pickedSheepsIDs, item1.id]
                    pickedSheepsIDs = [item1.id]
                }
            } else {
                if (item1.sharing_type && item1.sharing_type.count == 2) {
                    pickedSheepsIDs = []
                    pickedSheepsID = [item1.id]
                }
                else {
                    pickedSheepsID = []
                    // pickedSheepsIDs = [...pickedSheepsIDs, item1.id]
                    pickedSheepsIDs = [item1.id]
                }
            }

            pickedSheeps = item1

        }

        if (props.route.params.sheepTypeId == 1) {
            if (pickedSheeps.sharing_type && pickedSheeps.sharing_type.count == 2) {
                // setModalVisibleShareType(!ModalVisibleShareType)
                setSharingId('half')
                refRbSheet.current.open()
                setSelectedSheep(pickedSheeps)
                setSelectedSheepsId(pickedSheepsID)
                setSelectedSheepsIds([])
            }
            else if (pickedSheeps.sharing_type && pickedSheeps.sharing_type.count == 4) {
                // setModalVisibleShareType2(!ModalVisibleShareType2)
                setSharingId('quarter')
                refRbSheet.current.open()
                setSelectedSheep(pickedSheeps)
                setSelectedSheepsId(pickedSheepsID)
                setSelectedSheepsIds([])
            }
            else {
                setSelectedSheepsIds(pickedSheepsIDs)
                setSelectedSheepsId([])
            }
        }
        else if (props.route.params.sheepTypeId == 2) {
            if (pickedSheeps.sharing_type && pickedSheeps.sharing_type.count == 4) {
                // setModalVisibleShareType2(!ModalVisibleShareType2)
                setSharingId('quarter')
                refRbSheet.current.open()
                setSelectedSheep(pickedSheeps)
                setSelectedSheepsId(pickedSheepsID)
                setSelectedSheepsIds([])
            }
            else {
                setSelectedSheepsIds(pickedSheepsIDs)
                setSelectedSheepsId([])
            }
        } else {

            if (pickedSheeps.sharing_type && pickedSheeps.sharing_type.count == 2) {
                // setModalVisibleShareType(!ModalVisibleShareType)
                setSharingId('half')
                refRbSheet.current.open()
                setSelectedSheep(pickedSheeps)
                setSelectedSheepsId(pickedSheepsID)
                setSelectedSheepsIds([])
            }
            else {
                setSelectedSheepsIds(pickedSheepsIDs)
                setSelectedSheepsId([])
            }
        }
        console.log('pickedSheeps', pickedSheeps);
        console.log(pickedSheepsID);
        console.log(pickedSheepsIDs);

        setLoadingMore(false)
    }

    const addSheeps = async () => {
        setLoading(true)

        for (let item of selectedSheepsIds) {
            try {
                let response = await dispatch(cartActions.addItem(item, props.route.params.sheepTypeId));
                if (response.success == false) {
                    setLoading(false)
                    Toaster(
                        'top',
                        'warning',
                        Red1,
                        // strings('lang.Sheepisnotavailable'),
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                } else {
                    setLoading(false)
                }
            }
            catch (err) {
                console.log('err', err)
            }
        }
        props.navigation.push('Cart', { shared: false, sheepTypeId: props.route.params.sheepTypeId })

        setLoading(false)
    }

    const addSheepsShareType2 = async () => {
        setLoading(true)

        for (let item of selectedSheepsId) {
            try {
                let response = await dispatch(cartActions.addItem(item,
                    selectedSheep.sharing_type && selectedSheep.sharing_type.id
                ));
                if (response.success == false) {
                    setLoading(false)
                    Toaster(
                        'top',
                        'warning',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 50,
                    );
                } else {
                    // setModalVisibleShareType(!ModalVisibleShareType)
                    // setModalVisibleShareType2(!ModalVisibleShareType2)
                    refRbSheet.current.close();
                    setLoading(false)
                }
            }
            catch (err) {
                console.log('err', err)
            }
        }
        props.navigation.push('Cart', { shared: false, sheepTypeId: selectedSheep.sharing_type && selectedSheep.sharing_type.count == 4 ? 3 : 2 })
        setLoading(false)
    }

    const [Token, setToken] = useState('');

    useEffect(() => {

        const token = async () => {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        }
        token()
    }, []);
    const skipLogin = () => {
        Toaster(
            'top',
            'warning',
            Red1,
            strings('lang.login_continuou'),
            White,
            1500,
            screenHeight / 50,
        );
        props.navigation.navigate('Login')
    }
    const CopilotText = walkthroughable(Text);
    const CopilotButton = walkthroughable(Button);
    const CopilotScroll = walkthroughable(ScrollView);
    const CopilotView = walkthroughable(View);
    const [showCopilot, setShowCopilot] = useState(false);
    const [showCopilotId, setShowCopilotId] = useState(1);
    const [intro, setIntro2] = useState('');

    useEffect(() => {
        const checkIfFirstLaunch = async () => {
            let intro2 = await AsyncStorage.getItem('intro2');
            if (intro2 == 'Done') {
            } else {
                // if (Platform.OS == 'ios') {
                //     setModalVisible(!ModalVisible)
                // } else {
                setModalVisible(!ModalVisible)
                // }
            }
        };

        setTimeout(() => {
            checkIfFirstLaunch();
        }, 1000);
    }, []);


    useEffect(() => {
        const getToday = async () => {
            // setLoading(true)
            let response = await dispatch(moveSheepRequestsActions.getToday());
            if (response.success == true) {
                setMoveCount(moveCount - response.data.length)
                setIs_confirmed(response.data[0] && response.data[0].is_confirmed)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoading(false);
            }
        };
        const interval = setInterval(() => {
            getToday()
        }, 60000); // 60000ms = 1 minute
        getToday()

        // // Cleanup interval on component unmount
        return () => clearInterval(interval);
    }, []);



    const moveSheep = async () => {
        try {
            let response = await dispatch(moveSheepRequestsActions.store(props.route.params.item.id));
            if (response.success == true) {
                getToday()
                Toaster(
                    'top',
                    'success',
                    Red,
                    strings('lang.sentsuccesfully'),
                    White,
                    1500,
                    screenHeight / 15,
                );
                setModalVisibleSheep(!ModalVisibleSheep)
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
        }
        catch (err) {
            console.log('err', err)
            setLoadingMore(false)
        }
    }

    const getToday = async () => {
        // setLoading(true)
        let response = await dispatch(moveSheepRequestsActions.getToday());
        if (response.success == true) {
            setMoveCount(3 - response.data.length)
            setIs_confirmed(response.data[0] && response.data[0].is_confirmed)
        }
        else {
            if (response.message) {
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    response.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
            setLoading(false);
        }
    };


    const Finish = () => {
        AsyncStorage.setItem("intro2", "Done")
        setModalVisible(!ModalVisible)
    }
    const handleStepChange = (step) => {
        console.log(`step change:${step.name}`);
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                props.navigation.push('Home', {
                    scrollToIndex: props.route.params.scrollToIndex,
                    logo: props.route.params.logo,
                    sheepTypeId: props.route.params.sheepTypeId,
                    weightId: props.route.params.weightId,
                    sheepCategory: props.route.params.sheepCategory,
                    sheepCategoryName: props.route.params.sheepCategoryName,
                    sheepTypeName: props.route.params.sheepTypeName,
                    sheepWeightName: props.route.params.sheepWeightName,
                    farmId: props.route.params.farmId,
                    link: props.route.params.link,
                })
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );
    return (
        <View style={{
            height: screenHeight / 1, width: screenWidth, justifyContent: 'space-between', alignItems: 'center', transform: [{ rotate: I18nManager.isRTL ? '0deg' : '0deg' }],
            // backgroundColor: '#02161D'
            backgroundColor: Black
        }}>


            {loadingMore ? <Loading /> : <></>}

            <View style={{ width: screenWidth / 1, height: screenHeight / 6, flexDirection: 'row', marginTop: -screenHeight / 100, zIndex: 10000 }}>
                <View style={{ ...styles.infoContainer, ...{ backgroundColor: null } }}>
                </View>

                <View style={{
                    alignSelf: "center", alignItems: 'center',
                    justifyContent: 'flex-end', zIndex: 1000, transform: [{ rotate: '90deg' }],
                    height: screenWidth / 3, width: screenWidth / 5, marginStart: screenWidth / 5, marginTop: Platform.OS == 'ios' ? screenHeight / 17 : 0
                }}>

                    <Button
                        transparent
                        disabled={moveCount == 0 || is_confirmed == false ? true : false} onPress={async () => {
                            if (Token) {
                                setModalVisibleSheep(!ModalVisibleSheep)
                                // refRbSheet.current.open()
                            } else {
                                skipLogin()
                            }

                        }}
                        //  style={{
                        //     ...styles.reserveContainer, ...{
                        //         paddingHorizontal: 5, backgroundColor: moveCount == 0 || is_confirmed == false ? MediumGrey : Red, flexDirection: 'row', justifyContent: 'space-between'
                        //     }
                        // }}>
                        style={{
                            ...styles.reserveContainer, ...{
                                paddingHorizontal: 5, flexDirection: 'row', justifyContent: 'space-between'
                            }
                        }}>
                        <Image
                            source={
                                moveCount == 0 || is_confirmed == false ?
                                    require('../images/newIcon/confirmSheep.png')
                                    :
                                    require('../images/newIcon/moveSheep.png')
                            }
                            style={{
                                width: Platform.OS == 'ios' ? screenWidth / 5.5 : screenWidth / 5,
                                height: screenWidth / 8.4,
                                position: 'absolute',
                                resizeMode: 'cover'
                            }}
                        // resizeMode="cover"
                        />
                        <View style={{
                            backgroundColor: Red1,
                            height: '100%', width: '22%',
                            borderRadius: 5,
                            justifyContent: 'center',
                            alignItems: 'center'
                        }}>
                            <Text style={{ fontSize: screenWidth / 30, fontFamily: appFontBold, color: White, alignSelf: 'center', textAlignVertical: 'center' }}>{moveCount}</Text>
                        </View>
                        <Text style={{ fontSize: screenWidth / 44, fontFamily: appFontBold, color: moveCount == 0 || is_confirmed == false ? Red : White, alignSelf: 'center', textAlignVertical: 'center' }}>{strings('lang.Movethesheep')}</Text>
                    </Button>

                    {/* <Button disabled={false} onPress={async () => {
                        if (Token) {
                            props.navigation.navigate('TwistingInTheMarket', { weightId: item.weight_id, sheepCategory: item.sheep_category.id, farmId: item.id, reservation: false, item: props.route.params.item, link: link, min: 10, sec: 0 })
                        } else {
                            skipLogin()
                        }

                    }} style={{ ...styles.reserveContainer, ...{ backgroundColor: Red1, } }}>
                        <Text style={{ fontSize: screenWidth / 44, fontFamily: appFontBold, color: White, alignSelf: 'center', textAlignVertical: 'center' }}>{strings('lang.Twistinginthemarket')}</Text>
                    </Button> */}


                    {/* <Button disabled={selectedSheepsIds.length == 0 ? true : false} onPress={async () => {
                        if (Token) {
                            await addSheeps(); props.navigation.push('TwistingInTheMarket', { weightId: item.weight_id, sheepCategory: item.sheep_category.id, farmId: item.id, reservation: true, item: props.route.params.item, link: link, min: 10, sec: 0 })
                        } else {
                            skipLogin()
                        }
                    }} style={{ ...styles.reserveContainer, ...{ backgroundColor: selectedSheepsIds.length == 0 ? MediumGrey : Red1 } }}>
                        <Text style={{ fontSize: screenWidth / 44, fontFamily: appFontBold, color: White, alignSelf: 'center', textAlignVertical: 'center' }}>{strings('lang.MoveToAddareservation')}</Text>
                    </Button> */}

                    <Button
                        transparent
                        disabled={selectedSheepsIds.length == 0 ? true : false} onPress={async () => {
                            if (Token) {
                                await addSheeps();
                                // props.navigation.push('Cart', { shared: false })
                            } else {
                                skipLogin()
                            }
                            // }} style={{ ...styles.reserveContainer, ...{ backgroundColor: selectedSheepsIds.length == 0 ? Red1 : Red1 } }}>
                        }} style={{ ...styles.reserveContainer, ...{} }}>
                        <Image
                            source={
                                selectedSheepsIds.length == 0 ?
                                    require('../images/newIcon/confirmSheep.png')
                                    :
                                    require('../images/newIcon/activeConfirmSheep.png')
                            }
                            style={{
                                width: Platform.OS == 'ios' ? screenWidth / 5.5 : screenWidth / 5,
                                height: screenWidth / 8.4,
                                position: 'absolute',
                                resizeMode: 'cover'
                            }}
                        // resizeMode="cover"
                        />
                        <Text style={{ fontSize: screenWidth / 44, fontFamily: appFontBold, color: selectedSheepsIds.length == 0 ? Red : White, alignSelf: 'center', textAlignVertical: 'center' }}>{strings('lang.reservationconfirmation')}</Text>
                        {/* </ImageBackground> */}
                    </Button>
                </View>

                <Button
                    transparent
                    onPress={() => {
                        if (link != props.route.params.item.camera_url) {
                            console.log('link', link); console.log('props.route.params.camera_url', props.route.params.item.camera_url); props.navigation.replace('Live',
                                {
                                    item: props.route.params.item,
                                    link: props.route.params.item.camera_url,
                                    screen: props.route.params.screen,
                                    scrollToIndex: props.route.params.scrollToIndex,
                                    logo: props.route.params.logo,
                                    sheepTypeId: props.route.params.sheepTypeId,
                                    weightId: props.route.params.weightId,
                                    sheepCategory: props.route.params.sheepCategory,
                                    sheepCategoryName: props.route.params.sheepCategoryName,
                                    sheepTypeName: props.route.params.sheepTypeName,
                                    sheepWeightName: props.route.params.sheepWeightName,
                                    farmId: props.route.params.farmId,
                                });
                        }
                    }}
                    //  style={[styles.camContainer3, { backgroundColor: link == props.route.params.item.camera_url ? Red : White }]}>
                    style={[styles.camContainer3]}>
                    {link == props.route.params.item.camera_url ?
                        <Image source={require('../images/newIcon/activeCam1.png')} style={{ width: '160%', height: '140%', resizeMode: 'cover', transform: [{ rotate: '90deg' }], }} />
                        :
                        <Image source={require('../images/newIcon/cam1.png')} style={{ width: '160%', height: '140%', resizeMode: 'cover', transform: [{ rotate: '90deg' }], }} />
                    }
                </Button>
            </View>

            <>

                <VLCPlayer
                    source={{
                        initType: 2,
                        hwDecoderEnabled: 1,
                        hwDecoderForced: 1,
                        uri: props.route.params.link,
                        initOptions: [
                            '--no-audio',
                            '--rtsp-tcp',
                            // '--network-caching=50',
                            // '--rtsp-caching=50',
                            '--no-stats',
                            // '--tcp-caching=150',
                            // '--realrtsp-caching=50',
                            // '--avcodec-hw=auto',
                            // Platform.select({ android: '--avcodec-hw=auto', ios: `${'--avcodec-hw=any', '--avcodec-hw-output-format=auto'} ` }),
                            // '--avcodec-hw=any',
                            // '--avcodec-hw-output-format=auto'
                        ],
                    }}
                    style={cameraId == true ? styles.backgroundVideo : { width: 0, height: 0 }}
                    // source={{ uri: linkk }}
                    videoAspectRatio="16:9"
                    isLive={true}
                    autoplay={true}
                    // autoReloadLive={true}
                    // hwDecoderEnabled={1}
                    // hwDecoderForced={1}
                    // onBuffering={handleBuffering}
                    // onPlaying={() => { setIsLoading(false) }}
                    mediaOptions={{
                        // ':network-caching': 150,
                        // ':live-caching': 150,
                        'no-audio': false,
                        'rtsp-tcp': true,
                        ":live-caching": 0,
                        ":file-caching": 0,
                        ":realrtsp-caching": 0,
                        ":live-caching": 0,
                        ":network-caching": 50,
                        ":clock-jitter": 0,
                        ":clock-synchro": 0,
                    }}
                    onError={(err) => console.log("video error:", err)}
                    resizeMode='contain'
                />
                {
                    isLoading ? (
                        <>
                            <View style={{
                                transform: [{ rotate: '90deg' }],
                                position: 'absolute', height: screenWidth, width: screenHeight / 1.5, alignItems: 'center',
                                justifyContent: 'center', top: screenHeight / 4
                            }}>
                                <Text style={{ color: White, marginBottom: 5, fontFamily: appFontBold, fontSize: screenWidth / 35 }}>{'برجاء الانتظار جاري تحميل البث...'}</Text>
                                <CircularProgress3 size={100} strokeWidth={10}
                                    duration={Platform.OS == 'ios' ? 20000 : 6000} />
                            </View>

                        </>
                    ) : (
                        <></>
                    )
                }




                <View style={styles.infoContainer1}>
                    <Image source={require('../images/newIcon/liveIcon.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', }} />

                </View>
                <View style={styles.farmImageContainer}>
                    <Image source={{ uri: item.image }} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
                </View>
            </>


            <View style={{ width: screenWidth / 1.1, height: screenHeight / 12, flexDirection: 'row', marginBottom: screenHeight / 20 }}>
                <Button
                    transparent
                    onPress={() => {
                        if (props.route.params.screen == 'Home') {
                            if (props.route.params.deepLink && props.route.params.deepLink == true) {
                                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                            }
                            else {
                                props.navigation.push('Home', {
                                    scrollToIndex: props.route.params.scrollToIndex,
                                    logo: props.route.params.logo,
                                    sheepTypeId: props.route.params.sheepTypeId,
                                    weightId: props.route.params.weightId,
                                    sheepCategory: props.route.params.sheepCategory,
                                    sheepCategoryName: props.route.params.sheepCategoryName,
                                    sheepTypeName: props.route.params.sheepTypeName,
                                    sheepWeightName: props.route.params.sheepWeightName,
                                    farmId: props.route.params.farmId,
                                    link: props.route.params.item.camera_url,
                                })
                                // props.navigation.goBack()
                            }
                        } else {
                            // props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                            props.navigation.goBack()
                        }
                    }} style={styles.backImageContainer}>
                    <Image source={require('../images/newIcon/back.png')} style={styles.backImage} />
                </Button>
                <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'space-between', marginStart: screenWidth / 3.7, transform: [{ rotate: '90deg' }], flexDirection: 'row', height: screenWidth / 1.5, width: screenHeight / 15, }}>
                    <ScrollView
                        horizontal={false}
                        showsVerticalScrollIndicator={false}
                        style={{ alignSelf: "center", }}
                    >
                        {farms.map((item, index) => {
                            return (

                                <TouchableOpacity
                                    onPress={() => { addSheepToPicked(item) }}
                                    style={{
                                        ...styles.colorContainer, ...{
                                            // backgroundColor: Red1
                                            // backgroundColor: selectedSheepsIds.find(sheep => sheep == item.id) ||
                                            //     selectedSheepsId.find(sheep => sheep == item.id)
                                            //     ? Red : White, borderWidth: 2.5, borderColor: selectedSheepsIds.find(sheep => sheep == item.id) ||
                                            //         selectedSheepsId.find(sheep => sheep == item.id)
                                            //         ? null : item.collar_color
                                        }
                                    }} >
                                    {item.collar_color
                                        ?
                                        <Image source={
                                            item.collar_color == "green" ?
                                                selectedSheepsIds.find(sheep => sheep == item.id) ||
                                                    selectedSheepsId.find(sheep => sheep == item.id) ?
                                                    require('../images/newIcon/activeGreen.png')
                                                    :
                                                    require('../images/newIcon/green.png')
                                                :
                                                item.collar_color == "black"
                                                    ?
                                                    selectedSheepsIds.find(sheep => sheep == item.id) ||
                                                        selectedSheepsId.find(sheep => sheep == item.id) ?
                                                        require('../images/newIcon/activeBlack.png')
                                                        :
                                                        require('../images/newIcon/black.png')
                                                    :
                                                    item.collar_color == "blue"
                                                        ?
                                                        selectedSheepsIds.find(sheep => sheep == item.id) ||
                                                            selectedSheepsId.find(sheep => sheep == item.id) ?
                                                            require('../images/newIcon/activeBlue.png')
                                                            :
                                                            require('../images/newIcon/blue.png')
                                                        :
                                                        item.collar_color == "orange"
                                                            ?
                                                            selectedSheepsIds.find(sheep => sheep == item.id) ||
                                                                selectedSheepsId.find(sheep => sheep == item.id) ?
                                                                require('../images/newIcon/activeOrange.png')
                                                                :
                                                                require('../images/newIcon/orange.png')
                                                            :
                                                            item.collar_color == "gold"
                                                                ?
                                                                selectedSheepsIds.find(sheep => sheep == item.id) ||
                                                                    selectedSheepsId.find(sheep => sheep == item.id) ?
                                                                    require('../images/newIcon/activeGold.png')
                                                                    :
                                                                    require('../images/newIcon/gold.png')
                                                                :
                                                                item.collar_color == "red"
                                                                    ?
                                                                    selectedSheepsIds.find(sheep => sheep == item.id) ||
                                                                        selectedSheepsId.find(sheep => sheep == item.id) ?
                                                                        require('../images/newIcon/activeRed.png')
                                                                        :
                                                                        require('../images/newIcon/red.png')
                                                                    :
                                                                    require('../images/newIcon/red.png')
                                        } style={{ width: '100%', resizeMode: 'contain', height: '100%', }} />
                                        :
                                        <></>
                                    }

                                </TouchableOpacity>

                            )
                        })}

                    </ScrollView>
                </View>
                <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'space-between', zIndex: 1000, transform: [{ rotate: '90deg' }], height: screenWidth / 20, width: screenHeight / 12, marginStart: screenWidth / 4.5 }}>
                    <Button
                        transparent
                        onPress={() => {
                            if (link != props.route.params.item.camera_url2) {
                                console.log('link', link); console.log('props.route.params.camera_url2', props.route.params.item.camera_url2);
                                props.navigation.replace('Live',
                                    {
                                        screen: props.route.params.screen,
                                        item: props.route.params.item,
                                        link: props.route.params.item.camera_url2,
                                        scrollToIndex: props.route.params.scrollToIndex,
                                        logo: props.route.params.logo,
                                        sheepTypeId: props.route.params.sheepTypeId,
                                        weightId: props.route.params.weightId,
                                        sheepCategory: props.route.params.sheepCategory,
                                        sheepCategoryName: props.route.params.sheepCategoryName,
                                        sheepTypeName: props.route.params.sheepTypeName,
                                        sheepWeightName: props.route.params.sheepWeightName,
                                        farmId: props.route.params.farmId,
                                    });
                            }
                        }}
                        style={[styles.camContainer2]}>
                        {/* style={[styles.camContainer2, { backgroundColor: link == props.route.params.item.camera_url2 ? Red : White }]}> */}
                        {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 20, color: link == props.route.params.item.camera_url2 ? White : Red }}>{'2'}</Text>
                        <Image source={require('../images/modrek/cam.png')} style={[styles.camImage1, { tintColor: link == props.route.params.item.camera_url2 ? White : Red }]} /> */}
                        {link == props.route.params.item.camera_url2 ?
                            <Image source={require('../images/newIcon/activeCam2.png')} style={{ width: '160%', height: '140%', resizeMode: 'cover' }} />
                            :
                            <Image source={require('../images/newIcon/cam2.png')} style={{ width: '160%', height: '140%', resizeMode: 'cover', }} />

                        }
                    </Button>
                </View>
            </View>




            <Modal
                transparent={true}
                animationType="fade"
                visible={ModalVisible}
            >

                <View transparent style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                </View>
                {showCopilotId == 1
                    ?
                    <>
                        <View transparent style={{ backgroundColor: '#FFFFFF', opacity: 0.5, top: screenHeight / 1.32, right: '43%', transform: [{ rotate: '90deg' }], position: 'absolute', height: screenWidth / 1.5, width: screenHeight / 15, }} >
                            <View style={{
                                width: screenWidth / 20,
                                height: screenWidth / 20,
                                borderRadius: screenWidth / 40,
                                alignItems: 'center',
                                justifyContent: 'center',
                                backgroundColor: DarkGreen,
                                // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                            }}>
                                <Text style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 35, color: White
                                }}>{showCopilotId}</Text>
                            </View>
                        </View>

                        <View style={[styles.modal, {
                            top: screenHeight / 1.5
                        }]}>
                            <Text style={styles.modaltext}>{strings('lang.step4')}</Text>

                            <View style={{ flexDirection: 'row', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                                <Button style={styles.modalbuttonContainer} onPress={() => { Finish() }}>
                                    <Text style={styles.modalbuttonText}>{strings('lang.Skip')}</Text>
                                </Button>
                                <Button style={[styles.modalbuttonContainer, { backgroundColor: Red }]} onPress={() => { setShowCopilotId(2) }}>
                                    <Text style={styles.modalbuttonText2}>{strings('lang.next')}</Text>
                                </Button>
                            </View>

                        </View>
                    </>

                    :
                    showCopilotId == 2
                        ?
                        <>
                            {[{}, {}].map((item, index) => {
                                return (
                                    <View style={index == 1 ? styles.numberStep2 : styles.numberStep} >
                                        <View style={{
                                            width: screenWidth / 20,
                                            height: screenWidth / 20,
                                            borderRadius: screenWidth / 40,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            backgroundColor: DarkGreen,
                                            // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                                        }}>
                                            <Text style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 35, color: White
                                            }}>{showCopilotId}</Text>
                                        </View>
                                    </View>
                                )
                            })}
                            {[{}, {}].map((item, index) => {
                                return (
                                    <View style={index == 1 ? styles.arrowStep2 : styles.arrowStep} >
                                        <Image source={require('../images/modrek/arrow.png')} style={{
                                            width: '100%', resizeMode: 'contain',
                                            height: '100%', alignSelf: 'center', tintColor: White
                                        }} />
                                    </View>
                                )
                            })}
                            {/* <View transparent style={{
                                backgroundColor: '#FFFFFF', opacity: 0.5,
                                transform: [{ rotate: '90deg' }], position: 'absolute',
                                top: screenHeight / 1.15,
                                right: '1.7%',
                                height: screenWidth / 6,
                                width: screenWidth / 6,
                                borderRadius: screenWidth / 12
                            }} >
                                <View style={{
                                    width: screenWidth / 20,
                                    height: screenWidth / 20,
                                    borderRadius: screenWidth / 40,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: DarkGreen,
                                    // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                                }}>
                                    <Text style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 35, color: White
                                    }}>{showCopilotId}</Text>
                                </View>
                            </View>

                            <View transparent style={{
                                backgroundColor: '#FFFFFF', opacity: 0.5,
                                transform: [{ rotate: '90deg' }], position: 'absolute',
                                bottom: screenHeight / 1.15,
                                right: '1.7%',
                                height: screenWidth / 6,
                                width: screenWidth / 6,
                                borderRadius: screenWidth / 12
                            }} >
                                <View style={{
                                    width: screenWidth / 20,
                                    height: screenWidth / 20,
                                    borderRadius: screenWidth / 40,
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: DarkGreen,
                                    // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                                }}>
                                    <Text style={{
                                        fontFamily: appFontBold,
                                        fontSize: screenWidth / 35, color: White
                                    }}>{showCopilotId}</Text>
                                </View>
                            </View> */}

                            <View style={[styles.modal, {
                                top: screenHeight / 2.4,
                            }]}>
                                <Text style={styles.modaltext}>{strings('lang.step5')}</Text>

                                <View style={{ flexDirection: 'row', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                                    <Button style={styles.modalbuttonContainer} onPress={() => { Finish() }}>
                                        <Text style={styles.modalbuttonText}>{strings('lang.Skip')}</Text>
                                    </Button>
                                    <Button style={[styles.modalbuttonContainer, { backgroundColor: Red }]} onPress={() => { setShowCopilotId(3) }}>
                                        <Text style={styles.modalbuttonText2}>{strings('lang.next')}</Text>
                                    </Button>
                                </View>

                            </View>
                        </>
                        :
                        <>
                            <View transparent style={{
                                backgroundColor: '#FFFFFF', opacity: 0.5,
                                transform: [{ rotate: '90deg' }], position: 'absolute',
                                top: Platform.OS == 'ios' ? 18 : -5,
                                right: '37%',
                                height: screenWidth / 3,
                                width: screenWidth / 5,
                            }} >

                                {[{}, {}, {}].map((item, index) => {
                                    return (
                                        <View style={{
                                            width: screenWidth / 20,
                                            height: screenWidth / 20,
                                            borderRadius: screenWidth / 40,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            alignSelf: 'flex-end',
                                            backgroundColor: DarkGreen,
                                            marginTop: index == 0 ? 0 : index == 1 ? 20 : 30
                                            // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                                        }}>
                                            <Text style={{
                                                fontFamily: appFontBold,
                                                fontSize: screenWidth / 35, color: White
                                            }}>{showCopilotId + index}</Text>
                                        </View>
                                    )
                                })}
                            </View>



                            <View style={[styles.modal, {
                                top: screenHeight / 4,
                            }]}>
                                <Text style={[styles.modaltext, { textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{`3-${'من هنا تقدر تطلب الراعي يحرك ويوضح الاغنام .'}`} </Text>
                                <Text style={[styles.modaltext, { textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{`4-${strings('lang.step8')}`} </Text>
                                {/* <Text style={[styles.modaltext, { textAlign: I18nManager.isRTL ? 'left' : 'right' }]}>{`5-${strings('lang.step8')}`} </Text> */}

                                <View style={{ flexDirection: 'row', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                                    <Button style={styles.modalbuttonContainer} onPress={() => { Finish() }}>
                                        <Text style={styles.modalbuttonText}>{strings('lang.Skip')}</Text>
                                    </Button>
                                    <Button style={[styles.modalbuttonContainer, { backgroundColor: Red }]} onPress={() => { Finish() }}>
                                        <Text style={styles.modalbuttonText2}>{strings('lang.ending')}</Text>
                                    </Button>
                                </View>

                            </View>
                        </>
                }

            </Modal>

            <RBSheet
                ref={refRbSheet}
                height={screenHeight / 3.5}
                openDuration={250}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        backgroundColor: Red,
                        alignItems: "center",
                        // borderTopEndRadius: 25,
                        // borderTopStartRadius: 25,
                        width: '100%',
                        alignSelf: 'center',
                        transform: [{ rotate: '90deg' }],
                        marginBottom: screenHeight / 3,
                        borderRadius: 30,
                        overflow: 'hidden',
                        // position: 'absolute',
                        // top: screenHeight / 2.5
                        // bottom: '25%'
                    },
                    wrapper: {

                    },
                    // draggableIcon: {
                    //     width: '25%',
                    //     backgroundColor: Red1
                    // }
                }}

                closeOnDragDown={false}
                closeOnPressMask={false}
                closeOnPressBack={false}

                onClose={() => {
                    setSharingId('')
                }
                }
            >

                <>
                    {/* <View transparent style={{ backgroundColor: '#FFFFFF', opacity: 0.5, top: screenHeight / 1.32, right: '43%', transform: [{ rotate: '90deg' }], position: 'absolute', height: screenWidth / 1.5, width: screenHeight / 15, }} >
                        <View style={{
                            width: screenWidth / 20,
                            height: screenWidth / 20,
                            borderRadius: screenWidth / 40,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: DarkGreen,
                            // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                        }}>
                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 35, color: White
                            }}>{showCopilotId}</Text>
                        </View>
                    </View> */}

                    {/* <View style={[styles.modalSheepShare, {

                    }]}> */}

                    <ImageBackground
                        source={

                            require('../images/newIcon/backgroundModal.png')
                        }
                        style={{
                            flex: 1,
                            width: '100%',
                            height: '100%',
                            alignItems: 'center',
                            justifyContent: 'center',
                            overflow: 'hidden',     // Important to clip the image

                            // position: 'absolute',
                            // resizeMode: 'cover'
                        }}
                        imageStyle={{
                            borderRadius: 30        // Very important to make image follow border
                        }}
                        resizeMode="cover"
                    >
                        <View style={{
                            padding: 10
                        }}>

                            {sharingId == 'half'
                                ?
                                <Text style={styles.modaltextSheepShare}>{strings('lang.messageShare1')}</Text>
                                :
                                <Text style={styles.modaltextSheepShare}>{strings('lang.messageShare2')}</Text>
                            }

                            <View style={{ flexDirection: 'row', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                                <Button
                                    transparent
                                    style={styles.modalbuttonContainerSheepShare} onPress={() => {
                                        refRbSheet.current.close()

                                    }}>
                                    <Image
                                        source={

                                            require('../images/newIcon/cancelModal.png')

                                        }
                                        style={{
                                            width: screenHeight / 6.3,
                                            height: screenWidth / 12,
                                            position: 'absolute',
                                            resizeMode: 'cover'
                                        }}
                                    // resizeMode="cover"
                                    />
                                    <Text style={styles.modalbuttonTextSheepShare}>{strings('lang.cancel')}</Text>
                                </Button>
                                <Button
                                    transparent
                                    style={[styles.modalbuttonContainerSheepShare, {}]} onPress={() => { addSheepsShareType2() }}>
                                    <Image
                                        source={

                                            require('../images/newIcon/confirmModal.png')

                                        }
                                        style={{
                                            width: screenHeight / 6.3,
                                            height: screenWidth / 12,
                                            position: 'absolute',
                                            resizeMode: 'cover'
                                        }}
                                    // resizeMode="cover"
                                    />
                                    <Text style={styles.modalbuttonText2SheepShare}>{strings('lang.Transfertoreservation')}</Text>
                                </Button>
                            </View>
                        </View>
                    </ImageBackground>


                </>

            </RBSheet>

            <Modal
                transparent={true}
                animationType="fade"
                visible={ModalVisibleSheep}
            >

                <View transparent style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                </View>
                <>
                    {/* <View transparent style={{ backgroundColor: '#FFFFFF', opacity: 0.5, top: screenHeight / 1.32, right: '43%', transform: [{ rotate: '90deg' }], position: 'absolute', height: screenWidth / 1.5, width: screenHeight / 15, }} >
                        <View style={{
                            width: screenWidth / 20,
                            height: screenWidth / 20,
                            borderRadius: screenWidth / 40,
                            alignItems: 'center',
                            justifyContent: 'center',
                            backgroundColor: DarkGreen,
                            // top: screenHeight / 1.32, right: '43%', position: 'absolute', zIndex: 300, transform: [{ rotate: '90deg' }],
                        }}>
                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 35, color: White
                            }}>{showCopilotId}</Text>
                        </View>
                    </View> */}

                    <View style={[styles.modalSheep, {

                    }]}>
                        <ImageBackground
                            source={

                                require('../images/newIcon/backgroundModal.png')
                            }
                            style={{
                                flex: 1,
                                width: '100%',
                                height: '100%',
                                alignItems: 'center',
                                justifyContent: 'center',
                                overflow: 'hidden',     // Important to clip the image
                                // position: 'absolute',
                                // resizeMode: 'cover'
                            }}
                            imageStyle={{
                                borderRadius: 30        // Very important to make image follow border
                            }}

                            resizeMode="cover"
                        >
                            <Text style={styles.modaltextSheep}>{strings('lang.moveMessage')}</Text>

                            <View style={{ flexDirection: 'row', alignSelf: 'center', marginTop: 20, alignItems: 'center', justifyContent: 'space-between', width: '70%', }}>
                                <Button
                                    transparent
                                    style={styles.modalbuttonContainerSheep}
                                    onPress={() => { setModalVisibleSheep(!ModalVisibleSheep) }}>
                                    <Image
                                        source={
                                            require('../images/newIcon/cancelModal.png')
                                        }
                                        style={{
                                            width: screenHeight / 7,
                                            height: screenWidth / 12,
                                            position: 'absolute',
                                            resizeMode: 'cover'
                                        }}
                                    // resizeMode="cover"
                                    />
                                    <Text style={styles.modalbuttonTextSheep}>{strings('lang.cancel')}</Text>
                                </Button>
                                <Button
                                    transparent
                                    style={[styles.modalbuttonContainerSheep, {}]} onPress={() => { moveSheep() }}>
                                    <Image
                                        source={

                                            require('../images/newIcon/confirmModal.png')

                                        }
                                        style={{
                                            width: screenHeight / 7,
                                            height: screenWidth / 12,
                                            position: 'absolute',
                                            resizeMode: 'cover'
                                        }}
                                    // resizeMode="cover"
                                    />
                                    <Text style={styles.modalbuttonText2Sheep}>{strings('lang.Confirm')}</Text>
                                </Button>
                            </View>
                        </ImageBackground>
                    </View>
                </>

            </Modal>

        </View >
    )
}


export default copilot({
    tooltipComponent: MyComponent, // Set the custom tooltip component
    overlay: 'view',
    // animated: true,
    // backdropColor: Black,
    androidStatusBarVisible: true,
    // stopOnOutsideClick: true
})(Live);
const styles = StyleSheet.create({
    numberStep: {
        backgroundColor: '#FFFFFF', opacity: 0.5,
        transform: [{ rotate: '90deg' }], position: 'absolute',
        top: screenHeight / 1.15,
        right: '1.7%',
        height: screenWidth / 6,
        width: screenWidth / 6,
        borderRadius: screenWidth / 12
    },
    numberStep2: {
        backgroundColor: '#FFFFFF', opacity: 0.5,
        transform: [{ rotate: '90deg' }], position: 'absolute',
        bottom: screenHeight / 1.155,
        right: '1.6%',
        height: screenWidth / 6,
        width: screenWidth / 6,
        borderRadius: screenWidth / 12
    },
    arrowStep: {
        // backgroundColor: '#FFFFFF',
        transform: [{ rotate: '180deg' }], position: 'absolute',
        alignItems: 'center', justifyContent: 'center',
        top: screenHeight / 1.13,
        right: '20%',
        height: screenWidth / 10,
        width: screenWidth / 10,
    },
    arrowStep2: {
        // backgroundColor: '#FFFFFF', opacity: 0.5,
        transform: [{ rotate: '180deg' }], position: 'absolute',
        alignItems: 'center', justifyContent: 'center',
        bottom: screenHeight / 1.13,
        right: '20%',
        height: screenWidth / 10,
        width: screenWidth / 10,
    },
    modal: {
        flex: 1,
        alignItems: 'center',
        width: Platform.OS == 'ios' ? screenHeight / 4 : screenHeight / 3.6,
        // height: screenWidth / 2,
        position: 'absolute',
        transform: [{ rotate: '90deg' }],

        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '5%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 40,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: '35%',
        height: screenWidth / 12,
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    // modalbuttonContainer2: {
    //     width: '35%',
    //     height: '50%',
    //     backgroundColor: Red,
    //     alignSelf: 'center',
    //     marginHorizontal: 5,
    //     justifyContent: 'center',
    //     borderRadius: 10,
    //     alignItems: 'center',
    //     padding: 12,
    //     marginTop: 7,
    // },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 35
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 35
    },
    modalSheep: {
        flex: 1,
        alignItems: 'center',
        width: screenHeight / 2,
        height: screenWidth / 1.5,
        position: 'absolute',
        transform: [{ rotate: '90deg' }],
        top: screenHeight / 3,
        alignSelf: 'center',
        // backgroundColor: Red1,
        // borderWidth: 1, borderColor: Black,
        // borderRadius: 10,
        paddingVertical: '5%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltextSheep: {
        fontFamily: appFontTajawal,
        color: White,
        fontSize: screenWidth / 28,
        alignSelf: 'center',
        marginTop: '0%',
        marginBottom: '5%',
        textAlign: 'center'
    },
    modalbuttonContainerSheep: {
        width: '35%',
        height: screenWidth / 12,
        // backgroundColor: Red1,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        // marginStart: 7,
    },

    modalbuttonText2Sheep: {
        color: White,
        fontFamily: appFontTajawal,
        fontSize: screenWidth / 25
    },
    modalbuttonTextSheep: {
        color: Red,
        fontFamily: appFontTajawal,
        fontSize: screenWidth / 25
    },
    modalSheepShare: {
        flex: 1,
        alignItems: 'center',
        width: '100%',
        // height: screenWidth / 2,
        // position: 'absolute',
        // transform: [{ rotate: '90deg' }],
        // top: screenHeight / 2.4,
        alignSelf: 'center',
        backgroundColor: Red,
        // borderWidth: 1, borderColor: Black,
        borderRadius: 10,
        paddingVertical: '5%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltextSheepShare: {
        fontFamily: appFontTajawal,
        color: White,
        fontSize: screenWidth / 25,
        alignSelf: 'center',
        marginBottom: '10%',
        textAlign: 'center'
    },
    modalbuttonContainerSheepShare: {
        width: '45%',
        height: screenWidth / 12,
        // backgroundColor: Red1,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        // borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        // borderWidth: 1,
        // borderColor: Red,
    },

    modalbuttonText2SheepShare: {
        color: White,
        fontFamily: appFontTajawal,
        fontSize: screenWidth / 30
    },
    modalbuttonTextSheepShare: {
        color: Red,
        fontFamily: appFontTajawal,
        fontSize: screenWidth / 30
    },
    colorContainer: {
        width: screenHeight / 16,
        height: screenHeight / 16,
        alignSelf: 'center',
        marginHorizontal: 10,
        borderRadius: screenHeight / 10,
        borderWidth: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginVertical: 4
    },
    container: {
        flex: 1,
        alignItems: 'center',
        backgroundColor: 'white',
    },
    backgroundVideo: {
        // flex: 1,
        // backgroundColor: '#02161D',
        // backgroundColor: '#02161D',
        width: Platform.OS == 'ios' ? screenHeight / 1.4 : screenHeight / 1.35,
        height: screenWidth / 1,
        // position: 'absolute',
        top: -screenHeight / 100,
        // left: 0,
        // bottom: 0,
        // right: 0,
        transform: [{ rotate: '90deg' }],
        // top: -screenHeight / 20,
        // zIndex: 1000000000
    },

    backImage: {
        width: "50%", height: "70%", tintColor: Red, resizeMode: "contain", alignSelf: 'center',
    },
    infoContainer: {
        width: screenWidth / 5,
        height: screenWidth / 15, marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: DarkGrey, borderRadius: 10,
    },
    infoContainer1: {
        width: screenWidth / 5,
        height: screenWidth / 15, marginLeft: 10,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, position: 'absolute', zIndex: 10000, top: screenHeight / 6, start: 0
    },
    farmImageContainer: {
        width: screenWidth / 8,
        height: screenWidth / 8, backgroundColor: Red, marginLeft: screenWidth / 17, borderRadius: 20, overflow: 'hidden',
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }], marginTop: Platform.OS == 'ios' ? screenWidth / 7 : screenWidth / 14, backgroundColor: White, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 100, start: 0
    },
    reserveContainer: {
        width: Platform.OS == 'ios' ? screenWidth / 5.5 : screenWidth / 5,
        height: screenWidth / 11,
        // marginLeft: I18nManager.isRTL ? '5%' : '0%',
        // marginRight: I18nManager.isRTL ? '0%' : '5%',
        // borderRadius: 10, borderWidth: 0, borderColor: White,
        // backgroundColor: '#A30000',
        // position: 'absolute',
        // bottom: screenHeight / 5,
        // start: screenWidth / 1.35,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'flex-end',
        marginVertical: '4%'
        // borderWidth: 1,
        // borderColor: '#A30000'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer: {
        width: screenWidth / 9,
        height: screenWidth / 9,
        borderRadius: screenWidth / 15,
        backgroundColor: Red,
        // position: 'absolute',
        // bottom: screenHeight / 20,
        // start: screenWidth / 1.3,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer2: {
        width: screenWidth / 8.5,
        height: screenWidth / 8.5,
        // borderRadius: screenWidth / 15,
        // backgroundColor: Red,
        // position: 'absolute',
        // top: screenHeight / 15,
        // start: screenWidth / 1.3,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center'
        // transform: [{ rotate: '90deg' }]
    },
    camContainer3: {
        width: screenWidth / 8.5,
        height: screenWidth / 8.5,
        // borderRadius: screenWidth / 15,
        // backgroundColor: Red,
        // position: 'absolute',
        // top: screenHeight / 15,
        // start: screenWidth / 1.3,
        alignItems: 'center',
        justifyContent: 'center',
        alignSelf: 'center', marginStart: screenWidth / 4.4, marginTop: screenHeight / 25
        // transform: [{ rotate: '90deg' }]
    },
    backImageContainer: {
        width: screenWidth / 10,
        height: screenWidth / 10,
        // backgroundColor: White,
        // position: 'absolute',
        // bottom: screenHeight / 30,
        // start: I18nManager.isRTL ? screenWidth / 25 : screenWidth / 1.20,
        alignItems: 'center',
        justifyContent: 'center',
        transform: [{ rotate: '90deg' }],
        // borderWidth: 1,
        // borderColor: MediumGrey,
        alignSelf: 'center'
    },
    backImage: {
        width: "100%", height: "130%", resizeMode: "cover", alignSelf: 'center',
    },
    camImage: {
        width: "40%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ rotate: '90deg' }], marginTop: 5
    },
    camImage1: {
        width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ scaleX: -1 }]
    },
});
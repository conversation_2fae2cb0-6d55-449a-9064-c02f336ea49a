import React, { useCallback, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, Red, Black, appColor2, Red1 } from '../components/Styles';
import { Button, Textarea, Toast } from 'native-base';
import { ScrollView } from 'react-native-gesture-handler';
import MyFooter from '../components/MyFooter';
import Header from '../components/Header';
import { strings } from './i18n';
import SelectDropdown from 'react-native-select-dropdown'
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import Toaster from '../components/Toaster';
import * as addressesActions from '../../Store/Actions/addresses';
import * as cartActions from '../../Store/Actions/cart';
import { forModalPresentationIOS } from '@react-navigation/stack/lib/typescript/src/TransitionConfigs/CardStyleInterpolators';
import { useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import BackgroundTimer from 'react-native-background-timer';


const AddressDetails = props => {

    const [Cities, setCities] = useState([]);
    const [cityId, setCityId] = useState('');
    const [error_cityId, setError_cityId] = useState('');
    const [cityName, setCityName] = useState('');
    const [Areas, setAreas] = useState([]);
    const [areaId, setAreaId] = useState('');
    const [error_areaId, setError_areaId] = useState('');
    const [areaName, setAreaName] = useState('');
    const [building, setBuilding] = useState('');
    const [floor, setFloor] = useState('');
    const [apartment, setApartment] = useState('');
    const [phone, setPhone] = useState('');
    const [error_phone, setError_phone] = useState('');
    const [specialMark, setSpecialMark] = useState('');
    const [error_specialMark, setError_specialMark] = useState('');
    const [error_building, setError_building] = useState('');
    const [lat, setLat] = useState(0);
    const [lng, setLng] = useState(0);
    const [ID, setID] = useState('');
    const [addressDesription, setAddressDesription] = useState('');
    const cartt = useSelector(state => state.cart.cart)
    const [cartItems, setCartItems] = useState(cartt.sheep);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();
    const citiesDropdownRef = useRef();
    const userPhone = useSelector(state => state.auth.user.phone)

    const [timeLeft, setTimeLeft] = useState(props.route.params.timeLeft); // 10 minutes in seconds
    const timerRef = useRef(null);

    // Start Timer Function
    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };
    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart())
    };
    // Stop Timer Function
    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };

    // Reset Timer to 10 minutes
    const resetTimer = () => {
        setTimeLeft(props.route.params.timeLeft);
    };

    // Start timer when screen is focused & Reset when leaving
    useFocusEffect(
        useCallback(() => {
            resetTimer(); // Reset every time you open the screen
            startTimer();

            // Cleanup when screen is unfocused (leaving screen)
            return () => {
                stopTimer();
                resetTimer(); // Reset when leaving the screen
            };
        }, [])
    );

    useEffect(() => {
        let lat = props.route.params.lat
        setLat(lat)
        console.log(lat);
        let lng = props.route.params.lng
        console.log(lng);
        setPhone(userPhone)

        setLng(lng)
        let addressDesription = props.route.params.addressDesription
        console.log(addressDesription);
        setAddressDesription(addressDesription)
        const getAreas = async () => {
            try {
                setLoading(true)
                let response = await dispatch(addressesActions.getAreas());
                if (response.success == true) {
                    setAreas(response.data);
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getAreas()
    }, []);

    const createAddresss = async () => {
        if (addressDesription == '' || phone == '') {
            if (cityId == '') {
                setError_cityId(strings('lang.Please_insert_field'));
            }
            if (phone == '') {
                setError_phone(strings('lang.Please_insert_phone'));
            }
            // if (specialMark == '') {
            //     setError_specialMark(strings('lang.Please_insert_street_name'));
            // }
            // if (building == '') {
            //     setError_building(strings('lang.PleaseInsertBuildingName'));
            // }
        }
        else {
            setLoadingMore(true);
            let AddAddress = await dispatch(addressesActions.createAddresss({
                lat: lat,
                lng: lng,
                address: addressDesription,
                phone: phone,
                building: building,
                floor: floor,
                apartment: apartment,
                specialMark: specialMark,
                // areaId: areaId,
            }));
            if (AddAddress.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    (strings('lang.Youraddresshasbeenaddedsuccessfully')),
                    White,
                    1500,
                    screenHeight / 15,
                );
                setAddressDesription('')
                setBuilding('')
                setFloor('')
                setApartment('')
                setPhone('')
                setSpecialMark('')
                setAddress(AddAddress.data)
                console.log('props.route.params.screen', props.route.params.screen);
                if (props.route.params.screen == 'CompleteOrder1') {
                    props.navigation.push('CompleteOrder1', { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
                }
                else {
                    props.navigation.push('Addresses', { screen: props.route.params.screen, shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })
                }
            }

            setLoadingMore(false);
        }
    }
    // console.log('ssss', createAddresss);

    const setAddress = async (address) => {
        AsyncStorage.setItem("address", JSON.stringify(address));

        try {
            let response = await dispatch(cartActions.selectAddress(address.id));
            if (response.success == true) {
            }
            else {
                if (response.message) {
                }
            }
        }
        catch (err) {
            console.log('err', err)
        }
    }

    const setCity = (item) => {
        setCities(item.cities)
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart') }} title={strings('lang.AddressDetails')}
                    backPress={() => { props.navigation.push('Map', { screen: props.route.params.screen, shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }}
                />

                {loadingMore ? <LoadingMore /> : <></>}


                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start',
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start',
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start',
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '75%', paddingHorizontal: '2%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 12,
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 5,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 25,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 50, flexDirection: 'row', width: '90%', justifyContent: 'space-between', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 2.2,
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 2.2,
                                    height: screenHeight / 18,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start',
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start',
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '90%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 30, flexDirection: 'row', width: '95%', justifyContent: 'space-between', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 2.2,
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 20,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 2.2,
                                    height: screenHeight / 18,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart') }} title={strings('lang.AddressDetails')}
                    backPress={() => { props.navigation.push('Map', { screen: props.route.params.screen, shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }}
                />
                {loadingMore
                    ?
                    <LoadingMore />
                    :
                    <></>
                }
                <ScrollView showsVerticalScrollIndicator={false} style={{ width: '90%', alignSelf: "center" }}>
                    <View style={[styles.labelContainer, { marginTop: '10%' }]}>
                        <Text numberOfLines={1} style={styles.textDarkGrey}>{strings('lang.addressD')}</Text>
                        <Text numberOfLines={1} style={styles.textRed}> * </Text>
                    </View>


                    <View style={[styles.contentContainer, {
                        borderRadius: 25, borderColor: MediumGrey, borderWidth: 1, paddingHorizontal: '5%'
                    }]}>
                        <Text numberOfLines={1} style={styles.textDarkGrey}>{addressDesription}</Text>
                    </View>
                    {error_cityId ? (
                        <Text style={styles.loginError}>{error_cityId}</Text>
                    ) : (
                        <View style={{ height: 0 }}></View>
                    )}

                    <View style={styles.labelContainer}>
                        <Text numberOfLines={1} style={styles.textDarkGrey}>{strings('lang.Building')}</Text>
                        <Text numberOfLines={1} style={[styles.textRed, {
                            color: MediumGrey, fontSize: screenWidth / 35, marginStart: 5
                        }]}>{[`(${strings('lang.Mychoice')})`]}</Text>
                    </View>
                    <View style={styles.contentContainer}>
                        <Textarea
                            onChangeText={(text) => { setBuilding(text); setError_building('') }}
                            value={building}
                            placeholderTextColor={'#535353'}
                            style={styles.textInput}
                        />
                    </View>
                    {error_building ? (
                        <Text style={styles.loginError}>{error_building}</Text>
                    ) : (
                        <View style={{ height: 0 }}></View>
                    )}

                    <View style={styles.labelsContainer}>
                        <View style={{ width: '58%' }}>
                            <Text numberOfLines={1} style={styles.textDarkGrey}>{strings('lang.Floor')} {' '}
                                <Text numberOfLines={1} style={[styles.textRed, {
                                    color: MediumGrey, fontSize: screenWidth / 35, marginStart: 5
                                }]}>{[`(${strings('lang.Mychoice')})`]}</Text>
                            </Text>

                        </View>

                        <View style={{ width: '42%' }}>
                            <Text numberOfLines={1} style={styles.textDarkGrey}>{strings('lang.apartment')} {' '}
                                <Text numberOfLines={1} style={[styles.textRed, {
                                    color: MediumGrey, fontSize: screenWidth / 35, marginStart: 5
                                }]}>{[`(${strings('lang.Mychoice')})`]}</Text>
                            </Text>

                        </View>
                    </View>

                    <View style={styles.contentContainerSmall}>
                        <Textarea
                            onChangeText={(text) => setFloor(text)}
                            value={floor}
                            placeholderTextColor={'#535353'}
                            style={styles.textInputSmall}
                        />
                        <Textarea
                            onChangeText={(text) => setApartment(text)}
                            value={apartment}
                            placeholderTextColor={'#535353'}
                            style={styles.textInputSmall}
                        />
                    </View>

                    <View style={styles.labelContainer}>
                        <Text numberOfLines={1} style={styles.textDarkGrey}>{strings('lang.MobileNumber')}</Text>
                        <Text numberOfLines={1} style={styles.textRed}> * </Text>
                    </View>
                    <View style={styles.contentContainer}>
                        <Textarea
                            onChangeText={(text) => { setPhone(text); setError_phone('') }}
                            value={phone}
                            placeholderTextColor={'#535353'}
                            placeholder={'05X XXX XXXX'}
                            style={styles.textInput}
                        />
                    </View>
                    {error_phone ?
                        <Text style={styles.loginError}>{error_phone}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <View style={styles.labelContainer}>
                        <Text numberOfLines={1} style={styles.textDarkGrey}>{strings('lang.special_marque')}</Text>
                        <Text numberOfLines={1} style={[styles.textRed, {
                            color: MediumGrey, fontSize: screenWidth / 35, marginStart: 5
                        }]}>{[`(${strings('lang.Mychoice')})`]}</Text>
                    </View>
                    <View style={styles.contentContainer}>
                        <Textarea
                            onChangeText={(text) => { setSpecialMark(text); setError_specialMark('') }}
                            value={specialMark}
                            placeholderTextColor={'#535353'}
                            style={styles.textInput}
                        />
                    </View>
                    {error_specialMark ? (
                        <Text style={styles.loginError}>{error_specialMark}</Text>
                    ) : (
                        <View style={{ height: 0 }}></View>
                    )}

                    <View style={{ width: '90%', flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: 'space-between', alignSelf: "center", marginTop: screenHeight / 30 }}>
                        <Button
                            onPress={() => { createAddresss() }}
                            style={{ flexDirection: 'row', alignItems: "center", justifyContent: "center", width: '48%', height: screenHeight / 18, backgroundColor: Red, borderRadius: 25 }}>
                            <Image source={require('../images/modrek/7669.png')} style={{ resizeMode: 'contain', width: '10%', marginHorizontal: 5, height: '65%', tintColor: White }} />
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: White, }}>{strings('lang.Add')}</Text>
                        </Button>
                        <Button onPress={() => { props.navigation.goBack() }} style={{ flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignItems: "center", justifyContent: "center", width: '48%', height: screenHeight / 18, borderColor: WhiteGery, borderWidth: 1, elevation: 0, borderRadius: 25, backgroundColor: WhiteGery }}>
                            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 28, color: Black, }}>{strings('lang.back')}</Text>
                            <Image source={I18nManager.isRTL ? require('../images/Silal/svgexport-66.png') : require('../images/Silal/svgexport-67.png')} style={{ resizeMode: 'contain', width: '15%', height: '60%', tintColor: Black, justifyContent: 'center', top: '1.4%' }} />
                        </Button>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView>

                {/* <MyFooter navigation={this.props.navigation} /> */}
            </View>
        )
    }
}

export default AddressDetails;
const styles = StyleSheet.create({
    labelContainer: { width: "90%", alignSelf: 'center', alignItems: 'center', flexDirection: 'row', marginTop: '5%' },
    labelsContainer: { width: "90%", alignSelf: 'center', justifyContent: 'space-between', flexDirection: 'row', marginTop: '5%' },
    icon: { width: '5%', height: '40%', tintColor: MediumGrey, resizeMode: 'contain', marginHorizontal: '7%' },
    textDarkGrey: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, alignSelf: 'flex-start' },
    textRed: { fontFamily: appFontBold, color: Red1, fontSize: screenWidth / 26 },
    contentContainer: {
        width: "90%", alignSelf: "center", justifyContent: "center", height: screenHeight / 18,
        borderRadius: 25, paddingHorizontal: 0,
    },
    textInput: {
        width: "100%", alignSelf: "center", borderRadius: 25,
        paddingHorizontal: '5%', borderColor: MediumGrey,
        borderWidth: 1, fontFamily: appFont, height: '100%', textAlignVertical: "center",
        fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    contentContainerSmall: { width: "90%", alignSelf: "center", justifyContent: 'space-between', marginBottom: 20, height: screenHeight / 18, borderRadius: 25, paddingHorizontal: 0, flexDirection: 'row' },
    textInputSmall: { width: "48%", alignSelf: "center", borderRadius: 25, paddingHorizontal: '5%', borderColor: MediumGrey, borderWidth: 1, fontFamily: appFont, height: '100%', textAlignVertical: "center", fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'right' : 'left' },
    loginError: {
        color: Red1, fontFamily: appFont, alignSelf: 'center', fontSize: screenWidth / 33,
    },
});
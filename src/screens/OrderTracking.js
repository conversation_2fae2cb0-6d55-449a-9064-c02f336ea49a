import React, { useRef, useState } from 'react';
import { View, Animated, Text, Image, Modal, ImageBackground, StyleSheet, I18nManager, Alert, RefreshControl, Platform, Linking } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor1, Black, appColor2, LightGreen, Red, Red1, DarkYellow } from '../components/Styles';
import { Button, Input, Textarea, Toast } from 'native-base';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
import { KeyboardAwareScrollView, ScrollableComponent } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import LoadingMore from '../components/LoadingMore';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as ordersActions from '../../Store/Actions/orders';
import Toaster from '../components/Toaster';
import StarRating from 'react-native-star-rating';
import Loading from '../components/Loading';
import ImageViewer from 'react-native-image-zoom-viewer';
import QrCode from '../components/QrCode';
import OrderDetailsModal from '../components/OrderDetailsModal';

const OrderTracking = props => {

    const [link, setLink] = useState('');
    const [review, setReview] = useState('');
    const [rating, setRating] = useState(0);
    const [rating1, setRating1] = useState(0);
    const statusess = useSelector(state => state.settings.statuses)
    const [statuses, setStatuses] = useState([]);
    const [orderStatuses, setOrderStatuses] = useState([]);
    const [order, setOrder] = useState({});
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [Reload, setReload] = useState(false);
    const [IsOpen, setIsOpen] = useState(false);
    const [IsLoad, setIsLoad] = useState(false);
    const [ratingSheep, setRatingSheep] = useState(false);

    const dispatch = useDispatch();
    const refRbSheet = useRef();
    const refRbSheetCheck = useRef();
    const refRbSheetImages = useRef();
    const [visible, setIsVisible] = useState(false);
    const [sheepImages, setSheepImages] = useState([]);
    const refRBSheet1 = useRef();
    const [modalVisible, setModalVisible] = useState(false);
    const refRBSheetCode = useRef();
    const [sheepCode, setSheepCode] = useState('');

    const [imageUrl, setImageUrl] = useState('https://iiif.wellcomecollection.org/image/A0000824/full/full/0/default.jpg');
    let intervalTime = IsLoad ? 100 : 40000;


    useEffect(() => {
        console.log('props.route.params.item', props.route.params.item);
        const getCurrentOrder = async () => {
            try {
                if (IsOpen) return;
                setLoading(true)
                // }
                let response = await dispatch(ordersActions.getOrder(props.route.params.item.id));
                if (response.success == true) {
                    setOrder(response.data)
                    setLink(response.data.sheep && response.data.sheep[0].sheep && response.data.sheep[0].sheep.farm && response.data.sheep[0].sheep.farm.camera_url)
                    setStatuses(statusess ? statusess : [])
                    console.log('order.sheep', response.data.sheep);
                    setOrderStatuses(response.data.statuses)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getCurrentOrder();
        opacityy();


        if (IsOpen == false) {
            const interval = setInterval(() => {

                console.log('statusess', statusess);
                getCurrentOrder();
                opacityy();
                setIsLoad(false)
            }, intervalTime); // 60000ms = 1 minute
            // Cleanup interval on component unmount
            return () => clearInterval(interval);
        } else {

        }

    }, [IsLoad, IsOpen, opacity]);
    const opacity = useRef(new Animated.Value(1)).current; // Initialize opacity as an Animated Value

    const opacityy = () => {
        const fadeAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 0.5, // Target opacity
                    duration: 1000, // Duration in ms
                    useNativeDriver: true, // Optimize for native performance
                }),
                Animated.timing(opacity, {
                    toValue: 1, // Reset to full opacity
                    duration: 1000, // Duration in ms
                    useNativeDriver: true,
                }),
            ])
        );

        fadeAnimation.start(); // Start the animation

        return () => fadeAnimation.stop();
    }

    const onRefresh = async () => {
        try {
            setLoading(true)
            let response = await dispatch(ordersActions.getOrder(props.route.params.item.id));
            if (response.success == true) {
                setOrder(response.data)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    }

    const openMap = async () => {
        const scheme = Platform.select({ ios: 'maps:0,0?q=', android: 'geo:0,0?q=' });
        const latLng = `${order.address.lat},${order.address.lng}`;
        const label = 'Custom Label';
        const url = Platform.select({
            ios: `${scheme}${label}@${latLng}`,
            android: `${scheme}${latLng}(${label})`
        });
        Linking.openURL(url);
    }
    const openMappartner = async () => {
        const scheme = Platform.select({ ios: 'maps:0,0?q=', android: 'geo:0,0?q=' });
        const latLng = `${order.partner_address.lat},${order.partner_address.lng}`;
        const label = 'Custom Label';
        const url = Platform.select({
            ios: `${scheme}${label}@${latLng}`,
            android: `${scheme}${latLng}(${label})`
        });
        Linking.openURL(url);
    }

    const addRating = async () => {
        if (rating == 0 && rating1 == 0 && review == '' && ratingSheep.sheep == null) {
            Toaster(
                'top',
                'danger',
                Red1,
                (strings('lang.Putyourrating')),
                White,
                1500,
                screenHeight / 15,
            );
        }
        else {
            setLoadingMore(true);

            let response = await dispatch(ordersActions.RateOrder(ratingSheep.sheep.id, rating, rating1, review));

            if (response.success == true) {
                Toaster(
                    'top',
                    'success',
                    DarkGreen,
                    (strings('lang.Evaluationcompletedsuccessfully')),
                    White,
                    1500,
                    screenHeight / 15,
                );
                setRating(0)
                setRating1(0)
                setReview('')
                getCurrentOrder()
                setIsOpen(false)
                setIsLoad(true)
                refRbSheet.current.close();
                setLoadingMore(false);
                setRatingSheep(null)
            } else {
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    response.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
                setLoadingMore(false);

            }

            setLoadingMore(false);
        }
    }

    const getCurrentOrder = async () => {
        try {
            setLoadingMore(true)
            let response = await dispatch(ordersActions.getOrder(props.route.params.item.id));
            if (response.success == true) {
                // setOrder(response.data)
                // setLink(response.data.sheep && response.data.sheep[0].sheep && response.data.sheep[0].sheep.farm && response.data.sheep[0].sheep.farm.camera_url)

                // console.log('order.sheep', order.sheep);
                // setOrderStatuses(response.data.statuses)
                setOrder(response.data)
                setLink(response.data.sheep && response.data.sheep[0].sheep && response.data.sheep[0].sheep.farm && response.data.sheep[0].sheep.farm.camera_url)
                setStatuses(statusess ? statusess : [])
                console.log('order.sheep', response.data.sheep);
                setOrderStatuses(response.data.statuses)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false);
        } catch (err) {
            console.log('err', err)
            setLoadingMore(false);
        }
    };
    const handleDataFromChild = async (data) => {
        console.log('data', data);

        if (data.data) {

            console.log('1');
            let orderr = (order.id === JSON.parse(data.data));
            if (orderr) {
                // props.navigation.push('MedicalExaminationDetails', { item: sheep })
                // setModalVisibleDetails(!ModalVisibleDetails)
                props.navigation.push('OrderDetails', { order: order, item: props.route.params.item, })
                console.log('success');
            }
            else {
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    strings('lang.Thisrequestcannotbefound'),
                    White,
                    1500,
                    screenHeight / 15,
                );
                setIsOpen(!IsOpen);
            }
        }
        refRBSheet1.current.close();
        refRbSheetCheck.current.close();
    }

    const handleDataFromText = async (data) => {
        console.log('data', data);
        if (data) {

            console.log('1');
            let orderr = (order.id === JSON.parse(data));
            if (orderr) {
                // props.navigation.push('MedicalExaminationDetails', { item: sheep })
                // setModalVisibleDetails(!ModalVisibleDetails)
                props.navigation.push('OrderDetails', { order: order, item: props.route.params.item, })

                console.log('success');
            }
            else {
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    strings('lang.Thisrequestcannotbefound'),
                    White,
                    1500,
                    screenHeight / 15,
                );
                setIsOpen(!IsOpen);
                setIsLoad(!IsLoad);
            }
        }
        setSheepCode('');
        refRBSheetCode.current.close();
        refRbSheetCheck.current.close();
    }
    const openModalImage = async (data) => {
        if (true) {

        }
    }


    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => props.navigation.push('Cart', { shared: false })} title={strings('lang.Ordertracking')} backPress={() => { props.navigation.push('MyOrders') }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 12,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 12,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 12,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 2,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => props.navigation.push('Cart', { shared: false })} title={strings('lang.Ordertracking')}
                    backPress={() => {
                        props.navigation.push('MyOrders')
                    }}
                />

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    }
                >
                    {loadingMore ? <Loading /> : <></>}
                    <View style={{ flexDirection: 'row', width: '90%', alignSelf: 'center', backgroundColor: White, paddingHorizontal: '1%', alignItems: "center", marginVertical: screenHeight / 100 }}>
                        <View style={{ width: '30%', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{strings('lang.Paymenttype')}</Text>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{strings('lang.deliverylocation')}</Text>
                            {order.partner_address && order.partner_address.address && order.partner_address.address
                                &&
                                <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{strings('lang.Partnerlocation')}</Text>
                            }
                        </View>
                        <View style={{ width: '55%', }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{order.payment_method ? order.payment_method.name : ''}</Text>
                            <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{order.address ? order.address.address && order.address.address + ' - ' + order.address.landmark : ''}</Text>
                            {order.partner_address && order.partner_address.address && order.partner_address.address
                                &&
                                <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{order.partner_address ? order.partner_address.address && order.partner_address.address + ' - ' + order.partner_address.landmark : ''}</Text>
                            }
                        </View>
                        <View style={{ width: '15%', }}>
                            {order.address && order.address.lat && order.address.lng
                                ?
                                <View style={{ width: '100%', alignItems: "center", justifyContent: "center", marginTop: screenHeight / 200, marginBottom: screenHeight / 200 }}>
                                    <Button style={{ backgroundColor: Red, width: '97%', height: 25, alignSelf: 'flex-end', alignItems: "center", justifyContent: "center", borderRadius: 15 }}
                                        onPress={() => { openMap() }}
                                    >
                                        <Image source={require('../images/modrek/location.png')} style={{ height: '100%', width: '80%', resizeMode: "contain", tintColor: White }} />
                                    </Button>
                                </View>
                                :
                                <></>
                            }
                            {order.partner_address && order.partner_address.lat && order.partner_address.lng
                                ?
                                <View style={{ width: '100%', alignItems: "center", justifyContent: "center", }}>
                                    <Button style={{ backgroundColor: Red, width: '97%', height: 25, alignSelf: 'flex-end', alignItems: "center", justifyContent: "center", borderRadius: 15 }}
                                        onPress={() => { openMappartner() }}
                                    >
                                        <Image source={require('../images/modrek/location.png')} style={{ height: '100%', width: '80%', resizeMode: "contain", tintColor: White }} />
                                    </Button>
                                </View>
                                :
                                <></>
                            }
                        </View>

                    </View>

                    <View style={{
                        width: '100%',
                        alignSelf: 'center', backgroundColor: WhiteGery, padding: '3%',
                        alignItems: "center", marginVertical: screenHeight / 100
                    }}>
                        <FlatList
                            data={order.sheep}
                            showsVerticalScrollIndicator={false}
                            renderItem={({ item }) =>
                                <View style={{ width: screenWidth / 1.1, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginVertical: '3%', alignItems: 'center', alignSelf: 'center', justifyContent: 'space-between', paddingHorizontal: '1%' }}>
                                    <View style={{ flexDirection: 'row', }}>
                                        <View style={{ width: screenWidth / 8.5, height: screenWidth / 8.5, backgroundColor: item.sheep && item.sheep.collar_color, alignItems: 'center', justifyContent: 'center', marginEnd: '2%', alignSelf: 'center', borderRadius: screenWidth / 60 }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: White }}>{item.sheep && item.sheep.id}</Text>
                                        </View>

                                        <View style={{ flexDirection: 'column', alignItems: 'flex-start', justifyContent: 'center', flex: 1 }}>
                                            <Text style={{ color: DarkGrey, fontFamily: appFont, fontSize: screenWidth / 28, textAlign: 'left' }}>{`${item.sheep && item.sheep.sheep_category && item.sheep.sheep_category.name} / ${item.sheep &&
                                                item.sheep.sharing_type ? item.sheep.sharing_type.count == 4 ?
                                                (item.sheep.weight.from / 4) :
                                                item.sheep.sharing_type.count == 2 &&
                                                (item.sheep.weight.from / 2) :
                                                item.sheep.weight.from} كيلو - ${item.sheep && item.sheep.sharing_type ?
                                                    item.sheep.sharing_type.count == 4 ?
                                                        (item.sheep.weight.to / 4) :
                                                        item.sheep.sharing_type.count == 2 &&
                                                        (item.sheep.weight.to / 2) :
                                                    item.sheep.weight.to} كيلو / ${item.sheep && item.sheep.farm && item.sheep.farm.code} `}</Text>
                                            <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, }}>{order.payment_type == "Shared" ? order.shared_value : item.sheep && item.sheep.price} {strings('lang.SAR')} {' '}
                                                <Text style={{ color: MediumGrey, fontFamily: appFontBold, fontSize: screenWidth / 30, }}> {order.payment_type == "Full" && `(${strings('lang.Purepayment')})`}</Text>
                                            </Text>
                                        </View>
                                    </View>
                                    <View style={{
                                        flexDirection: 'row', width: '70%', paddingHorizontal: 10, marginBottom: 2,
                                        justifyContent: item.sheep.images && item.sheep.images.length != 0 ? 'space-between' : 'flex-end',
                                        alignSelf: 'flex-end'
                                    }}>

                                        {item.sheep.images && item.sheep.images.length != 0
                                            ?
                                            <View style={{
                                                width: '40%',
                                                height: screenHeight / 15,
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                borderRadius: 15, borderWidth: 1,
                                                borderColor: MediumGrey,
                                                overflow: 'hidden'
                                            }}>
                                                <Button transparent
                                                    onPress={() => {
                                                        setLoading(true)
                                                        setIsOpen(true)
                                                        let sheepImages = [];
                                                        // for (let itemm of item.sheep.images) {
                                                        //     if(item.sheep.images&&item.sheep.images[0]){

                                                        //     }
                                                        sheepImages = [...sheepImages,
                                                        { url: item.sheep.images && item.sheep.images[0] },
                                                        ]
                                                        if (item.sheep.images[1]) {
                                                            sheepImages = [...sheepImages,
                                                            { url: item.sheep.images && item.sheep.images[1] },
                                                            ]
                                                        }
                                                        // };
                                                        console.log('images', sheepImages);
                                                        setSheepImages(sheepImages)
                                                        refRbSheetImages.current.open();
                                                        // setIsVisible(!visible)
                                                        setLoading(false)
                                                    }}
                                                    style={{
                                                        backgroundColor: 'transpernt',
                                                        width: '100%', height: '100%',
                                                        alignItems: "center",
                                                        justifyContent: "center",
                                                        position: 'absolute', zIndex: 2
                                                    }}>
                                                </Button>
                                                {item.sheep.images && item.sheep.images[0] &&

                                                    <Image source={{ uri: item.sheep.images[0] }} style={{ width: '100%', height: '100%', resizeMode: 'stretch', }} />
                                                }
                                            </View>

                                            :
                                            <></>
                                        }
                                        {/* <View style={{ flexDirection: 'row', backgroundColor: Red1, }}> */}
                                        {order.sheep && order.sheep.length > 1
                                            ?
                                            <></>
                                            :
                                            <View style={{ width: '30%', height: 30, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', }}>
                                                <Button
                                                    onPress={() => {
                                                        setIsOpen(true)
                                                        refRbSheetCheck.current.open();
                                                    }}
                                                    disabled={order.delivered_to_customer == false ? true : false}
                                                    style={{ backgroundColor: order.delivered_to_customer ? Red1 : MediumGrey, width: '100%', height: '100%', alignItems: "center", justifyContent: "center", borderRadius: 15 }}>
                                                    <Text style={{ color: order.delivered_to_customer ? White : DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 40, }}>{strings('lang.checkyourorder')}</Text>
                                                </Button>
                                            </View>
                                        }

                                        {item.sheep && item.sheep.is_rated
                                            ?
                                            <>

                                            </>
                                            :

                                            <View style={{ width: '30%', height: 30, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', }}>
                                                <Button
                                                    onPress={() => {
                                                        refRbSheet.current.open();
                                                        setIsOpen(true)
                                                        setRatingSheep(item);
                                                    }}
                                                    disabled={order.delivered_to_customer == false ? true : false}
                                                    style={{ backgroundColor: order.delivered_to_customer ? Red : MediumGrey, width: '100%', height: '100%', alignItems: "center", justifyContent: "center", borderRadius: 15 }}>
                                                    <Text style={{ color: order.delivered_to_customer ? White : DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 40, }}>{strings('lang.Evaluation')}</Text>
                                                </Button>
                                            </View>

                                        }
                                    </View>

                                    {/* </View> */}

                                </View>
                            }
                            keyExtractor={item => item.id}
                            style={{ alignSelf: "center", width: screenWidth / 1.1, overflow: "hidden", flexDirection: "column", flexWrap: "wrap", }}
                        // onEndReached={() => this.LoadMore()}
                        // onEndReachedThreshold={0.1}
                        // numColumns={2}
                        />
                        {order.sheep && order.sheep.length > 1
                            ?
                            <>
                                <View style={{ width: '30%', height: 30, alignItems: 'center', justifyContent: 'center', marginStart: '2%', alignSelf: 'center', }}>
                                    <Button
                                        onPress={() => {
                                            setIsOpen(true)
                                            setModalVisible(!modalVisible)
                                        }}
                                        disabled={order.delivered_to_customer == false ? true : false}
                                        style={{ backgroundColor: order.delivered_to_customer ? Red1 : MediumGrey, width: screenWidth / 3, height: screenHeight / 22, alignItems: "center", justifyContent: "center", borderRadius: 15 }}>
                                        <Text style={{ color: order.delivered_to_customer ? White : DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 40, }}>{strings('lang.checkyourorder')}</Text>
                                    </Button>
                                </View>
                            </>
                            :
                            <></>
                        }
                    </View>
                    {order.expected_delivery_time
                        ?
                        <View style={{ width: screenWidth / 1.1, alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '2%', height: screenHeight / 15, alignItems: 'center' }}>
                            <View style={{ width: '50%', }}>
                                <Text style={styles.textGrey1}>{strings('lang.Estimateddeliverytime')}</Text>
                            </View>
                            <View style={{ width: '50%', alignItems: "flex-end", flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                                <Image source={I18nManager.isRTL ? require('../images/modrek/clock.png') : require('../images/modrek/clock.png')} style={{ tintColor: Red, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} />
                                <Text style={styles.textRed1}>{order.expected_delivery_time}</Text>
                            </View>
                        </View>
                        :
                        <></>
                    }

                    {statuses.length != 0
                        &&
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, marginStart: '5%', alignSelf: 'flex-start' }}>{strings('lang.Ordertracking')}</Text>
                    }


                    <View style={{ flexDirection: 'row', width: '90%', alignSelf: 'center', marginTop: 5 }}>
                        <View style={{ flexDirection: 'column', width: '10%', alignItems: 'center', justifyContent: 'flex-start', marginTop: 6, }}>
                            {statuses.map((item, index) => (
                                <>
                                    <View style={{ width: screenHeight / 37, height: screenHeight / 37, borderRadius: 11, borderWidth: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: orderStatuses.find(x => x.id === item.id) ? Red : White }}>
                                        <Image source={require('../images/modrek/true.png')} style={{ tintColor: orderStatuses.find(x => x.id === item.id) ? White : DarkGrey, resizeMode: 'contain', width: '80%', height: '55%', }} />
                                    </View>
                                    {index == (statuses.length - 1)
                                        ?
                                        <></>
                                        :
                                        <View style={styles.line}>
                                            {/* <Image source={require('../images/modrek/line.png')} style={{ tintColor: index + 1 <= orderStatuses.length ? Red : MediumGrey, resizeMode: 'cover', width: '100%', height: '100%', }} /> */}
                                            <View style={{ backgroundColor: index + 1 <= orderStatuses.length ? Red : DarkGrey, width: 2, height: '100%', }} ></View>
                                            {/* <Image source={require('../images/modrek/line.png')} style={{ tintColor: index + 1 <= orderStatuses.length ? Red : MediumGrey, resizeMode: 'cover', width: '100%', height: '100%', }} /> */}
                                        </View>
                                    }

                                </>
                            ))}
                        </View>

                        <View style={{ flexDirection: 'column', width: '90%', }}>
                            {statuses.map((item, index) => (
                                <View style={styles.titlecontainer}>
                                    <View style={{ flexDirection: 'column', width: '65%', }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 32, textAlign: I18nManager.isRTL ? 'left' : 'right', color: orderStatuses.find(x => x.id === item.id) ? Red : MediumGrey }}>{item.name}</Text>
                                        <Text style={{ fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{orderStatuses.find(x => x.id === item.id) ? orderStatuses.find(x => x.id === item.id).date : ''}</Text>
                                    </View>
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 4 &&
                                        <Animated.View style={[styles.button, { opacity }]}>
                                            <Button
                                                // onPress={() => { props.navigation.navigate('MoveOrderToSlaughter', { statuses: statuses[3].children, orderStatuses: orderStatuses[3] ? orderStatuses[3].children : [{ id: 6, name: 'انتظار الطلب في حظائر المسلخ"', handle: 'Waiting in slaughter', date: '12-13-14' }] }) }}
                                                onPress={() => { props.navigation.navigate('MoveOrderToSlaughter', { statuses: statuses[3].children, orderStatuses: orderStatuses[3] ? orderStatuses[3].children : [], sheeps: order.sheep, sheep: order.sheep[0].sheep, id: props.route.params.item.id }) }}
                                                style={{ width: '90%', height: 30, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Image source={require('../images/modrek/eyee.png')} style={{ tintColor: White, resizeMode: 'contain', width: '12%', height: '100%', marginEnd: 5 }} />
                                                <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.thedetails')}</Text>
                                            </Button>
                                        </Animated.View>
                                    }
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 5 &&
                                        order.delivered_to_customer == false ?
                                        <View style={styles.button}>
                                            <View
                                                // onPress={() => { props.navigation.navigate('MoveOrderToSlaughter', { statuses: statuses[3].children, orderStatuses: orderStatuses[3] ? orderStatuses[3].children : [{ id: 6, name: 'انتظار الطلب في حظائر المسلخ"', handle: 'Waiting in slaughter', date: '12-13-14' }] }) }}
                                                // onPress={() => { props.navigation.navigate('MoveOrderToSlaughter', { statuses: statuses[3].children, orderStatuses: orderStatuses[3] ? orderStatuses[3].children : [], sheep: order.sheep }) }}
                                                style={{ width: '90%', height: 50, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Image source={require('../images/modrek/bus.gif')} style={{ resizeMode: 'contain', width: '100%', height: '100%', marginEnd: 5 }} />
                                                {/* <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.Yourorderisontheway')}</Text> */}
                                            </View>
                                        </View>
                                        :
                                        <></>
                                    }
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id != 4 && item.id != 5 &&
                                        <Animated.View style={[styles.button, { opacity }]}>
                                            <Button
                                                onPress={() => { props.navigation.navigate('LiveModal2', { sheeps: order.sheep, sheep: order.sheep[0].sheep, status: orderStatuses[orderStatuses.length - 1], link: link }) }}
                                                style={{ width: '90%', height: 30, backgroundColor: Red1, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Image source={require('../images/modrek/svgexport-183.png')} style={{ tintColor: White, resizeMode: 'contain', width: '12%', height: '100%', marginEnd: 5 }} />
                                                <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.watch')}</Text>
                                            </Button>
                                        </Animated.View>
                                    }
                                </View>
                            ))}


                        </View>
                    </View>
                </ScrollView >

                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={250}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            // borderTopEndRadius: 25,
                            // borderTopStartRadius: 25,
                            borderRadius: 20,
                            width: '90%',
                            alignSelf: 'center',
                            marginBottom: screenHeight / 4
                            // bottom: '25%'
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey
                        }
                    }}
                    closeOnPressMask={false} // Prevent closing on pressing the mask
                    closeOnPressBack={false}
                // closeOnDragDown={true}
                // onClose={() => {  }}
                >
                    <View style={{ flexDirection: 'column', width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', paddingBottom: '1%' }}>

                        <KeyboardAwareScrollView
                            showsVerticalScrollIndicator={false}
                            style={{ width: '100%' }}
                        >
                            <View style={{ flexDirection: 'row', height: screenHeight / 18, width: '100%', alignItems: 'center', justifyContent: 'center', }}>
                                <Text style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 25,
                                    color: Black
                                }}> {strings('lang.Putyourrating')}</Text>
                                {/* <Button transparent onPress={() => { refRbSheet.current.close(); }} style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={require('../images/modrek/x.png')} style={{ width: '50%', height: '50%', resizeMode: 'contain', }} />
                            </Button> */}
                            </View>

                            <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 30,
                                color: Black,
                                alignSelf: 'flex-start'
                            }}>{strings('lang.rate1')}</Text>
                            <View style={{ height: screenHeight / 18, marginVertical: 10, flexDirection: "row", alignItems: 'center', alignSelf: 'center' }}>
                                <StarRating
                                    disabled={false}
                                    maxStars={5}
                                    starSize={screenHeight / 25}
                                    starStyle={{ marginEnd: 2, alignSelf: 'center' }}
                                    rating={rating}
                                    selectedStar={(rating) => setRating(rating)}
                                    fullStarColor={DarkYellow}
                                />
                                {/* <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>(1) </Text> */}
                            </View>

                            <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 30,
                                color: Black,
                                alignSelf: 'flex-start'
                            }}>{strings('lang.rate2')}</Text>
                            <View style={{ height: screenHeight / 18, marginVertical: 10, flexDirection: "row", alignItems: 'center', alignSelf: 'center' }}>
                                <StarRating
                                    disabled={false}
                                    maxStars={5}
                                    starSize={screenHeight / 25}
                                    starStyle={{ marginEnd: 2, alignSelf: 'center' }}
                                    rating={rating1}
                                    selectedStar={(rating) => setRating1(rating)}
                                    fullStarColor={DarkYellow}
                                />
                                {/* <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>(1) </Text> */}
                            </View>

                            <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                            <Text style={{
                                fontFamily: appFontBold,
                                fontSize: screenWidth / 30,
                                color: Black,
                                alignSelf: 'flex-start'
                            }}>{strings('lang.Whatdoyouthinkoftheservice')}</Text>
                            <View style={{ width: '90%', height: screenHeight / 15, marginBottom: 20, alignSelf: 'center', marginVertical: '2.5%' }}>
                                <Textarea
                                    onChangeText={text => setReview(text)}
                                    value={review}
                                    placeholder={strings('lang.Whatdoyouthinkoftheservice')}
                                    placeholderTextColor={MediumGrey}
                                    style={{
                                        width: '100%',
                                        alignSelf: 'center',
                                        borderRadius: 15,
                                        paddingHorizontal: '5%',
                                        borderColor: MediumGrey,
                                        color: DarkGrey,
                                        borderWidth: 1,
                                        fontFamily: appFontBold,
                                        height: '100%',
                                        textAlignVertical: 'top',
                                        fontSize: screenWidth / 30,
                                        textAlign: I18nManager.isRTL ? 'right' : 'left',
                                    }}
                                />
                            </View>

                            <Button
                                onPress={() => { addRating() }}
                                style={{
                                    height: screenHeight / 20, width: '95%', alignSelf: 'center',
                                    justifyContent: 'center', alignItems: 'center',
                                    backgroundColor: Red, borderRadius: 20
                                }}>
                                <Text style={{
                                    fontFamily: appFontBold,
                                    fontSize: screenWidth / 30,
                                    color: White
                                }}>{strings('lang.Evaluation')}</Text>
                            </Button>
                        </KeyboardAwareScrollView>
                    </View>

                </RBSheet>

                <Modal visible={visible}
                    transparent={true}
                    onRequestClose={() => setIsVisible(false)}
                    animationType="fade"
                >
                    <View style={{
                        flex: 1,
                        width: screenWidth,
                        height: screenHeight,
                        backgroundColor: White,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>

                        <Button transparent onPress={() => { setIsVisible(false); setIsOpen(!IsOpen); setIsLoad(!IsLoad); }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                            <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                        </Button>
                        <FlatList
                            data={sheepImages}
                            keyExtractor={(item) => item.id}
                            horizontal
                            pagingEnabled
                            showsHorizontalScrollIndicator={false}
                            // initialScrollIndex={selectedIndex}
                            style={{ width: screenWidth, height: '100%' }}
                            getItemLayout={(data, index) => ({
                                length: screenWidth, // Width of each image
                                offset: screenWidth * index,
                                index,
                            })}
                            renderItem={({ item }) => (
                                <Image source={{ uri: item.url }} style={{
                                    width: screenWidth,
                                    height: screenHeight,
                                    resizeMode: 'contain'
                                    // marginHorizontal: 10,
                                    // borderRadius: 10,
                                }} />
                            )}
                        />
                    </View>

                    {/* <ImageViewer
                        // onChange={(imageIndex) => { setSheepImages(sheepImages[imageIndex].url) }} 
                        enableSwipeDown={false} onSwipeDown={() => { setIsVisible(false); setIsOpen(!IsOpen) }} backgroundColor='white' style={{ backgroundColor: 'White', height: '100%', borderRadius: 10, overflow: 'hidden' }} imageUrls={sheepImages} />
                    <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', zIndex: 100, padding: 5 }} >
                        {sheepImages.map((item, index) => {
                            return (
                                <Button
                                    // transparent onPress={() => { console.log('1'); setSheepImages(sheepImages[index].url) }}
                                    style={styles.imageActive}
                                >
                                    <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', borderRadius: 5 }} />
                                </Button>
                            )
                        })}
                    </ScrollView> */}
                </Modal>

                <RBSheet
                    ref={refRbSheetImages}
                    height={screenHeight}
                    openDuration={250}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            // borderTopEndRadius: 25,
                            // borderTopStartRadius: 25,
                            // borderRadius: 20,
                            width: '100%',
                            alignSelf: 'center',
                            // marginBottom: screenHeight / 4
                            // bottom: '25%'
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey
                        }
                    }}
                    closeOnPressMask={false} // Prevent closing on pressing the mask
                    closeOnPressBack={false}
                // closeOnDragDown={true}
                // onClose={() => {  }}
                >
                    <View style={{
                        flex: 1,
                        width: screenWidth,
                        height: screenHeight,
                        backgroundColor: White,
                        justifyContent: 'center',
                        alignItems: 'center'
                    }}>

                        <Button transparent onPress={() => { refRbSheetImages.current.close(); setIsOpen(!IsOpen); setIsLoad(!IsLoad); }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                            <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                        </Button>
                        <FlatList
                            data={sheepImages}
                            keyExtractor={(item) => item.id}
                            horizontal
                            pagingEnabled
                            showsHorizontalScrollIndicator={false}
                            // initialScrollIndex={selectedIndex}
                            style={{ width: screenWidth, height: '100%' }}
                            getItemLayout={(data, index) => ({
                                length: screenWidth, // Width of each image
                                offset: screenWidth * index,
                                index,
                            })}
                            renderItem={({ item }) => (
                                <Image source={{ uri: item.url }} style={{
                                    width: screenWidth,
                                    height: screenHeight,
                                    resizeMode: 'contain'
                                    // marginHorizontal: 10,
                                    // borderRadius: 10,
                                }} />
                            )}
                        />
                    </View>

                </RBSheet>



                <>
                    <Modal
                        animationType='slide'
                        transparent={true}
                        visible={modalVisible}
                        onRequestClose={() => setModalVisible(false)}
                    >
                        <View style={styles.modalContainer}>
                            <View style={styles.modalContent}>
                                {/* <Text style={styles.modalText}>{
                                    qrtype == 'SellVisitors' ?
                                        strings('lang.Sellvisitors')
                                        :
                                        qrtype == 'ExternalExclusionDetails'
                                            ?
                                            strings('lang.externalexclusion')
                                            :
                                            qrtype == 'ExternalExclusionDetails'
                                            ?
                                            strings('lang.emergencyexclusion')
                                            :
                                            strings('lang.emergencyexclusion')

                                }</Text> */}
                                <View style={styles.buttonRow}>
                                    <Button
                                        style={[styles.modalButton, styles.cancelButton]}
                                        onPress={() => { setModalVisible(false); setIsOpen(!IsOpen); setIsLoad(!IsLoad); }}
                                    >
                                        <Text style={styles.buttonText}>{strings('lang.Cancel')}</Text>
                                    </Button>
                                    <Button
                                        style={[styles.modalButton, styles.confirmButton]}
                                        onPress={() => {
                                            setModalVisible(false);
                                            refRBSheetCode.current.open();
                                        }}
                                    >
                                        <Text style={styles.buttonText}>{strings('lang.Addthecode')}</Text>
                                    </Button>
                                    <Button
                                        style={[styles.modalButton, styles.confirmButton]}
                                        onPress={() => {
                                            setModalVisible(false);
                                            refRBSheet1.current.open();
                                        }}
                                    >
                                        <Text style={styles.buttonText}>{strings('lang.Scanthecode')}</Text>
                                    </Button>
                                </View>
                            </View>
                        </View>
                    </Modal>

                    <RBSheet
                        ref={refRbSheetCheck}
                        height={screenHeight / 10}
                        openDuration={250}
                        customStyles={{
                            container: {
                                // justifyContent: "center",
                                alignItems: "center",
                                // borderTopEndRadius: 25,
                                // borderTopStartRadius: 25,
                                borderRadius: 20,
                                width: '90%',
                                alignSelf: 'center',
                                marginBottom: screenHeight / 2.5,
                                backgroundColor: White
                                // bottom: '25%'
                            },
                            wrapper: {

                            },
                            draggableIcon: {
                                width: '25%',
                                backgroundColor: White
                            }
                        }}
                        closeOnPressMask={false} // Prevent closing on pressing the mask
                        closeOnPressBack={false}
                    // closeOnDragDown={true}
                    // onClose={() => {  }}
                    >
                        <View style={styles.modalContainer}>
                            <View style={styles.modalContent}>
                                <View style={styles.buttonRow}>
                                    <Button
                                        style={[styles.modalButton, styles.cancelButton]}
                                        onPress={() => { refRbSheetCheck.current.close(); setIsOpen(!IsOpen); setIsLoad(!IsLoad); }}
                                    >
                                        <Text style={styles.buttonText}>{strings('lang.Cancel')}</Text>
                                    </Button>
                                    <Button
                                        style={[styles.modalButton, styles.confirmButton]}
                                        onPress={() => {
                                            // refRbSheetCheck.current.close();
                                            refRBSheetCode.current.open();
                                        }}
                                    >
                                        <Text style={styles.buttonText}>{strings('lang.Addthecode')}</Text>
                                    </Button>
                                    <Button
                                        style={[styles.modalButton, styles.confirmButton]}
                                        onPress={() => {
                                            // refRbSheetCheck.current.close();
                                            refRBSheet1.current.open();
                                        }}
                                    >
                                        <Text style={styles.buttonText}>{strings('lang.Scanthecode')}</Text>
                                    </Button>
                                </View>
                            </View>
                        </View>

                        <>
                            <RBSheet
                                ref={refRBSheet1}
                                height={screenHeight / 1.1}
                                openDuration={280}
                                customStyles={{
                                    container: {
                                        // justifyContent: "center",
                                        alignItems: "center",
                                        width: '100%',

                                        alignSelf: 'center',
                                        borderRadius: 15

                                    },
                                    wrapper: {

                                    },
                                    draggableIcon: {
                                        // width: '25%',
                                        // backgroundColor: DarkGrey
                                    }
                                }}
                            // onClose={() => { refRBSheet1.current.close() }}
                            // closeOnDragDown={true}
                            >
                                <QrCode sendDataToParent={handleDataFromChild} />
                            </RBSheet>

                            <RBSheet
                                ref={refRBSheetCode}
                                height={screenHeight / 3}
                                openDuration={280}
                                customStyles={{
                                    container: {
                                        // justifyContent: "center",
                                        alignItems: "center",
                                        width: '100%',
                                        paddingVertical: 5,
                                        alignSelf: 'center',
                                        borderTopLeftRadius: 15,
                                        borderTopRightRadius: 15

                                    },
                                    wrapper: {

                                    },
                                    draggableIcon: {
                                        // width: '25%',
                                        // backgroundColor: DarkGrey
                                    }
                                }}
                            // onClose={() => { refRBSheet1.current.close() }}
                            // closeOnDragDown={true}
                            >
                                <View style={{ width: '90%', alignSelf: 'center', height: screenHeight / 20, alignItems: "flex-end", justifyContent: 'flex-start' }}>
                                    <Button
                                        onPress={() => { refRBSheetCode.current.close(); }}
                                        style={{ width: screenWidth / 12, height: screenWidth / 12, borderRadius: screenWidth / 24, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center" }}>
                                        <Image source={require('../images/Silal/x.png')} style={{ height: '50%', resizeMode: "contain" }} />
                                    </Button>
                                </View>

                                <View style={{ width: '90%', height: screenHeight / 18, marginTop: '10%', marginBottom: '5%', alignSelf: 'center', marginVertical: '2.5%' }}>
                                    <Textarea
                                        onChangeText={text => setSheepCode(text)}
                                        value={sheepCode}
                                        placeholder={strings('lang.Addthecode')}
                                        placeholderTextColor={MediumGrey}
                                        style={{
                                            width: '100%',
                                            alignSelf: 'center',
                                            borderRadius: 15,
                                            paddingHorizontal: '5%',
                                            borderColor: MediumGrey,
                                            color: DarkGrey,
                                            borderWidth: 1,
                                            fontFamily: appFontBold,
                                            height: '100%',
                                            textAlignVertical: 'top',
                                            fontSize: screenWidth / 30,
                                            textAlign: I18nManager.isRTL ? 'right' : 'left',
                                        }}
                                    />
                                </View>
                                <Button
                                    onPress={async () => {
                                        handleDataFromText(sheepCode);
                                        // setModalVisibleDetails(!ModalVisibleDetails)
                                    }}
                                    // onPress={() => { moveToInspection() }}
                                    style={{ width: '90%', alignSelf: "center", height: screenHeight / 18, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20 }}>
                                    {/* <View style={{ width: 13, height: 13, borderRadius: 26, backgroundColor: White, marginEnd: 5, marginTop: 4, alignItems: 'center', justifyContent: 'center', }}>
              <Image source={require('../images/modrek/scancode.png')}
                style={{ width: "70%", height: "60%", resizeMode: "contain", tintColor: Black }} />
            </View> */}
                                    <Text style={{ fontSize: screenWidth / 30, fontFamily: appFontBold, color: White }}>{strings('lang.Addthecode')}</Text>
                                </Button>

                            </RBSheet>
                        </>

                    </RBSheet>


                </>


                {/* <LiveModal2 sheep={order.sheep} status={orderStatuses[orderStatuses.length - 1]} link={link} ModalVisible={ModalVisible} modalPress={() => { setModalVisible(!ModalVisible) }} /> */}

                <View style={{ height: screenHeight / 8, }}></View>
                <MyFooter navigation={props.navigation} />
            </View >
        )
    }
}
export default OrderTracking;
const styles = StyleSheet.create({

    modalContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
    },
    modalContent: {
        width: screenWidth / 1.2,
        backgroundColor: '#fff',
        // borderRadius: 10,
        padding: 20,
        alignItems: 'center',
    },
    modalText: {
        marginBottom: 15,
        fontSize: screenWidth / 20,
        fontFamily: appFontBold,
        color: Black,
        textAlign: 'center',
    },
    buttonText: {
        fontSize: screenWidth / 30,
        fontFamily: appFontBold,
        color: White,
    },
    buttonRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width: '100%',
        height: screenHeight / 20
    },
    modalButton: {
        height: '100%',
        width: screenWidth / 4.5,
        padding: 10,
        marginHorizontal: 5,
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center'
    },
    cancelButton: {
        backgroundColor: Red1,
    },
    confirmButton: {
        backgroundColor: Red,
    },
    button: { width: '32%', alignSelf: 'flex-end', alignItems: 'center', justifyContent: 'center', height: '100%' },
    titlecontainer: { marginStart: '5%', height: screenHeight / 11.8, flexDirection: 'row', width: '100%' },
    containerline: { width: 20, height: 20, borderRadius: 4, borderWidth: 1, borderColor: Red, alignItems: 'center', justifyContent: 'center' },
    line: { width: '10%', height: screenHeight / 18, },

    textBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textBlack0: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-end' },
    textBlack1: { fontFamily: appFont, color: Black, fontSize: screenWidth / 24, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textAddress: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textRed1: { fontFamily: appFontBold, color: Red, fontSize: screenWidth / 24, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textRed2: { fontFamily: appFont, color: Red, fontSize: screenWidth / 30, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey: { fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 26, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey2: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },
    textGrey1: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey3: { fontFamily: appFont, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 },
    textWhite1: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 24 },
    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    squareGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: 2, marginEnd: '3%' },
    circleGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: screenHeight / 100, marginStart: '20%', marginEnd: '3%' },
});
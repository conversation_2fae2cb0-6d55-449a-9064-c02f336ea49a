import { Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, I18nManager, Image, StyleSheet, Text, View } from "react-native";
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import { Black, DarkBlue, DarkGrey, Gold, MediumGrey, Red, Red1, White, WhiteGery, appFont, appFontBold, screenHeight, screenWidth } from "../components/Styles";
import { strings } from './i18n';
// import Header from '../components/Header';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import I18n from 'react-native-i18n';
import RBSheet from 'react-native-raw-bottom-sheet';
import RNRestart from 'react-native-restart';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import { useDispatch, useSelector } from 'react-redux';
import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';


const More = props => {

    const [name, setName] = useState('');
    const [touchId, setTouchId] = useState('0');
    const refRbSheet = useRef();
    const [lang, setLang] = useState('');
    const [lan, setLan] = useState('ar');
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [isGolden, setIsGolden] = useState(false);
    const dispatch = useDispatch();
    const user = useSelector(state => state.auth.user)
    const [Token, setToken] = useState('');

    useEffect(() => {
        setName(user.name)
        setIsGolden(user.is_golden)
        const token = async () => {
            let token = await AsyncStorage.getItem('token')
            setToken(token)
        }
        token()
    }, []);

    const set_ar = async () => {
        // let lan = await AsyncStorage.getItem("lan")
        // if (lan == 'en') {
        // this.setState({
        //   lan: 'ar',
        //   modalDeleteVisible: false
        // })
        AsyncStorage.setItem('lan', 'ar')
        AsyncStorage.setItem('stat', '1')
        I18n.locale = "ar";
        I18nManager.forceRTL(true);
        RNRestart.Restart();
        // }
    }

    const set_en = async () => {
        // let lan = await AsyncStorage.getItem("lan")
        // if (lan == 'ar') {
        // this.setState({
        //   lan: 'en',
        // })
        AsyncStorage.setItem('lan', 'en')
        I18n.locale = "en";
        I18nManager.forceRTL(false);
        RNRestart.Restart();
        // }
    }

    const logout = async () => {
        dispatch(authActions.logout());
        await AsyncStorage.removeItem('token')
        await AsyncStorage.removeItem('email')
        await AsyncStorage.removeItem('name')
        await AsyncStorage.removeItem('phone')
        await AsyncStorage.removeItem('user')
        await AsyncStorage.removeItem('access_id')
        await AsyncStorage.removeItem('address')
        props.navigation.navigate('Login')
    }

    const skipLogin = () => {
        Toaster(
            'top',
            'warning',
            Red1,
            strings('lang.login_continuou'),
            White,
            1500,
            screenHeight / 50,
        );
        props.navigation.navigate('Login')
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Profile')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 3,
                                    height: screenWidth / 3,
                                    borderRadius: screenWidth / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '40%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '35%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 2,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'More'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>

                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Profile')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView style={{ flexDirection: "column", alignSelf: "center", width: "100%", marginBottom: screenHeight / 10 }}>



                    <View style={{ flexDirection: "column", alignSelf: "center", justifyContent: "center", width: "100%", alignItems: 'center', backgroundColor: White }}>

                        <View style={{ width: screenWidth / 3, height: screenWidth / 3, borderRadius: screenWidth / 6, overflow: 'hidden', alignSelf: "center", alignItems: "center", justifyContent: "center", borderColor: MediumGrey, borderWidth: 1, backgroundColor: Gold, marginTop: screenHeight / 30, }}>
                            <Image style={{ width: '70%', height: '70%', resizeMode: "contain", tintColor: White }} source={require('../images/user.png')} />
                        </View>

                        <Text style={{ textAlign: "center", fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26, }}>{name}</Text>
                        {isGolden
                            &&
                            <Text style={{ textAlign: "center", fontFamily: appFontBold, color: Gold, fontSize: screenWidth / 26, marginBottom: screenHeight / 60 }}>عميل ذهبي</Text>
                        }
                        <View style={{ width: '95%', backgroundColor: WhiteGery, borderRadius: 20 }}>
                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (Token) {
                                    props.navigation.navigate('MyProfile')
                                } else {
                                    skipLogin()
                                }
                            }
                            }>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport612.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Profile')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => props.navigation.navigate('PartnershipOrders')}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport-1.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.PartnershipApplications')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity> */}

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (Token) {
                                    // props.navigation.navigate('Notifications')
                                    props.navigation.navigate('Test')
                                } else {
                                    skipLogin()
                                }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/surface-897.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Notifications')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => this.props.navigation.navigate('Orders')}>
                            <View style={styles.optionSubContainer}>
                                <View style={styles.iconContainer}>
                                    <Image style={styles.icon} source={require('../images/Silal/svgexport-6(14).png')} />
                                </View>
                                <View style={styles.labelContainer}>
                                    <Text style={styles.label}>{strings('lang.Orders')}</Text>
                                </View>
                            </View>
                        </TouchableOpacity> */}

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (Token) {
                                    props.navigation.navigate('Addresses', { screen: 'More', shared: false, order: {} })
                                } else {
                                    skipLogin()
                                }
                            }
                            }>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/pin-2.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Adresses')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => this.props.navigation.navigate('')}>
                            <View style={styles.optionSubContainer}>
                                <View style={styles.iconContainer}>
                                    <Image style={styles.icon} source={require('../images/Silal/svgexport-6(32).png')} />
                                </View>
                                <View style={styles.labelContainer}>
                                    <Text style={styles.label}>{strings('lang.FixedConnectionProducts')}</Text>
                                </View>
                            </View>
                        </TouchableOpacity> */}

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => this.props.navigation.navigate('ExpectedProducts')}>
                            <View style={styles.optionSubContainer}>
                                <View style={styles.iconContainer}>
                                    <Image style={styles.icon} source={require('../images/Silal/svgexport-6(15).png')} />
                                </View>
                                <View style={styles.labelContainer}>
                                    <Text style={styles.label}>{strings('lang.ExpectedProducts')}</Text>
                                </View>
                            </View>
                        </TouchableOpacity> */}

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => props.navigation.navigate('PartnershipOrders')}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport-187.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.PartnershipApplications')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity> */}

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (Token) {
                                    props.navigation.navigate('MyOrders')
                                } else {
                                    skipLogin()
                                }
                            }}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport10.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.OrdersTracking')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.optionContainer} onPress={() => {
                                if (Token) {
                                    props.navigation.navigate('ComplaintsAndSuggestions')
                                } else {
                                    skipLogin()
                                }
                            }
                            }>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport-6(16).png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Complaintsandsuggestions')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => refRbSheet.current.open()}>
                                    <View style={styles.optionSubContainer}>
                                        <View style={styles.iconContainer}>
                                            <Image style={styles.icon} source={require('../images/Silal/surface-890.png')} />
                                        </View>
                                        <View style={styles.labelContainer}>
                                            <Text style={styles.label}>{strings('lang.Language')}</Text>
                                        </View>
                                    </View>
                                </TouchableOpacity> */}

                            {/* <TouchableOpacity style={styles.optionContainer} onPress={() => this.props.navigation.navigate('ContactUs')}>
                            <View style={styles.optionSubContainer}>
                                <View style={styles.iconContainer}>
                                    <Image style={styles.icon} source={require('../images/Silal/svgexport-6(16).png')} />
                                </View>
                                <View style={styles.labelContainer}>
                                    <Text style={styles.label}>{strings('lang.CallUs')}</Text>
                                </View>
                            </View>
                        </TouchableOpacity> */}

                            <TouchableOpacity style={styles.optionContainer} onPress={() => props.navigation.navigate('TermsAndConditions')}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport-79.png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.terms_and_conditions')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>

                            <TouchableOpacity style={styles.optionContainer} onPress={() => logout()}>
                                <View style={styles.optionSubContainer}>
                                    <View style={styles.iconContainer}>
                                        <Image style={styles.icon} source={require('../images/modrek/svgexport-6(17).png')} />
                                    </View>
                                    <View style={styles.labelContainer}>
                                        <Text style={styles.label}>{strings('lang.Exit')}</Text>
                                    </View>
                                </View>
                            </TouchableOpacity>
                        </View>
                    </View>

                </ScrollView>

                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: 'center',
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {},
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey,
                        },
                    }}
                // closeOnDragDown={true}
                >
                    <ScrollView
                        style={{
                            width: '92%',
                            alignSelf: 'center',
                            height: '81%',
                            paddingVertical: '2%',
                        }}
                    >
                        <View
                            style={{
                                flexDirection: 'row',
                                width: '65%',
                                height: screenHeight / 15,
                                marginTop: '2%',
                                alignSelf: 'flex-end',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                            }}
                        >
                            <Text style={styles.label}>
                                {strings('lang.Changethelanguage')}
                            </Text>
                            <Button
                                onPress={() => {
                                    refRbSheet.current.close();
                                }}
                                style={{
                                    width: screenWidth / 15,
                                    height: screenWidth / 10,
                                    alignItems: 'center',
                                    alignSelf: 'center',
                                    justifyContent: 'center',
                                    backgroundColor: White,
                                    elevation: 0,
                                }}
                            >

                            </Button>
                        </View>
                        <View
                            style={{
                                flexDirection: 'column',
                                marginVertical: '5%',
                                alignItems: 'center',
                                alignSelf: 'flex-start',
                                justifyContent: 'center',
                            }}
                        >
                            <View style={styles.radiocontainer}>
                                {touchId == '0' ? (
                                    <Button style={styles.activeradio}>
                                        <View style={styles.activeview}></View>
                                    </Button>
                                ) : (
                                    <Button
                                        style={styles.radio}
                                        onPress={() => setTouchId('0')}
                                    ></Button>
                                )}
                                <View style={{ marginStart: '4%' }}>
                                    <Text style={styles.label1}>
                                        {strings('lang.arabicLanguage')}
                                    </Text>
                                </View>
                            </View>
                            <View style={styles.radiocontainer}>
                                {touchId == '1' ? (
                                    <Button style={styles.activeradio}>
                                        <View style={styles.activeview}></View>
                                    </Button>
                                ) : (
                                    <Button
                                        style={styles.radio}
                                        onPress={() => setTouchId('1')}
                                    ></Button>
                                )}
                                <View style={{ marginStart: '4%' }}>
                                    <Text style={styles.label1}>
                                        {strings('lang.englishLanguage')}
                                    </Text>
                                </View>
                            </View>

                            <View
                                style={{
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    width: '100%',
                                    alignSelf: 'center',
                                    marginTop: '20%',
                                }}
                            >
                                <Button
                                    style={{
                                        width: '47%',
                                        alignSelf: 'center',
                                        height: 45,
                                        backgroundColor: Red,
                                        alignItems: 'center',
                                        flexDirection: 'row',
                                        justifyContent: 'center',
                                        borderRadius: 10,
                                        flexDirection: 'row',
                                    }}
                                    onPress={() => {
                                        refRbSheet.current.close();
                                        if (touchId == '0' && lang != 'ar') {
                                            set_ar()
                                        }
                                        else if (touchId == '1' && lang != 'en') {
                                            set_en()
                                        }

                                    }}
                                >
                                    <Text
                                        style={{
                                            fontSize: screenWidth / 28,
                                            fontFamily: appFontBold,
                                            color: White,
                                        }}
                                    >
                                        {strings('lang.Confirm')}
                                    </Text>
                                </Button>
                            </View>
                        </View>
                    </ScrollView>
                </RBSheet>

                <View style={{ height: screenHeight / 25, }}></View>
                <MyFooter current={'More'} navigation={props.navigation} />
            </View>
        )
    }
};


export default More;
const styles = StyleSheet.create({
    radiocontainer: {
        flexDirection: 'row',
        alignSelf: 'flex-start',
        alignItems: 'center',
        marginVertical: '2%',
    },

    activeradio: {
        width: screenHeight / 30,
        height: screenHeight / 30,
        borderRadius: screenHeight / 60,
        borderColor: Red,
        borderWidth: 1,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: White,
    },
    activeview: {
        width: screenHeight / 50,
        height: screenHeight / 50,
        borderRadius: screenHeight / 100,
        backgroundColor: Red,
    },
    radio: {
        width: screenHeight / 30,
        height: screenHeight / 30,
        borderRadius: screenHeight / 60,
        borderColor: DarkGrey,
        borderWidth: 1,
        backgroundColor: White,
    },
    label1: { color: DarkGrey, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'flex-start' },


    input: {
        height: 45, borderWidth: 1, borderRadius: 2, borderColor: DarkGrey, flexDirection: 'row', justifyContent: 'center',
        width: "85%", alignSelf: 'center'
    },
    inputText: {
        textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFont,
        fontSize: screenWidth / 30, alignSelf: 'center'
    },
    loginError: {
        color: Red,
        fontFamily: appFont,
        alignSelf: 'flex-start',
        fontSize: screenWidth / 25,
        marginVertical: 3,
        marginHorizontal: '10%'
    },
    buttonContainer: {
        width: '85%',
        height: 45,
        backgroundColor: DarkBlue,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: '3%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    optionContainer: { alignSelf: 'center', width: '100%', height: screenHeight / 21, borderWidth: 0, marginTop: screenHeight / 100, },
    optionContainer2: { alignSelf: 'center', width: '95%', height: 50, borderWidth: 0.5, marginTop: screenHeight / 20, backgroundColor: DarkBlue, borderRadius: 10, borderColor: MediumGrey, },
    optionSubContainer: { alignSelf: 'center', width: '95%', height: '100%', flexDirection: 'row', alignItems: 'center' },
    iconContainer: { width: '15%', height: '80%', alignItems: 'center', justifyContent: 'center' },
    icon: { width: '75%', height: '55%', resizeMode: 'contain', tintColor: Red },
    labelContainer: { width: '85%', height: '100%', justifyContent: 'center' },
    label: { color: Black, fontSize: screenWidth / 26, fontFamily: appFontBold, alignSelf: 'flex-start' },
    textBlue: { color: DarkBlue, fontSize: screenWidth / 22, fontFamily: appFontBold }
});
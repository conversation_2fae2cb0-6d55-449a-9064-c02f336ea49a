import React, { useEffect, useRef } from 'react';
import { Animated, View, Image, StyleSheet, Dimensions } from 'react-native';
import { useFocusEffect } from '@react-navigation/native';
import Header from '../components/Header';
import { Black } from '../components/Styles';
const Test = props => {

    const screenWidth = Dimensions.get('window').width;
    // Starting from 0 and moving right by (screenWidth - carWidth)
    const animatedValue = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        // Create a looping horizontal animation
        Animated.loop(
            Animated.timing(animatedValue, {
                toValue: screenWidth - 100, // assuming car width is 100
                duration: 3000,
                useNativeDriver: true,
            })
        ).start();
    }, [animatedValue, screenWidth]);

    return (
        <View style={styles.container}>
            <Header backPress={() => { props.navigation.goBack() }} />
            {/* <View style={styles.container}> */}
            <Animated.View style={styles.carContainer}>
                <Animated.Image source={require('../images/modrek/bus.gif')} style={[styles.car,]} />
            </Animated.View>
        </View>
    );
};

export default Test;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
    },
    carContainer: {
        // Additional styling if needed
        justifyContent: 'center',
        width: '90%',
        alignItems: 'flex-end',
        alignSelf: 'flex-end',
        // backgroundColor: Black
    },
    car: {
        width: 100,
        height: 50, // Adjust the height as needed
    },
});
import React, { useState, useEffect, useRef } from 'react';
import { Image, View, Text, StyleSheet, I18nManager, BackHandler } from "react-native";
import { screenHeight, DarkBlue, screenWidth, DarkGrey, White, appFont, appFontBold, DarkGreen, Red, Green, appColor2, appColor1, <PERSON>1, <PERSON><PERSON>, <PERSON>, } from "../components/Styles";
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Input, Container, Toast, Button } from 'native-base';
import { ScrollView, TouchableOpacity, } from 'react-native-gesture-handler';
import { strings } from './i18n'
import I18n from 'react-native-i18n';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import CodeInput from 'react-native-confirmation-code-input';
import Header from '../components/Header';
import LoadingMore from '../components/LoadingMore';
import { useDispatch } from 'react-redux';
import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';

const ConfirmationCode = props => {
    const [time, setTime] = useState({})
    const [forget, setForget] = useState(false)
    const [seconds, setSeconds] = useState(60)
    const [phone, setPhone] = useState('')
    const [code, setCode] = useState('')
    const [mins, setMins] = useState(1)
    const [secs, setSecs] = useState(30)
    const [Reload, setReload] = useState(false)
    const timerRef = useRef()
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();




    useEffect(() => {
        const timerId = setInterval(() => {
            if (secs <= 0) {
                if (mins <= 0) {

                }
                else {
                    setMins(m => m - 1)
                    setSecs(59)
                }
            }
            else setSecs(s => s - 1)
        }, 1000)
        return () => clearInterval(timerId);
    }, [secs, mins, Reload])

    // useEffect(() => {
    //     Submit('1234')
    // }, [])

    const Submit = async (code) => {
        setLoading(true)
        let phone = props.route.params.phone;
        let forget = props.route.params.forget;

        console.log('code', code);

        if (forget) {
            props.navigation.navigate('ForgetPasswordChange', { phone: phone })
        }
        else {
            let Verify = await dispatch(authActions.Verify(phone, code));
            console.log('Verify', Verify);

            if (Verify.success == true) {
                AsyncStorage.setItem("token", JSON.stringify(Verify.data.token));
                AsyncStorage.setItem("email", JSON.stringify(Verify.data.user.email));
                AsyncStorage.setItem("name", JSON.stringify(Verify.data.user.name));
                AsyncStorage.setItem("phone", JSON.stringify(Verify.data.user.phone));
                AsyncStorage.setItem("user", JSON.stringify(Verify.data.user));
                AsyncStorage.setItem("access_id", JSON.stringify(Verify.data.user.id));
                props.navigation.navigate('Home', { weightId: null, sheepCatId: null, link: '' })
                setLoading(false)
                Toaster(
                    'top',
                    'success',
                    'green',
                    (strings('lang.Login_Successfuly')),
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
            else {
                setLoading(false)
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    Verify.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
        }

    };


    return (
        <Container>
            <Header title={strings('lang.activation_Code')} backPress={() => { props.navigation.goBack() }} />
            {loading ? <LoadingMore /> : <></>}

            <KeyboardAwareScrollView>
                <View style={{ marginTop: 50, backgroundColor: WhiteGery, alignItems: 'center', justifyContent: 'center', alignSelf: 'center', width: '90%', maxHeight: screenHeight / 1.6, borderRadius: 20 }}>
                    <View style={{ height: '50%', width: '100%', alignItems: 'center', justifyContent: 'center' }}>
                        <Text style={styles.text1}>{strings('lang.Pleaseenterthe4digitcodesenttoyou')}</Text>
                        <CodeInput
                            placeholder=''
                            activeColor={Red}
                            inactiveColor='#343a5e'
                            className={'border-box'}
                            autoFocus={true}
                            ignoreCase={true}
                            inputPosition='center'
                            onFulfill={(text) => Submit(text)}
                            onChangeText={(text) => { setCode(text) }}
                            space={10}
                            codeLength={4}
                            size={70}
                            textAlign="center"
                            containerStyle={{ flexDirection: I18nManager.isRTL == 'ar' ? 'row-reverse' : 'row', justifyContent: "center" }}
                            codeInputStyle={{ borderRadius: 10, backgroundColor: White, borderColor: "#ddd" }}
                            keyboardType={'number-pad'}
                        />
                    </View>

                    <View style={{ height: '50%', width: '100%', alignItems: 'center', justifyContent: 'center' }}>
                        <Text style={styles.text2}>{strings('lang.Resendthecodewithin')}</Text>
                        <Text style={{ color: Red1, fontFamily: appFontBold, textAlign: "center", marginHorizontal: 2, fontSize: screenWidth / 20 }}>{mins}:{secs < 10 && 0}{secs}</Text>
                        <TouchableOpacity style={{ marginBottom: 50 }} disabled={mins == 0 && secs == 0 ? false : true} onPress={() => { setMins(1); setSecs(30); setReload(true); Resend() }}>
                            <Text style={{ color: mins == 0 && secs == 0 ? Red1 : DarkGrey, textDecorationLine: mins == 0 && secs == 0 ? 'none' : 'line-through', fontFamily: appFontBold, textAlign: "center", marginHorizontal: 10 }}>{strings('lang.Resendcode')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>


                {/* <Button onPress={() => { Submit(code) }} style={styles.buttonContainer}>
                    <Text style={styles.buttonText}>{strings('lang.Confirm')}</Text>
                </Button> */}

                {/* <View style={{ paddingTop: 200 }}> */}
                {/* </View> */}
            </KeyboardAwareScrollView>
        </Container>
    )
}

export default ConfirmationCode
const styles = StyleSheet.create({
    text1: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
        marginTop: '30%',
        alignSelf: "center",
        color: Black
    },
    text2: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
        marginTop: '10%',
        alignSelf: "center",
        color: Black

    },
    text3: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
        marginTop: '3%',
        alignSelf: "center",
        color: Red
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '80%',
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '10%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },

});
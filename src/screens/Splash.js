import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from "@react-native-community/netinfo";
import React, { useCallback, useEffect, useState } from 'react';
import {
  I18nManager,
  Image,
  Linking,
  StyleSheet,
  View
} from 'react-native';
import I18n from 'react-native-i18n';
import RNRestart from 'react-native-restart';
import { useDispatch } from 'react-redux';
import * as authActions from '../../Store/Actions/auth';
import * as cartActions from '../../Store/Actions/cart';
import * as farmesActions from '../../Store/Actions/farms';
import * as farmsActions from '../../Store/Actions/farms';
import * as settingsActions from '../../Store/Actions/settings';
import { Red, Red1, screenHeight, White } from '../components/Styles';

const Splash = props => {
  const [farms, setFarms] = useState([]);
  const [sharingTypeId, setSharingTypeId] = useState(1);
  const [typeName, setTypeName] = useState('');
  const [appStatus, setAppStatus] = useState({});
  const [isConnected, setIsConnected] = useState(true);
  const dispatch = useDispatch();

  const logout = async () => {
    dispatch(authActions.logout());
    await AsyncStorage.removeItem('token')
    await AsyncStorage.removeItem('email')
    await AsyncStorage.removeItem('name')
    await AsyncStorage.removeItem('phone')
    await AsyncStorage.removeItem('user')
    await AsyncStorage.removeItem('access_id')
    await AsyncStorage.removeItem('address')
    props.navigation.navigate('Login')
  }

  const InternetCheck = async () => {
    NetInfo.fetch().then(state => {
      console.log("Connection type", state.type);
      console.log("Is connected?", state.isConnected);
      if (state.isConnected == false) {
        setIsConnected(false)
      }
      if (state.isConnected == true) {
        setIsConnected(true)
      }
    });
  }


  useEffect(() => {
    const getSharingTypes = async () => {
      // setLoading(true)

      let response1 = await dispatch(farmesActions.getSharingTypes());
      if (response1.success == true) {
        // sharingTypeId(response1.data[0].id)
        setTypeName(response1.data[2].name)
        getFarms(response1.data[2].id)
      }
      else {
        if (response1.message) {
          Toaster(
            'top',
            'danger',
            Red1,
            response1.message,
            White,
            1500,
            screenHeight / 15,
          );
        }
        // setLoading(false);
      }

    };
    getSharingTypes()
    const getFarms = async (sacrificeId) => {
      try {
        let token = await AsyncStorage.getItem('token')

        // setLoading(true)
        let response = await dispatch(farmesActions.getFarmsRe(null, null, 1, '', sacrificeId));
        if (response.success == true) {
          console.log('responseSplash', response);
          if (response.data.items.length == 0) {
            setFarms([]);
          }
          else {
            setSharingTypeId(sacrificeId)
            setFarms(response.data.items)
            // if (token) {
            //   props.navigation.navigate('Home', {
            //     scrollToIndex: 0,
            //     logo: response.data.items[0].image,
            //     sheepTypeId: sacrificeId,
            //     weightId: null,
            //     sheepCategory: null,
            //     farmId: response.data.items[0].id, link: response.data.items[0].camera_url
            //   })
            // }
            console.log('farms', farms);
          }
        } else {
          if (response.message) {
            Toaster(
              'top',
              'danger',
              Red1,
              response.message,
              White,
              1500,
              screenHeight / 15,
            );
          }
        }
        // setLoading(false);
      } catch (err) {
        console.log('err', err)
        // setLoading(false);
      }
    };
  }, [])

  // useEffect(() => {
  //   async function GetInitalData() {
  //     //   let tokenExisted = false;
  //     try {
  //       let response = await dispatch(settingsActions.getSplash());
  //       let lan = await AsyncStorage.getItem('lan')

  //       if (lan) {
  //         if (lan == 'ar') {
  //           I18n.locale = "ar";
  //           I18nManager.forceRTL(true);
  //           I18nManager.allowRTL(true);
  //         }
  //         else {
  //           I18n.locale = "en";
  //           I18nManager.forceRTL(false);
  //           I18nManager.allowRTL(false);
  //         }
  //       }
  //       else {
  //         await AsyncStorage.setItem('lan', 'ar')
  //         await AsyncStorage.setItem('lang', '0')
  //         I18n.locale = "ar";
  //         I18nManager.forceRTL(true);
  //         RNRestart.Restart();
  //       }

  //       let token = await AsyncStorage.getItem('token')
  //       let token_type = await AsyncStorage.getItem('token_type')
  //       console.log('token', token)
  //       console.log('token_type', token_type)
  //       console.log('lan', lan)

  //       if (token) {
  //         dispatch(authActions.authenticate(JSON.parse(token)));
  //         let user = await dispatch(authActions.user());
  //         if (user.data) {
  //           await dispatch(cartActions.clearCart())
  //           props.navigation.navigate('Home', { weightId: null, sheepCatId: null })
  //         }
  //         else {
  //           logout()
  //         }
  //       }
  //       else {
  //         logout()
  //       }

  //       //       }
  //       //       }
  //       //     });
  //       //   }
  //     }
  //     catch (err) {
  //       console.log('splash err', err)
  //     }
  //   }

  //   setTimeout(() => {
  //     GetInitalData()
  //   }, 3000)
  // }, [isConnected]);

  const handleOpenURL = useCallback(async (url, data) => {
    // const { key, keyCode } = event;
    console.log('url', url);
    let token = await AsyncStorage.getItem('token')
    if (token) {
      navigate(url, data);
    }
    else {
    }

  }, []);

  useEffect(() => {

    async function GetInitalData() {
      //   let tokenExisted = false;
      try {
        let response = await dispatch(settingsActions.getSplash());
        let lan = await AsyncStorage.getItem('lan')

        if (lan) {
          if (lan == 'ar') {
            I18n.locale = "ar";
            I18nManager.forceRTL(true);
            I18nManager.allowRTL(true);
          }
          else {
            I18n.locale = "en";
            I18nManager.forceRTL(false);
            I18nManager.allowRTL(false);
          }
        }
        else {
          await AsyncStorage.setItem('lan', 'ar')
          await AsyncStorage.setItem('lang', '0')
          I18n.locale = "ar";
          I18nManager.forceRTL(true);
          RNRestart.Restart();
        }

        let token = await AsyncStorage.getItem('token')
        let token_type = await AsyncStorage.getItem('token_type')
        console.log('token', token)
        console.log('token_type', token_type)
        console.log('lan', lan)

        if (token) {
          dispatch(authActions.authenticate(JSON.parse(token)));
          let user = await dispatch(authActions.user());
          if (user.data) {
            await dispatch(cartActions.clearCart())
            props.navigation.navigate('Home', {
              // scrollToIndex: 0,
              // logo: farms[0] && farms[0].image,
              // sheepTypeId: sharingTypeId,
              weightId: null,
              sheepCategory: null,
              // farmId: farms[0] && farms[0].id, link: farms[0] && farms[0].camera_url
              link: ''
            })
          }
          else {
            logout()
          }
        }
        else {
          logout()
        }

        //       }
        //       }
        //     });
        //   }
      }
      catch (err) {
        console.log('splash err', err)
      }
    }

    Linking.getInitialURL()
      .then(async (url) => {
        // let urll = 'https://modrk.greencodet.com/44'
        let token = await AsyncStorage.getItem('token')
        console.log('urlurl1', url)
        console.log('token', token);
        if (url && token) {
          let i = url.split("/");
          console.log('asddsa', i);
          dispatch(authActions.authenticate(JSON.parse(token)));
          let response = await dispatch(farmsActions.getTraderFarms(i[4]));
          if (response.success == true) {
            tokenExisted = true
            handleOpenURL(url, response.data)
          }
          else {
            setTimeout(() => {
              GetInitalData()
            }, 3000)
          }
        }
        else {
          setTimeout(() => {
            GetInitalData()
          }, 3000)
        }
      })

    Linking.addEventListener("url", handleOpenURL);
    return () => {
      Linking.removeEventListener("url", handleOpenURL);
    };
  }, [handleOpenURL]);

  const navigate = (url, data) => { // E
    console.log('urlurl', url)
    props.navigation.navigate('TraderAllContent', { trader_id: data.trader.id, farms: data.farms })
    // props.navigation.push('Live', { item: data, link: data.camera_url, deepLink: true })
  }

  return (
    <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center', backgroundColor: Red }}>
      <Image source={require('../images/modrek/logo.png')} style={{
        width: '50%',
        height: '100%',
        alignSelf: 'center',
        resizeMode: 'contain'
        // tintColor:'white'
      }}
        resizeMode={'contain'}
      />
    </View>
  );
};

const styles = StyleSheet.create({});

export default Splash;

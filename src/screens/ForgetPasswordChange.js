import React, { useEffect, useState } from 'react';
import { Image, View, Text, StyleSheet, I18nManager, BackHandler, } from "react-native";
import { screenHeight, DarkBlue, screenWidth, DarkGrey, White, appFont, appFontBold, DarkGreen, Red, Green, appColor1, app<PERSON>olor2, <PERSON><PERSON><PERSON>, <PERSON>1, <PERSON>, } from "../components/Styles";

import { Input, Container, Toast, Button, } from 'native-base';
import { ScrollView, TouchableOpacity, } from 'react-native-gesture-handler';
import { strings } from './i18n'
import I18n from 'react-native-i18n';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Header from '../components/Header';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import LoadingMore from '../components/LoadingMore';
import AsyncStorage from '@react-native-async-storage/async-storage';

const ForgetPasswordChange = props => {
    const [password, setPassword] = useState('')
    const [error_password, setError_Password] = useState('')
    const [activeText, setActiveText] = useState(0);
    const [confirmPassword, setConfirmPassword] = useState('')
    const [error_confirmPassword, setError_confirmPassword] = useState('')
    const [conditions, setConditions] = useState(false)
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();


    useEffect(() => {

    }, []);

    const submit = async () => {
        if (password == '') {
            setError_Password(strings('lang.Please_insert_password'))
        }
        else if (password.length < 6) {
            setError_Password(strings('lang.Password_must_be_more_6_characters'))
        }
        else if (confirmPassword == '') {
            setError_confirmPassword(strings('lang.Please_insert_password'))
        }
        else if (confirmPassword.length < 6) {
            setError_confirmPassword(strings('lang.Password_must_be_more_6_characters'))
        }
        else if (password != confirmPassword) {
            setError_confirmPassword(strings('lang.Passwordnotright'))
        }
        else {
            let phone = props.route.params.phone;
            try {
                setLoadingMore(true)
                let ResetPassword = await dispatch(authActions.ResetPassword(phone, '1234', password, confirmPassword));
                if (ResetPassword.success == true) {
                    Toaster(
                        'top',
                        'success',
                        'green',
                        (strings('lang.passwordchangedsuccessfully')),
                        White,
                        1500,
                        screenHeight / 15,
                    );
                    // AsyncStorage.setItem("token", JSON.stringify(Login.data.token));
                    // AsyncStorage.setItem("email", JSON.stringify(Login.data.user.email));
                    // AsyncStorage.setItem("name", JSON.stringify(Login.data.user.name));
                    // AsyncStorage.setItem("phone", JSON.stringify(Login.data.user.phone));
                    // AsyncStorage.setItem("user", JSON.stringify(Login.data.user));
                    // AsyncStorage.setItem("access_id", JSON.stringify(Login.data.user.id));
                    props.navigation.navigate('Login')
                    setLoadingMore(false);
                } else {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        ResetPassword.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                setLoadingMore(false);
            }
            catch (err) {
                console.log('err', err)
                setLoadingMore(false);
            }
        }
    }

    return (
        <Container>
            <Header title={strings('lang.Changepassword')} backPress={() => { props.navigation.goBack() }} />
            {loadingMore ? <LoadingMore /> : <></>}

            <KeyboardAwareScrollView>
                <View style={{ width: '60%', height: screenHeight / 4, alignSelf: "center", alignItems: "center", justifyContent: "center", marginBottom: screenHeight / 40 }}>
                    <Image source={require('../images/Silal/lock.png')} style={{ width: '100%', height: '80%', resizeMode: "contain", alignSelf: "center", tintColor: Red }} />
                </View>

                <View style={styles.inputsContainer}>

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.NewPassword')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 2 ? styles.activeInput : styles.input}
                            value={password} onChangeText={(text) => {
                                setPassword(text)
                                setError_Password('')
                            }}
                            onFocus={() => setActiveText(2)}
                            onBlur={() => { setActiveText(0) }}
                            placeholderTextColor={'#707070'} placeholder={(strings('lang.NewPassword'))}
                        />
                    </View>
                    {error_password ?
                        <Text style={styles.loginError}>{error_password}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <View style={styles.labelContainer}>
                        <Text style={styles.text}>{strings('lang.ConfirmNewPassword')}</Text>
                        <Text style={styles.star}> * </Text>
                    </View>
                    <View>
                        <Input
                            style={activeText == 3 ? styles.activeInput : styles.input}
                            value={confirmPassword} onChangeText={(text) => {
                                setConfirmPassword(text)
                                setError_confirmPassword('')
                            }}
                            onFocus={() => setActiveText(3)}
                            onBlur={() => { setActiveText(0) }}
                            placeholderTextColor={'#707070'} placeholder={(strings('lang.ConfirmNewPassword'))}
                        />
                    </View>
                    {error_confirmPassword ?
                        <Text style={styles.loginError}>{error_confirmPassword}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }
                </View>


                <Button onPress={() => { submit() }} style={styles.buttonContainer}>
                    <Text style={styles.buttonText}>{strings('lang.Change')}</Text>
                </Button>


            </KeyboardAwareScrollView>

            {/* <View style={{ height: screenHeight / 9 }}></View> */}


            {/* <MyFooter navigation={props.navigation} /> */}
        </Container>


    );
}


export default ForgetPasswordChange

const styles = StyleSheet.create({
    text: {
        fontFamily: appFont,
        fontSize: screenWidth / 26,
        marginStart: '2%',
        color: Black
    },
    input: {
        height: 45, borderWidth: 1, borderRadius: 25, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 28, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    activeInput: {
        height: 45, borderWidth: 1, borderRadius: 25, borderColor: Red, flexDirection: 'row', justifyContent: 'center',
        width: "100%", fontSize: screenWidth / 28, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left'
    },
    texttitile: {
        marginVertical: 10,
        fontFamily: appFont,
        fontSize: screenWidth / 25,
        textDecorationStyle: 'solid',
        color: appColor1,
        textDecorationLine: "underline"
    },
    inputsContainer: {
        width: '90%', alignSelf: "center", justifyContent: "center",
    },
    star: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 20,
        color: Red1
    },
    labelContainer: { flexDirection: 'row', alignItems: "center", marginBottom: 5 },
    loginError: {
        color: Red1, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'left' : 'right'
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '90%',
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '10%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
});

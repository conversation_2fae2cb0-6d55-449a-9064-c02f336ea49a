Map


import React, { Component, useCallback, useEffect, useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, PermissionsAndroid, BackHandler, Platform, Linking, I18nManager } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor2, Red, Black, Red1 } from '../components/Styles';
import { Button } from 'native-base';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import Geolocation from 'react-native-geolocation-service';
import MapView, { Marker } from 'react-native-maps';
import { GooglePlacesAutocomplete } from 'react-native-google-places-autocomplete';
import Geocoder from 'react-native-geocoding';

import { strings } from './i18n';
import Loading from '../components/Loading';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { useDispatch, useSelector } from 'react-redux';
import { useFocusEffect } from '@react-navigation/native';
import Toaster from '../components/Toaster';
import * as cartActions from '../../Store/Actions/cart';
import BackgroundTimer from 'react-native-background-timer';

const ASPECT_RATIO = screenWidth / screenHeight
Geocoder.init("AIzaSyCgd4958qc1SDBSnNT-xhIXawnPhVq0V9A"); // use a valid API key

const Map = props => {
    const dispatch = useDispatch();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [region, SetRegion] = useState({
        latitude: 37.42209348000618,
        longitude: -122.0839218981564,
        longitudeDelta: 0.01 * ASPECT_RATIO,
        latitudeDelta: 0.01
    })
    const [region1, SetRegion1] = useState({
        latitude: 37.42209348000618,
        longitude: -122.0839218981564,
        longitudeDelta: 0.01 * ASPECT_RATIO,
        latitudeDelta: 0.01
    })

    const [lat, SetLat] = useState(0)
    const [lng, SetLng] = useState(0)

    const [addressDesription, setAddressDesription] = useState('');
    const cartt = useSelector(state => state.cart.cart)
    const [cartItems, setCartItems] = useState(cartt.sheep);
    const [timeLeft, setTimeLeft] = useState(props.route.params.timeLeft); // 10 minutes in seconds
    const timerRef = useRef(null);

    // Start Timer Function
    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };
    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart())
    };
    // Stop Timer Function
    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };
    // Reset Timer to 10 minutes
    const resetTimer = () => {
        setTimeLeft(props.route.params.timeLeft);
    };

    // Start timer when screen is focused & Reset when leaving
    useFocusEffect(
        useCallback(() => {
            resetTimer(); // Reset every time you open the screen
            startTimer();

            // Cleanup when screen is unfocused (leaving screen)
            return () => {
                stopTimer();
                resetTimer(); // Reset when leaving the screen
            };
        }, [])
    );
    useEffect(() => {

        const onRegionChangeComplete = (region) => {

            var initial_Region = {
                longitudeDelta: region.longitudeDelta,
                latitudeDelta: region.latitudeDelta,
                longitude: region.longitude,
                latitude: region.latitude
            }

            onRegionChangeComplete.bind(initial_Region)

            console.log('region', region);

        }

        const requestLocationPermission = async () => {
            try {
                const granted = await PermissionsAndroid.request(
                    PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
                    {
                        'title': 'Location Permission',
                        'message': 'MyMapApp needs access to your location'
                    }
                )

                if (granted === PermissionsAndroid.RESULTS.GRANTED) {
                    get_Lat_User()
                    // Alert.alert("Please allow location")
                } else {
                    console.log("Location permission denied")
                    setLoading(false)

                }
            } catch (err) {
                console.warn(err)
                setLoading(false)
            }
        }

        const RequestIosPermissio = () => {
            Geolocation.requestAuthorization('always').then((res) => {
                console.log('res', res);
                if (res == 'denied') {
                    //  Alert.alert("Please allow location to continuo")/
                    // Linking.openURL('app-settings:');
                    showCustomAlert()
                }
            });
            setLoading(false)
        }

        const showCustomAlert = () => {
            Alert.alert(
                'Permission Alert',
                'The app will not work properly without this permission ',
                [
                    {
                        text: 'Access permission from settings',
                        onPress: () => {
                            console.log('start permission');
                            Linking.openURL('app-settings:');
                            // get_Lat_User()
                        },
                    },
                    {
                        text: 'Cancel',
                        onPress: () => {
                            console.log('exit');
                            props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                        },
                    },
                ],
                { cancelable: false }
            );
        };

        const get_Lat_User = async () => {
            // Geolocation.requestAuthorization();
            // Geolocation.setRNConfiguration({
            //   skipPermissionRequests: false,
            //   authorizationLevel: 'whenInUse',
            // });
            if (Platform.OS == 'ios') {
                RequestIosPermissio()
            }

            Geolocation.getCurrentPosition(
                (position) => {
                    console.log('position', position);
                    var lat = parseFloat(position.coords.latitude)
                    var longi = parseFloat(position.coords.longitude)
                    var initialRegion = {
                        latitude: lat,
                        longitude: longi,
                        longitudeDelta: 0.01 * ASPECT_RATIO,
                        latitudeDelta: 0.01
                    }
                    SetRegion(initialRegion)
                    SetLat(lat)
                    SetLng(longi)
                    go(lat, longi)
                },
                (error) => {
                    console.log('error', error);

                },
                { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
            );
            setLoading(false)
        }

        if (Platform.OS == 'ios') {
            get_Lat_User()
        }
        else {
            requestLocationPermission();
        }

    }, []);

    const RequestIosPermissio = () => {
        Geolocation.requestAuthorization('always').then((res) => {
            console.log('res', res);
            if (res == 'denied') {
                //  Alert.alert("Please allow location to continuo")/
                // Linking.openURL('app-settings:');
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        });
        setLoading(false)
    }

    const get_Lat_User = async () => {
        SetRegion(region1)
        console.log('newRegion', region);
        // Geolocation.requestAuthorization();
        // Geolocation.setRNConfiguration({
        //   skipPermissionRequests: false,
        //   authorizationLevel: 'whenInUse',
        // });
        if (Platform.OS == 'ios') {
            RequestIosPermissio()

        }
        // console.log('SetRegion', region);
        Geolocation.getCurrentPosition(
            async (position) => {
                console.log('position', position);
                var lat = parseFloat(position.coords.latitude)
                var longi = parseFloat(position.coords.longitude)
                var initialRegion = {
                    latitude: lat,
                    longitude: longi,
                    longitudeDelta: 0.01 * ASPECT_RATIO,
                    latitudeDelta: 0.01
                }
                SetRegion(initialRegion)
                console.log('Region', region);

                SetLat(lat)
                SetLng(longi)
                console.log('lat', lat, longi);
                Geocoder.init("AIzaSyCgd4958qc1SDBSnNT-xhIXawnPhVq0V9A");
                await Geocoder.from(lat, longi, 'ar')
                    .then(json => {
                        var addressComponent = json.results[0].formatted_address;
                        console.log(addressComponent);
                        setAddressDesription(addressComponent)
                    })
                    .catch(error => console.log('error', error));

            },
            (error) => {
                console.log('error', error);

            },
            { enableHighAccuracy: false, showLocationDialog: true, timeout: 20000, maximumAge: 1000 },
        );
        setLoading(false)
    }
    const [isFirstTime, setIsFirstTime] = useState(true)

    const onRegionChangeComplete = (newRegion) => {
        // console.log('newRegion', newRegion);

        if (Platform.OS == 'android') {
            // if (loadingMore) {
            //     return;
            // }
            if (isFirstTime) {
                setLoadingMore(true)

                Geolocation.getCurrentPosition(
                    async position => {
                        console.log('position', position);
                        var lat = parseFloat(position.coords.latitude)
                        var longi = parseFloat(position.coords.longitude)
                        var initialRegion = {
                            latitude: lat,
                            longitude: longi,
                            longitudeDelta: 0.01 * ASPECT_RATIO,
                            latitudeDelta: 0.01
                        }
                        SetRegion(initialRegion)
                        SetLat(lat)
                        SetLng(longi)

                        go(lat, longi)
                        // console.log('region', initialRegion);
                        setLoadingMore(false)
                        setIsFirstTime(false)
                    },
                    error => {
                        console.error(error);
                    },
                    { enableHighAccuracy: true, timeout: 20000, maximumAge: 1000 }
                );
            }

        }

        setLoadingMore(true)
        var initial_Region = {
            longitudeDelta: newRegion.longitudeDelta,
            latitudeDelta: newRegion.latitudeDelta,
            longitude: newRegion.longitude,
            latitude: newRegion.latitude
        }


        SetRegion1(newRegion);

        SetLat(newRegion.latitude)
        SetLng(newRegion.longitude)

        go(newRegion.latitude, newRegion.longitude)
        // console.log('region', newRegion);
        setLoadingMore(false)
        // // for (let city of working_cities) {
        // //   let isPointInPolygon = geolib.isPointInPolygon(region, city.coordinates)
        // //   setIsPointInPolygon(isPointInPolygon)
        // //   if (isPointInPolygon) {
        // //     return;
        // //   }
        // // }
    }

    const [userInteracted, setUserInteracted] = useState(false);

    const HandleSearch = (data, details) => {

        setLoadingMore(true)

        console.log("data", data)
        console.log("details", details)
        let initial_Region = {
            longitudeDelta: 0.01 * ASPECT_RATIO,
            latitudeDelta: 0.01,
            longitude: details.geometry.location.lng,
            latitude: details.geometry.location.lat,
        }
        ChangeLocation(initial_Region)
    }

    const ChangeLocation = (region) => {
        var initial_Region = {
            longitudeDelta: 0.01 * ASPECT_RATIO,
            latitudeDelta: 0.01,
            longitude: region.longitude,
            latitude: region.latitude
        }
        SetRegion(initial_Region)
        SetLat(region.latitude)
        SetLng(region.longitude)
        go(region.latitude, region.longitude)
    }

    const go = async (latitude, longitude,) => {
        if (latitude && longitude) {
            Geocoder.init("AIzaSyCgd4958qc1SDBSnNT-xhIXawnPhVq0V9A");
            await Geocoder.from(latitude, longitude, 'ar')
                .then(json => {
                    var addressComponent = json.results[0].formatted_address;
                    console.log(addressComponent);
                    setAddressDesription(addressComponent)
                })
                .catch(error => console.log('error', error));
        }

        setLoadingMore(false)
    }
    return (
        <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
            <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.Select_your_location')}
                backPress={() => { props.navigation.push('Addresses', { screen: props.route.params.screen, shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }}
            />


            <View style={{
                zIndex: 2, position: 'absolute', top: '45%', padding: '3%',
                width: '50%',
                backgroundColor: Red,
                borderRadius: 15
            }}>
                {/* {loadingAddress ? <Loading /> : */}
                <Text
                    numberOfLines={2}
                    style={{
                        color: White, textAlign: I18nManager.isRTL ? 'left' : 'right',
                        fontFamily: appFontBold, fontSize: screenWidth / 33
                    }}>
                    {addressDesription}
                </Text>
                {/* } */}
            </View>

            <Image source={require('../images/pin3.png')} style={{ resizeMode: "contain", width: '12%', height: '8%', zIndex: 1000000, position: 'absolute', start: '45%', top: '52.5%' }} />

            {loadingMore ? <Loading /> : <></>}

            <View style={{ overflow: 'hidden', zIndex: 100000000, position: 'absolute', top: screenHeight / 9, height: screenHeight / 4.2, borderRadius: 10 }}>
                <GooglePlacesAutocomplete
                    placeholder={strings('lang.Search')}
                    minLength={2}
                    fetchDetails={true}
                    styles={{
                        // textInputContainer: { position: 'absolute', top: 20, height: 50, width: '80%', alignSelf: 'center', borderRadius: 10,borderColor: MediumGrey, borderWidth: 1, overflow: 'hidden' },
                        textInputContainer: { height: screenHeight / 18, width: screenWidth / 1.1, alignSelf: 'center', borderRadius: 100, borderColor: Red, borderWidth: 1, overflow: 'hidden', },
                        textInput: { height: '100%', color: Black, fontSize: 16, fontFamily: appFont, textAlign: I18nManager.isRTL ? 'right' : 'left' },
                        predefinedPlacesDescription: { color: Black, },
                        poweredContainer: { width: 0, height: 0 },
                        powered: { width: 0, height: 0 },
                        description: { color: Black },
                        listView: { position: 'absolute', width: '100%', top: screenHeight / 18, alignSelf: 'center', zIndex: 100000000, height: screenHeight / 2, borderRadius: 10 },
                    }}
                    onPress={(data, details) => { HandleSearch(data, details) }}
                    query={{
                        key: 'AIzaSyCgd4958qc1SDBSnNT-xhIXawnPhVq0V9A',
                        language: 'ar',
                    }}
                    onFail={error => console.error(error)}
                    listUnderlayColor={Black}
                // nearbyPlacesAPI='GooglePlacesSearch'
                // debounce={200}
                />
            </View>

            {/* {display == true
                &&
                <MapView
                    region={initialRegion}
                    style={{ width: "100%", height: '100%', zIndex: 100 }}
                // ref={ref => map = ref}
                >
                 
                </MapView>
            } */}


            <MapView
                region={region}
                // initialRegion={region}
                // showsUserLocation={true}
                // showsMyLocationButton={false}
                // followsUserLocation={true}
                // showsCompass={true}
                scrollEnabled={true}
                zoomEnabled={true}
                // minZoomLevel={17}
                pitchEnabled={true}
                rotateEnabled={true}
                // moveOnMarkerPress={true}
                // onMarkerPress={() => { setMapp(true) }}
                // height: driver || details ? screenHeight / 1.66 : screenHeight / 1.95,
                onRegionChangeComplete={onRegionChangeComplete.bind(this)}
                // onMapReady={async () => setUserInteracted(true)}
                style={{ height: screenHeight, width: screenWidth, zIndex: 1 }}
            >
                {/* {longitude && latitude ?
                        <Marker
                            // draggable
                            coordinate={{
                                latitude: latitude,
                                longitude: longitude
                            }}
                        />
                        :
                        <View></View>
                    } */}
            </MapView>


            {/* <View style={{ width: screenWidth / 10, height: screenHeight / 7, backgroundColor: appColor2, borderWidth: .3, borderColor: DarkGrey, borderRadius: 15, elevation: 10, zIndex: 100000000, position: 'absolute', bottom: '15%', left: '7%', justifyContent: 'space-around', alignItems: 'center' }} >
                <TouchableOpacity
                    style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: screenWidth / 24, backgroundColor: 'white', alignItems: 'center', justifyContent: 'center' }}
                    onPress={() => { onPressZoomOut() }}
                >
                    <Image
                        style={{ width: '60%', height: '60%', resizeMode: 'contain', }}
                        source={require('../images/Silal/Add.png')}
                    />
                </TouchableOpacity>
                <TouchableOpacity
                    style={{ width: screenWidth / 14, height: screenWidth / 14, borderRadius: screenWidth / 24, backgroundColor: 'white', alignItems: 'center', justifyContent: 'center' }}
                    onPress={() => { onPressZoomIn() }}
                >
                    <Image
                        style={{ width: '60%', height: '60%', resizeMode: 'contain' }}
                        source={require('../images/Silal/Sub.png')}
                    />
                </TouchableOpacity>
            </View> */}

            <View style={{ width: 60, height: 60, zIndex: 100, position: "absolute", bottom: '15%', right: '7%', borderRadius: 30, elevation: 20, backgroundColor: "white", alignItems: 'center', justifyContent: 'center', borderWidth: .5, borderColor: DarkGrey }} >
                <TouchableOpacity
                    onPress={() => {
                        get_Lat_User()
                    }}
                >
                    <Image
                        style={{ width: 25, height: 25, resizeMode: 'contain', tintColor: DarkGrey }}
                        source={require('../images/mylocation.png')}
                    />
                </TouchableOpacity>
            </View>

            {/* 
            {display == true
                ?
                <View></View>
                : */}
            <View style={styles.btnContainer}>
                <Button style={styles.buttonContainer} onPress={() => props.navigation.push('AddressDetails', { lat: lat, lng: lng, addressDesription: addressDesription, screen: props.route.params.screen, shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft, screen: props.route.params.screen })}>
                    <Text style={styles.buttonText}>{strings('lang.Locate')}</Text>
                </Button>
            </View>
            {/* } */}

            {/* <MyFooter navigation={props.navigation} /> */}
        </View>
    )
}

export default Map;
const styles = StyleSheet.create({
    img: {
        width: '40%',
        height: '40%',
    },
    btnContainer: {
        zIndex: 2, flexDirection: "row", width: "90%", alignSelf: "center", justifyContent: "center", marginBottom: screenHeight / 20, position: 'absolute', bottom: 0
    },
    buttonContainer: {
        width: '100%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginBottom: 0,
        zIndex: 3
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
});


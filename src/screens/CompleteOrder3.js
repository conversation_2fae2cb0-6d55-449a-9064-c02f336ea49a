import React, { useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Alert } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor1, Black, appColor2, LightGreen, Red, Red1 } from '../components/Styles';
import { Button, Input, Textarea, Toast } from 'native-base';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
import { KeyboardAwareScrollView, ScrollableComponent } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import LoadingMore from '../components/LoadingMore';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as cartActions from '../../Store/Actions/cart';
import * as ordersActions from '../../Store/Actions/orders';
import Toaster from '../components/Toaster';
import RNRestart from 'react-native-restart';
import appsFlyer from 'react-native-appsflyer';

const CompleteOrder3 = props => {
    const [shared, setShared] = useState(props.route.params.shared);
    const [order, setOrder] = useState(props.route.params.order)

    const [address, setAddress] = useState({});
    const [discount, setDiscount] = useState('');
    const [methodId, setMethodId] = useState(0);
    const [review, setReview] = useState('');
    const refRBSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();

    const minss = useSelector(state => state.cart.mins)
    const secss = useSelector(state => state.cart.secs)
    const [mins, setMins] = useState(minss)
    const [secs, setSecs] = useState(secss)

    const cartt = useSelector(state => state.cart.cart)
    const [cart, setCart] = useState(cartt);
    const [cartItems, setCartItems] = useState(order ? order.sheep : cartt.sheep);

    useEffect(() => {
        const getAddresss = async () => {
            const address = await AsyncStorage.getItem("address");
            setAddress(JSON.parse(address));
        }
        getAddresss()


        const SubmitHome = async () => {
            setLoadingMore(true)

            try {
                let response = await dispatch(ordersActions.CreateOrder());
                if (response.success == true) {
                    refRBSheet.current.open()
                    setLoadingMore(true)
                    // await dispatch(cartActions.clearCart())
                    // dispatch(cartActions.timer(10, 0));
                    setOrder(response.data)
                    console.log('order', response.data);

                    logPurchaseEvent(response.data)
                }
                else {
                }
                setLoadingMore(false)
            }
            catch (err) {
                console.log('err', err)
                setLoadingMore(false)
            }

        }


        const logPurchaseEvent = (item) => {
            const eventName = 'af_purchase';
            const eventValues = {
                af_content_id: item.id.toString(),
                // af_content_type: 'product',
                af_revenue: item.total,
                af_currency: 'SAR',
            };

            appsFlyer.logEvent(eventName, eventValues,
                (res) => {
                    console.log('Event sent successfully:', res);
                },
                (err) => {
                    console.error('Event failed to send:', err);
                }
            );
        };
        SubmitHome()
    }, []);

    const Submit = async () => {
        refRBSheet.current.open()
    }

    const SubmitHome = async () => {
        props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
        setLoadingMore(false)
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header
                    title={strings('lang.Completetheapplication')} backPress={() => { SubmitHome() }} /> */}

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 100, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 5,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 10,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 10,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 40, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 5,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '20%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center', flexDirection: 'row', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 3,
                                    height: screenHeight / 13,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenWidth / 4,
                                    height: screenHeight / 20,
                                    borderRadius: 20,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header
                    title={strings('lang.Completetheapplication')} backPress={() => { SubmitHome() }} /> */}

                {loadingMore ? <LoadingMore /> : <></>}

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                >
                    {/* <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 3, borderColor: WhiteGery, backgroundColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'center' }}>
                        <Image source={require('../images/modrek/alert.png')} style={{ resizeMode: 'contain', width: '10%', height: '40%', tintColor: Red }} />
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, marginStart: '1%' }}>{mins}:{secs < 10 && 0}{secs}</Text>
                    </View> */}

                    <View style={{
                        flexDirection: 'row', width: "100%", alignSelf: "center", justifyContent: 'space-between', alignItems: "flex-end", height: screenHeight / 9,
                        backgroundColor: Red,
                    }}>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Cuttingandprocessing')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Titleanddate')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.payingoff')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Orderdetails')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                    </View>

                    <Text style={styles.title}>{strings('lang.Orders')}</Text>
                    <FlatList
                        data={cartItems}
                        showsVerticalScrollIndicator={false}
                        renderItem={({ item }) =>
                            <View style={{ flexDirection: 'row', width: screenWidth / 1.1, height: screenHeight / 10, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginVertical: 4, alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start' }}>
                                <View style={{ width: screenWidth / 8, height: 45, backgroundColor: item.sheep.collar_color, alignItems: 'center', justifyContent: 'center', marginEnd: '5%', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60 }}>
                                    <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 30, }}>{item.sheep.id}</Text>
                                </View>

                                <View style={{ flexDirection: 'column' }}>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 33, }}>{`${item.sheep.sheep_category.name} / ${item.sheep.farm.weight.from}KG - ${item.sheep.farm.weight.to}KG / ${item.sheep.farm.code} `}</Text>
                                    <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 33, marginHorizontal: '5%', alignSelf: 'flex-start' }}>{item.sheep.price} {strings('lang.SR')}</Text>
                                </View>
                            </View>
                        }
                        keyExtractor={item => item.id}
                        style={{ alignSelf: "center", width: screenWidth / 1.1, overflow: "hidden", flexDirection: "column", flexWrap: "wrap", }}
                    // onEndReached={() => this.LoadMore()}
                    // onEndReachedThreshold={0.1}
                    // numColumns={2}
                    />


                    {/* <View style={{ flexDirection: 'row', width: screenWidth / 1.1, height: screenHeight / 13, borderWidth: 1, borderRadius: 7, borderColor: MediumGrey, marginBottom: screenHeight / 100, alignItems: 'center', alignSelf: 'center', justifyContent: 'flex-start' }}>
                        <View style={{ width: screenWidth / 8, height: '70%', backgroundColor: Red, alignItems: 'center', justifyContent: 'center', marginEnd: '5%', marginStart: '2%', alignSelf: 'center', borderRadius: screenWidth / 60 }}>
                            <Text style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 24, }}>1</Text>
                        </View>
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, }}>A70 - 25 Kg</Text>
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, marginHorizontal: '5%' }}>SR 1200</Text>
                    </View> */}

                    {shared
                        ?
                        <>
                            <Text style={{ marginVertical: 10, fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start', marginStart: '2.5%' }}>{strings('lang.Yourpriceowed')}</Text>
                            <View style={{ width: '95%', alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '2%' }}>
                                <View style={{ width: '75%', }}>
                                    <Text style={styles.textGrey1}>{strings('lang.Thepriceisinclusiveofqtm')}</Text>
                                </View>
                                <View style={{ width: '25%', alignItems: "flex-end" }}>
                                    {order.payment_type == "Shared"
                                        ?
                                        <Text style={styles.textGreen1}> {order.shared_value}  {strings('lang.SAR')}</Text>
                                        :
                                        <Text style={styles.textGreen1}> {'0'}  {strings('lang.SAR')}</Text>
                                    }
                                </View>
                            </View>
                        </>
                        :
                        <View style={{ width: '100%', alignSelf: "center", flexDirection: "row", marginVertical: 10, backgroundColor: WhiteGery, padding: '5%', }}>
                            <View style={{ width: '75%', }}>
                                <Text style={styles.textGrey1}>{strings('lang.Totalproductsinclusiveofqqt')}</Text>
                                <Text style={styles.textGrey1}>{strings('lang.Delivery')}</Text>
                                {cart.coupon
                                    &&
                                    <Text style={styles.textGrey1}>{strings('lang.Discount')}</Text>
                                }
                                {cart.payment_method && cart.payment_method.id == 4
                                    &&
                                    <Text style={styles.textGrey1}>{strings('lang.COD')}</Text>
                                }
                                <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: 5 }}></View>
                                <Text style={styles.textGrey1}>{strings('lang.Finaltotal')}</Text>
                            </View>
                            <View style={{ width: '25%', alignItems: "flex-end" }}>
                                <Text style={styles.textBlack0}>{cart.sub_total} {strings('lang.SAR')}</Text>
                                <Text style={styles.textBlack0}> {cart.delivery_fees} {strings('lang.SAR')}</Text>
                                {cart.coupon
                                    &&
                                    <Text style={styles.textBlack0}>- {cart.discount} {strings('lang.SAR')}</Text>
                                }
                                {cart.payment_method && cart.payment_method.id == 4
                                    &&
                                    <Text style={styles.textBlack0}> {cart.cod} {strings('lang.SAR')}</Text>
                                }
                                <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: 5 }}></View>
                                <Text style={styles.textRed1}> {cart.total} {strings('lang.SAR')}</Text>

                            </View>
                        </View>
                    }



                    <Text style={styles.title}>{strings('lang.Address')}</Text>

                    {address && address.address ?
                        <View style={{ flexDirection: 'row', width: '90%', alignSelf: 'center', height: screenHeight / 12, backgroundColor: WhiteGery, borderRadius: 10, paddingHorizontal: '3%', justifyContent: "space-between", alignItems: "center", marginVertical: screenHeight / 100 }}>
                            <View style={{ width: '10%', height: '90%', alignItems: "center", alignSelf: 'center' }}>
                                <Image source={require('../images/modrek/location.png')} style={{ height: '100%', width: '80%', resizeMode: "contain", tintColor: Red }} />
                            </View>
                            <View style={{ width: '90%', height: '90%', alignSelf: 'center' }}>
                                <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, marginStart: '5%', alignSelf: 'flex-start' }}>{address.address}</Text>
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, marginStart: '5%', alignSelf: 'flex-start' }}>{address.landmark ? address.landmark : ''} {address.landmark ? '-' : ''}  {address.building ? address.building : ''}</Text>
                            </View>
                        </View>
                        :
                        <></>
                    }

                    {/* <View style={{ flexDirection: 'row', width: '90%', alignSelf: 'center', height: screenHeight / 10, backgroundColor: White, paddingHorizontal: '3%', justifyContent: "space-between", alignItems: "center", marginVertical: screenHeight / 100 }}>
                            <View style={{ width: '10%', height: '90%', alignItems: "center", }}>
                                <Image source={require('../images/modrek/location.png')} style={{ height: '50%', resizeMode: "contain", tintColor: Red }} />
                            </View>
                            <View style={{ width: '90%', height: '90%', }}>
                                {this.state.address.city
                                    ?
                                <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{'جدة'}</Text>
                                :
                                    <View></View>
                                }
                                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{this.state.address.landmark}</Text>
                            </View>
                        </View> */}

                    <View style={{ flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse', alignItems: 'flex-end', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, marginVertical: '10%' }}>
                        <Button onPress={() => { SubmitHome() }} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Image source={require('../images/modrek/arrowRight.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: Black, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} />
                            <Text style={{ fontSize: screenWidth / 44, fontFamily: appFontBold, color: Black, marginHorizontal: '1%' }}>{strings('lang.Returntothehomepage')}</Text>
                        </Button>
                        <Button onPress={() => Submit()} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10 }}>
                            <Image source={require('../images/modrek/continue.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? -1 : 1 }], tintColor: White, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} />
                            <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.Confirm')}</Text>
                        </Button>
                    </View>
                </ScrollView>

                <View style={{ height: screenHeight / 10, }}></View>

                <RBSheet
                    ref={refRBSheet}
                    height={screenHeight / 2}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            width: '90%',
                            marginBottom: screenHeight / 4,
                            alignSelf: 'center',
                            borderRadius: 5

                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            // width: '25%',
                            // backgroundColor: DarkGrey
                        }
                    }}
                    closeOnDragDown={false}
                    closeOnPressMask={false}
                    closeOnPressBack={false}
                // onClose={() => { props.navigation.navigate('Home') }}
                // closeOnDragDown={true}
                >
                    <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'center', width: '100%', paddingVertical: '3%', }}>

                        {/* <View style={{ width: '80%', marginStart: 15, height: '40%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '80%', height: '90%', tintColor: Red, }} />
                        </View> */}
                        <View style={{ width: '80%', height: '40%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/modrek/surface-7.png')} style={{ resizeMode: 'contain', width: '60%', height: '60%', }} />
                        </View>

                        <View style={{ height: '30%', width: '90%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', flexDirection: 'row' }}>
                            <Text style={{ fontSize: screenWidth / 20, fontFamily: appFontBold, color: Red, }}>{shared ? strings('lang.Therequestwasaccepted') : strings('lang.Buyingsucceeded')}</Text>
                            {/* <Image source={require('../images/modrek/surface-7.png')} style={{ resizeMode: 'contain', width: '15%', height: '20%', }} /> */}
                        </View>

                        <View style={{ alignItems: "center", justifyContent: 'center', width: '90%', alignSelf: "center", height: '30%' }}>
                            <Button onPress={() => {
                                refRBSheet.current.close();
                                props.navigation.push("OrderTracking", { item: order })
                            }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                                <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Ordertracking')}</Text>
                            </Button>
                            <Button onPress={() => {
                                refRBSheet.current.close();
                                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                            }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                                <Image source={require('../images/modrek/svgexport-1.png')} style={{ resizeMode: 'contain', width: '10%', height: '50%', tintColor: Black, }} />
                                <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.Home')}</Text>
                            </Button>
                        </View>
                    </View>
                </RBSheet>

                {/* <MyFooter navigation={props.navigation} /> */}
            </View>
        )
    }
}

export default CompleteOrder3;
const styles = StyleSheet.create({
    textBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start' },
    textBlack0: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-end' },
    textBlack1: { fontFamily: appFont, color: Black, fontSize: screenWidth / 24, alignSelf: 'flex-start' },
    textAddress: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26 },
    textRed1: { fontFamily: appFontBold, color: Red, fontSize: screenWidth / 24 },
    textGrey: { fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 26, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey2: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },
    textGrey1: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 },
    textWhite1: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 24 },
    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    textGreen1: { fontFamily: appFontBold, color: 'green', fontSize: screenWidth / 24 },
    squareGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: 2, marginEnd: '3%' },
    circleGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: screenHeight / 100, marginStart: '20%', marginEnd: '3%' },
});
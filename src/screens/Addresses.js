import React, { useCallback, useState, useRef } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, BackHandler, Modal, TouchableNativeFeedback } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, Black, appColor2, Red, Red1 } from '../components/Styles';
import { Button, Left, Toast } from 'native-base';
import MyFooter from '../components/MyFooter';
import Header from '../components/Header';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import { SwipeListView } from 'react-native-swipe-list-view';
import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as addressesActions from '../../Store/Actions/addresses';
import Toaster from '../components/Toaster';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as cartActions from '../../Store/Actions/cart';
import { useFocusEffect } from '@react-navigation/native';
import BackgroundTimer from 'react-native-background-timer';

const Addresss = props => {

    const [Addresses, setAddresses] = useState([]);
    const [ModalVisible, setModalVisible] = useState(false);
    const [ModalVisibleCart, setModalVisibleCart] = useState(false);
    const [addressId, setAddressId] = useState('');
    const [item, setItem] = useState({});
    const [loading, setLoading] = useState(false);
    const [key, setkey] = useState(0);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();
    const [modalVisible1, setModalVisible1] = useState(false)
    const [screen, setScreen] = useState('')
    const cartt = useSelector(state => state.cart.cart)
    const [cartItems, setCartItems] = useState(cartt.sheep);


    const [timeLeft, setTimeLeft] = useState(props.route.params.timeLeft); // 10 minutes in seconds
    const timerRef = useRef(null);

    // Start Timer Function
    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };
    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart())
    };
    // Stop Timer Function
    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };

    // Reset Timer to 10 minutes
    const resetTimer = () => {
        setTimeLeft(props.route.params.timeLeft);
    };

    // Start timer when screen is focused & Reset when leaving
    useFocusEffect(
        useCallback(() => {
            resetTimer(); // Reset every time you open the screen
            startTimer();

            // Cleanup when screen is unfocused (leaving screen)
            return () => {
                stopTimer();
                resetTimer(); // Reset when leaving the screen
            };
        }, [])
    );
    useEffect(() => {
        let screen = props.route.params.screen
        setScreen(screen)
        console.log(screen);
        const getAddresses = async () => {
            try {
                setLoading(true)
                let response = await dispatch(addressesActions.getAddresses());
                if (response.success == true) {
                    setAddresses(response.data)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getAddresses()
    }, []);



    const renderItem = (data, rowMap) => (
        <Pressable
            onPress={() => { rowMap[data.item.id].closeRow(); setAddress(data.item) }}
            style={styles.addressContainer}>
            <TouchableNativeFeedback
                onPress={() => rowMap[data.item.id].closeRow()}
                style={{ alignItems: 'center', justifyContent: 'center', marginStart: 10, }}
            >
                <Image source={require('../images/Silal/location.png')} style={{ height: '100%', width: 20, resizeMode: "contain", tintColor: Red }} />
            </TouchableNativeFeedback>
            <View
                // onPress={() => rowMap[rowData.item.key].closeRow()}
                style={{ alignItems: 'flex-start', justifyContent: 'center', marginStart: 10, }}
            >
                <Text numberOfLines={2} style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{data.item.address}</Text>
                <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{data.item.landmark} {'-'} {strings('lang.Building')} : {data.item.building} {'-'} {strings('lang.Floor')} : {data.item.floor} {'-'} {strings('lang.apartment')} : {data.item.flat}</Text>
            </View>
        </Pressable>
    );

    const renderHiddenItem = (data, rowMap) => (
        // <View style={{ height: screenHeight / 8, marginVertical: '1%', width: '100%', justifyContent: 'space-between', alignSelf: 'flex-start', flexDirection: 'row' }}>
        //     <Pressable
        //         // onPress={() => deleteRow(rowMap, data.item.id)}
        //         onPress={() => { setModalVisible1(!modalVisible1) }}
        //         style={{ backgroundColor: Red1, height: '100%', alignSelf: 'flex-start', alignItems: 'center', justifyContent: 'center', width: screenWidth / 8 }}>
        //         <Image source={require('../images/modrek/box.png')} style={{ height: '25%', resizeMode: "contain", tintColor: White, width: '30%' }} />
        //     </Pressable>

        // </View>
        <>
            <View style={{ height: screenHeight / 10, width: '100%', justifyContent: 'space-between', alignSelf: 'flex-start', flexDirection: 'row' }}>
                <Pressable
                    onPress={() => { setModalVisible1(!modalVisible1) }}
                    style={{ backgroundColor: Red1, height: '100%', alignSelf: 'flex-start', alignItems: 'flex-start', justifyContent: 'center', width: screenWidth / 2 }}>
                    <Image source={require('../images/modrek/box.png')} style={{ marginStart: '12%', height: '25%', resizeMode: "contain", tintColor: White, width: '30%' }} />
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 25, color: White, marginStart: '15%' }}>Delete</Text>
                </Pressable>
                <Pressable
                    onPress={() => { closeRow(rowMap, data.item.id) }}
                    style={{ backgroundColor: Red, width: screenWidth / 2, height: '100%', alignSelf: 'flex-end', alignItems: 'flex-end', justifyContent: 'center' }}>
                    <Image source={require('../images/Silal/edit.png')} style={{ marginEnd: '12%', height: '25%', resizeMode: "contain", tintColor: White, width: '20%' }} />
                    <Text style={{ marginEnd: '15%', fontFamily: appFontBold, fontSize: screenWidth / 25, color: White }}>Edit</Text>
                </Pressable>

            </View>
            <Modal
                transparent={true}
                animationType="fade"
                visible={modalVisible1}
            >

                <Button transparent onPress={() => setModalVisible1(!modalVisible1)} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                </Button>

                <View style={styles.modal}>
                    <View style={{ marginVertical: '1%', width: '100%', alignItems: 'center', justifyContent: 'center' }}>
                        {/* <Image source={require('../images/nafud/alert.png')} style={{ height: '40%', resizeMode: "contain", width: '50%' }} /> */}
                        <Text style={styles.modaltext}>{strings('lang.Areyousuretodeletetheproduct')}</Text>

                        <View style={{ flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                            <Button style={styles.modalbuttonContainer} onPress={() => { setModalVisible1(!modalVisible1) }}>
                                <Text style={styles.modalbuttonText}>{strings('lang.No')}</Text>
                            </Button>
                            <Button style={styles.modalbuttonContainer2} onPress={() => { setModalVisible1(!modalVisible1); deleteRow(rowMap, data.item.id) }}>
                                <Text style={styles.modalbuttonText2}>{strings('lang.Yes')}</Text>
                            </Button>
                        </View>

                    </View>

                </View>
            </Modal>
        </>
    );

    const closeRow = (rowMap, rowId) => {
        if (rowMap[rowId]) {
            rowMap[rowId].closeRow();
        }
    };

    const deleteRow = async (rowMap, rowId) => {
        try {
            setLoading(true)
            let response = await dispatch(addressesActions.deleteAddress(rowId));
            if (response.success == true) {
                closeRow(rowMap, rowId);
                const newData = [...Addresses];
                const prevIndex = Addresses.findIndex(item => item.id === rowId);
                newData.splice(prevIndex, 1);
                setAddresses(newData);
                setLoading(false);
                AsyncStorage.removeItem("address");
                let response = await dispatch(addressesActions.getAddresses());
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }

    };
    const onRowDidOpen = rowId => {
        console.log('This row opened', rowId);
    };

    const setAddress = async (address) => {
        AsyncStorage.setItem("address", JSON.stringify(address));
        props.navigation.push(screen, { order: props.route.params.order, shared: props.route.params.shared, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft })

        try {
            let response = await dispatch(cartActions.selectAddress(address.id));
            if (response.success == true) {
            }
            else {
                if (response.message) {
                }
            }
        }
        catch (err) {
            console.log('err', err)
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header title={strings('lang.Adresses')}
                    backPress={() => { props.navigation.push(props.route.params.screen, { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }}
                />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 18,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={styles.section}>
                        <View style={styles.textContanier}>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>
                            <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, marginTop: screenHeight / 150 }} >
                                <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                                    <View style={{ width: '100%', height: screenHeight / 8.7, backgroundColor: WhiteGery, }} />
                                </SkeletonPlaceholder>
                            </View>

                        </View>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={''} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, backgroundColor: White }}>
                <Header title={strings('lang.Adresses')}
                    backPress={() => { props.navigation.push(props.route.params.screen, { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }}
                />



                <Button onPress={() => { props.navigation.push('Map', { screen: props.route.params.screen, shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft, }) }} style={{ elevation: 4, alignItems: "center", justifyContent: "center", width: '90%', height: 40, backgroundColor: WhiteGery, borderRadius: 15, alignSelf: "center", flexDirection: 'row', marginVertical: 20 }}>
                    <Image source={require('../images/Silal/location.png')} style={{ marginEnd: 8, height: '80%', resizeMode: "contain", tintColor: Red }} />
                    <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black }}>{strings('lang.Addatitle')}</Text>
                </Button>

                {/* <ScrollView showsVerticalScrollIndicator={false} style={{ width: '100%', alignSelf: "center", borderTopColor: MediumGrey, borderTopWidth: 1, marginTop: screenHeight / 20 }}> */}

                {Addresses.length == 0
                    ?
                    <View style={{ width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginBottom: '4%', marginTop: '4%' }}>
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 20, }}>{strings('lang.No_Results')}</Text>
                    </View>
                    :
                    <></>
                }

                <SwipeListView
                    data={Addresses}
                    // useSectionList={true}
                    // sections={sections}
                    // stopLeftSwipe={true}
                    keyExtractor={(item, index) => item.id}
                    renderItem={renderItem}
                    renderHiddenItem={renderHiddenItem}

                    // onRowOpen={(rowMap, rowId) => {
                    //     setTimeout(() => {
                    //         rowMap[Addresses.id].closeRow()
                    //     }, 4000)
                    // }}
                    previewRowKey={Addresses[0] ? Addresses[0].id : ''}
                    previewOpenValue={I18nManager.isRTL ? -100 : 100}
                    previewOpenDelay={0}
                    onRowDidOpen={onRowDidOpen}
                    // previewRepeat={true}
                    // previewRepeatDelay={2000}
                    leftOpenValue={110}
                    rightOpenValue={- 100}
                    closeOnScroll
                />


                {/* <FlatList
                data={Addresses}
                showsVerticalScrollIndicator={false}
                renderItem={({ item, key }) =>
                <View style={{backgroundColor:WhiteGery, flexDirection: 'row', width: '95%', height: 60, borderColor: MediumGrey, borderWidth: 1,  justifyContent: "center",borderRadius:15, alignItems: "center",alignSelf:'center',marginVertical:'2%' }}>
                <TouchableOpacity
                    // onPress={async () => { await this.setState({ addressId: item.id, item: item }); this.setModalVisibleCart() }}
                    style={{ width: screenWidth / 10, height: '90%', alignItems: "center",justifyContent:'center' }}
                >
                    <Image source={require('../images/Silal/location.png')} style={{ height: '50%', resizeMode: "contain", tintColor: Red }} />
                </TouchableOpacity>
                <TouchableOpacity

                    // onPress={async () => { await this.setState({ addressId: item.id, item: item }); this.setModalVisibleCart() }}
                    style={{ width: screenWidth / 1.72, height: '90%',justifyContent:'center' }}
                >
                    <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 28, color: Black, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{'جدة'}</Text>
                    <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>doha</Text>
                </TouchableOpacity>
                <View style={{ width: screenWidth / 10, height: '80%', alignItems: "center", justifyContent:'center'}}>
                    <TouchableOpacity
                        onPress={() => { props.navigation.navigate('Map', { address: item, screen: 'Addresses' }) }}
                        style={{ width: screenWidth / 12, height: screenWidth / 12, borderRadius: screenWidth / 24, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center" }}
                    >
                        <Image source={require('../images/Silal/edit.png')} style={{ height: '60%', resizeMode: "contain", tintColor: Red }} />
                    </TouchableOpacity>
                </View>
                <View style={{ width: screenWidth / 10, height: '80%', alignItems: "center",justifyContent:'center' }}>
                    <TouchableOpacity style={{ width: screenWidth / 12, height: screenWidth / 12, borderRadius: screenWidth / 24, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center" }}>
                        <Image source={require('../images/Silal/x.png')} style={{ height: '50%', resizeMode: "contain" }} />
                    </TouchableOpacity>
                </View>
            </View>
                }
                keyExtractor={item => item.id}
                style={{ alignSelf: "center", overflow: "hidden",width:'100%', backgroundColor: White, paddingVertical: 5,borderTopColor: MediumGrey, borderTopWidth: 1, marginTop: screenHeight / 20  }}
            // onEndReached={() => this.LoadMore()}
            // onEndReachedThreshold={0.1}
            // numColumns={2}

            /> */}

                <View style={{ height: screenHeight / 10 }}></View>

                <Modal
                    transparent={true}
                    animationType="fade"
                    visible={ModalVisible}
                >

                    <Button transparent onPress={() => setModalVisible()} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                    </Button>

                    <View style={styles.modal}>
                        <Text style={styles.modaltext}>{strings('lang.Do_you_want_delete')}</Text>

                        <View style={{ flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                            <Button style={styles.modalbuttonContainer} onPress={() => { setModalVisible(); }}>
                                <Text style={styles.modalbuttonText}>{strings('lang.No')}</Text>
                            </Button>
                            <Button style={styles.modalbuttonContainer2} onPress={() => { setModalVisible() }}>
                                <Text style={styles.modalbuttonText2}>{strings('lang.Yes')}</Text>
                            </Button>
                        </View>

                    </View>
                </Modal>

                <Modal
                    transparent={true}
                    animationType="fade"
                    visible={ModalVisibleCart}
                >

                    <Button transparent onPress={() => setModalVisibleCart({})} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
                    </Button>

                    <View style={styles.modal2}>
                        <Text style={styles.modaltext}>{strings('lang.Incaseofchangingtheaddress')}</Text>
                        <Text style={styles.modaltext}>{strings('lang.Doyouwanttocontinue')}</Text>

                        <View style={{ flexDirection: 'row-reverse', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', width: '85%', }}>
                            <Button style={styles.modalbuttonContainer} onPress={() => { setModalVisibleCart({}); }}>
                                <Text style={styles.modalbuttonText}>{strings('lang.No')}</Text>
                            </Button>
                            <Button style={styles.modalbuttonContainer2} onPress={() => { setModalVisibleCart({}) }}>
                                <Text style={styles.modalbuttonText2}>{strings('lang.Yes')}</Text>
                            </Button>
                        </View>

                    </View>
                </Modal>


                <MyFooter current={'Adresses'} navigation={props.navigation} />
            </View>
        )
    }
}
export default Addresss;
const styles = StyleSheet.create({
    section: { width: '100%', alignSelf: 'center' },
    textContanier: {
        width: '100%',
        marginVertical: '2%',
        alignItems: 'flex-start',
    },

    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modal2: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 3.5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 22,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    modalbuttonContainer2: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    textBlackCenter: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 29, },
    textBlackCenter2: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 27, },
    addressContainer: { backgroundColor: WhiteGery, flexDirection: 'row', width: screenWidth, height: screenHeight / 10, justifyContent: "flex-start", alignItems: "center", borderBottomColor: MediumGrey, borderBottomWidth: 1, paddingHorizontal: '3%' },
});
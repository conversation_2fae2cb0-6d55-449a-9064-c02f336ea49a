import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, Pressable, Keyboard } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor1, Black, appColor2, LightGreen, Red, WhiteRed, MediumBlue, Red1 } from '../components/Styles';
import { Button, Input } from 'native-base';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import CodeInput from 'react-native-confirmation-code-input';
import { useDispatch, useSelector } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as ordersActions from '../../Store/Actions/orders';
import * as cartActions from '../../Store/Actions/cart';
import Toaster from '../components/Toaster';
import LoadingMore from '../components/LoadingMore';
import * as addressesActions from '../../Store/Actions/addresses';
import RNRestart from 'react-native-restart';
import { useFocusEffect } from '@react-navigation/native';
import BackgroundTimer from 'react-native-background-timer';

const CompleteOrder2 = props => {
    const [shared, setShared] = useState(props.route.params.shared);
    const [order, setOrder] = useState(props.route.params.order)

    const [methodId, setMethodId] = useState(0);
    const [phone, setPhone] = useState('');
    const refRbSheet = useRef();
    const refRbSheet1 = useRef();
    const refCod = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();

    const minss = useSelector(state => state.cart.mins)
    const secss = useSelector(state => state.cart.secs)
    const [mins, setMins] = useState(minss)
    const [secs, setSecs] = useState(secss)

    const paymentt = useSelector(state => state.orders.payment)
    const [payment, setPayment] = useState(paymentt);


    const settingss = useSelector(state => state.settings.settings)
    const [Addresses, setAddresses] = useState([]);


    const cartt = useSelector(state => state.cart.cart)
    const [cart, setCart] = useState({});
    const [cartItems, setCartItems] = useState(cartt.sheep);
    const [discount, setDiscount] = useState('');
    useEffect(() => {
        const getCart = async () => {
            try {
                setLoading(true)

                let response = await dispatch(cartActions.getCart());
                if (response.success == true) {
                    setCart(response.data)
                    // setCartItems(response.data && response.data.sheep)
                    setDiscount(response.data.coupon ? response.data.coupon.code : '')
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getCart();
        console.log('paymentt', paymentt);
        console.log('props.route.params.cartt', cartt);
        console.log('props.route.params.order', props.route.params.order);
        const getAddresses = async () => {
            try {
                setLoading(true)
                let response = await dispatch(addressesActions.getAddresses());
                if (response.success == true) {
                    setAddresses(response.data)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };

        getAddresses()
    }, []);

    const intervalRef = useRef(null); // Store interval ID in a ref


    const [timeLeft, setTimeLeft] = useState(props.route.params.timeLeft); // 10 minutes in seconds
    const timerRef = useRef(null);

    // Start Timer Function
    const startTimer = () => {
        if (timerRef.current) return;

        timerRef.current = BackgroundTimer.setInterval(() => {
            setTimeLeft((prev) => {
                if (prev <= 1) {
                    BackgroundTimer.clearInterval(timerRef.current);
                    timerRef.current = null;
                    handleTimerEnd();
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
                    return 0;
                }
                return prev - 1;
            });

            if (cartItems.length == 0) {
                dispatch(cartActions.clearCart());
                BackgroundTimer.clearInterval(timerRef.current);
                timerRef.current = null;
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
            }
        }, 1000);
    };
    const handleTimerEnd = () => {
        Toaster(
            'top',
            'danger',
            Red1,
            strings('lang.message13'),
            White,
            4500,
            screenHeight / 15,
        );
        dispatch(cartActions.clearCart())
    };
    // Stop Timer Function
    const stopTimer = () => {
        if (timerRef.current) {
            BackgroundTimer.clearInterval(timerRef.current);
            timerRef.current = null;
        }
    };

    // Reset Timer to 10 minutes
    const resetTimer = () => {
        setTimeLeft(props.route.params.timeLeft);
    };

    // Start timer when screen is focused & Reset when leaving
    useFocusEffect(
        useCallback(() => {
            resetTimer(); // Reset every time you open the screen
            startTimer();

            // Cleanup when screen is unfocused (leaving screen)
            return () => {
                stopTimer();
                resetTimer(); // Reset when leaving the screen
            };
        }, [])
    );

    // Format time as mm:ss
    const formatTime = (seconds) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    // useEffect(() => {
    //     intervalRef.current = setInterval(async () => {
    //         if (cartItems.length == 0) {
    //             setMins(10)
    //             setSecs(0)
    //             // Toaster(
    //             //     'top',
    //             //     'danger',
    //             //     Red1,
    //             //     strings('lang.message13'),
    //             //     White,
    //             //     4500,
    //             //     screenHeight / 15,
    //             // );
    //             clearInterval(intervalRef.current);
    //             // dispatch(cartActions.timer(10, 0));
    //             dispatch(cartActions.timer(10, 0));
    //             dispatch(cartActions.clearCart())
    //             props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
    //             // RNRestart.Restart();
    //         } else {
    //             if (secs <= 0) {
    //                 if (mins <= 0) {
    //                     clearInterval(intervalRef.current);
    //                     Toaster(
    //                         'top',
    //                         'danger',
    //                         Red1,
    //                         strings('lang.message13'),
    //                         White,
    //                         4500,
    //                         screenHeight / 15,
    //                     );
    //                     clearInterval(intervalRef.current);
    //                     dispatch(cartActions.timer(10, 0));
    //                     dispatch(cartActions.clearCart())
    //                     props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' });
    //                 }
    //                 else {
    //                     setMins(m => m - 1)
    //                     setSecs(59)

    //                     dispatch(cartActions.timer(mins - 1, 59));
    //                 }
    //             }
    //             else {
    //                 setSecs(s => s - 1)
    //                 dispatch(cartActions.timer(mins, secs - 1));
    //             }
    //         }
    //     }, 1000)
    //     return () => clearInterval(intervalRef.current);
    // }, [secs, mins]);

    const performTaskAndStopTimer = () => {
        if (intervalRef.current) {
            clearInterval(intervalRef.current); // Stop the interval
            intervalRef.current = null; // Reset the interval reference
            console.log('Interval stopped automatically.');
        }
        dispatch(cartActions.timer(10, 0));
    };

    const nav = async () => {
        if (!methodId) {
            Toaster(
                'top',
                'danger',
                Red1,
                strings('lang.message12'),
                White,
                1500,
                screenHeight / 15,
            );
            // }else if(){

        }
        else {
            setLoadingMore(true)
            try {
                let response = await dispatch(cartActions.selectPaymentMethod(methodId, shared, shared ? order.id : null));
                if (response.success == true) {

                    setLoadingMore(false)
                }
                else {
                    if (response.message) {
                    }
                    setLoadingMore(false)
                }
            }
            catch (err) {
                setLoadingMore(false)
                console.log('err', err)
            }
            dispatch(cartActions.timer(10, 0));
            performTaskAndStopTimer();
            props.navigation.push("PaymentWebView", {
                shared: props.route.params.shared, orderId: shared ? order.id : cartt.id, order: order, sheepTypeId: props.route.params.sheepTypeId,
                timeLeft: timeLeft, methodId: methodId
            })


        }
    }

    const addCoupon = async () => {
        if (!discount) {
            Toaster(
                'top',
                'danger',
                Red1,
                strings('lang.message14'),
                White,
                1500,
                screenHeight / 15,
            );
        }
        else {
            setLoadingMore(true)
            try {
                let response = await dispatch(cartActions.addCoupon(discount));
                if (response.success == true) {
                    setCart(response.data)
                    setDiscount(response.data.coupon.code)
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        strings('lang.message15'),
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
            }
            catch (err) {
                console.log('err', err)
            }
            setLoadingMore(false)

        }
    }

    const removeCoupon = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(cartActions.removeCoupon());
            if (response.success == true) {
                setCart(response.data)
                setDiscount('')
                Toaster(
                    'top',
                    'danger',
                    Red,
                    strings('lang.message15'),
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
            else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red1,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
        }
        catch (err) {
            console.log('err', err)
        }
        setLoadingMore(false)
    }

    const [keyboardIsOpen, setKeyboardIsOpen] = React.useState(false);
    Keyboard.addListener("keyboardDidShow", () => {
        setKeyboardIsOpen(true);
    });
    Keyboard.addListener("keyboardDidHide", () => {
        setKeyboardIsOpen(false);
    });


    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header title={strings('lang.Completetheapplication')} backPress={() => {
                    props.navigation.push("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId })
                }} /> */}


                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ marginTop: screenHeight / 100, flexDirection: 'row', width: '100%', paddingHorizontal: '1%', justifyContent: 'space-between' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    backgroundColor: WhiteGery,
                                    borderRadius: 5,
                                    marginStart: 5
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: screenHeight / 8,
                                    height: screenHeight / 22,
                                    borderRadius: 5,
                                    backgroundColor: WhiteGery,
                                }}
                            />
                        </SkeletonPlaceholder>

                    </View>

                    < View style={{ marginTop: screenHeight / 50, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 5,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 80, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '40%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 15,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 80, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '40%',
                                    height: screenHeight / 25,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'flex-start'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 16,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 16,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 16,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, marginBottom: 10, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '95%',
                                    height: screenHeight / 16,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center',
                                    borderRadius: 20
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                {/* <Header title={strings('lang.Completetheapplication')} backPress={() => { props.navigation.push("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }} /> */}
                {loadingMore ? <LoadingMore /> : <></>}

                <KeyboardAwareScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                >




                    <View style={{
                        flexDirection: 'row', width: "100%", alignSelf: "center", justifyContent: 'space-between', alignItems: "flex-end", height: screenHeight / 9,
                        backgroundColor: Red,
                    }}>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Cuttingandprocessing')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.Titleanddate')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "25%", height: "70%", alignItems: 'flex-start', justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: White }}>{strings('lang.payingoff')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: White, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                        <View style={{ width: "25%", height: "70%", alignItems: "flex-start", justifyContent: "center" }}>
                            <Text numberOfLines={1} style={{ fontSize: screenWidth / 35, fontFamily: appFont, color: DarkGrey }}>{strings('lang.Orderdetails')}</Text>
                            <View style={{ height: 6, width: '95%', backgroundColor: DarkGrey, opacity: .5, marginTop: 5, borderRadius: 4 }}></View>
                        </View>
                    </View>

                    <View style={{ flexDirection: 'row', borderEndWidth: .5, borderRadius: 20, borderColor: WhiteGery, backgroundColor: WhiteGery, width: screenWidth / 1.1, height: screenHeight / 15, alignSelf: 'center', alignItems: 'center', justifyContent: 'center', marginVertical: 20 }}>
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 35, }}>{strings('lang.messageFinshTime')} </Text>
                        <Image source={require('../images/newIcon/watch.png')} style={{ resizeMode: 'contain', width: '10%', height: '30%', tintColor: Red1 }} />
                        {/* <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, marginStart: '1%' }}>{mins}:{secs < 10 && 0}{secs}</Text> */}
                        <Text style={{ color: Red1, fontFamily: appFontBold, fontSize: screenWidth / 30, marginStart: '1%' }}>{formatTime(timeLeft)}</Text>
                    </View>

                    {shared
                        ?
                        <>
                            <Text style={{ marginVertical: 10, fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start', marginStart: '2.5%' }}>{strings('lang.Yourpriceowed')}</Text>
                            <View style={{ width: '95%', alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '2%' }}>
                                <View style={{ width: '75%', }}>
                                    <Text style={styles.textGrey1}>{strings('lang.Thepriceisinclusiveofqtm')}</Text>
                                </View>
                                <View style={{ width: '25%', alignItems: "flex-end" }}>
                                    <Text style={styles.textGreen1}> {order.payment_type == "Full"
                                        ?
                                        '0'
                                        :
                                        order.shared_value}  {strings('lang.SAR')}</Text>
                                </View>
                            </View>
                        </>
                        :
                        <View style={{ width: '100%', alignSelf: "center", flexDirection: "row", marginVertical: 10, backgroundColor: WhiteGery, padding: '5%', }}>
                            <View style={{ width: '75%', }}>
                                <Text style={styles.textGrey1}>{strings('lang.Totalproductsinclusiveofqqt')}</Text>

                                <Text style={styles.textGrey1}>{strings('lang.Delivery')}</Text>
                                <Text style={styles.textGrey1}>{strings(cart.delivery_type_fees == 0 ? 'lang.Standarddelivery' : 'lang.Expediteddelivery')}</Text>
                                <Text numberOfLines={1} style={[styles.textAddress, {
                                    color: Red1, fontFamily: appFont, fontSize: screenWidth / 40
                                }]}>{strings('lang.Thedeliverypriceforonecarcassis29riyals')}</Text>
                                {cart.coupon
                                    &&
                                    <Text style={styles.textGrey1}>{strings('lang.Discount')}</Text>
                                }
                                <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: 5 }}></View>
                                <Text style={styles.textGrey1}>{strings('lang.Finaltotal')}</Text>
                                {cart.payment_type == "Shared" &&
                                    <Text style={styles.textGrey1}>{strings('lang.Yourprice')}</Text>
                                }
                            </View>


                            <View style={{ width: '25%', alignItems: "flex-end" }}>
                                <Text style={styles.textBlack0}>{cart.sub_total} {strings('lang.SAR')}</Text>
                                <Text style={styles.textBlack0}> {cart.delivery_fees} {strings('lang.SAR')}</Text>
                                <Text style={styles.textBlack0}> {cart.delivery_type_fees == 0 ? ' ' : cart.delivery_type_fees} {cart.delivery_type_fees == 0 ? '' : strings('lang.SAR')}</Text>
                                <Text style={[styles.textBlack0, {
                                    fontSize: screenWidth / 40, fontFamily: appFont, fontSize: screenWidth / 40
                                }]}> {' '} </Text>

                                {cart.coupon
                                    &&
                                    <Text style={styles.textBlack0}>- {cart.discount} {strings('lang.SAR')}</Text>
                                }
                                <View style={{ height: 1, width: '100%', backgroundColor: MediumGrey, marginVertical: 5 }}></View>
                                <Text style={styles.textRed1}> {cart.total} {strings('lang.SAR')}</Text>
                                {cart.payment_type == "Shared" &&
                                    <Text style={styles.textRed1}> {cart.shared_value} {strings('lang.SAR')}</Text>
                                }
                            </View>
                        </View>
                    }



                    {shared
                        ?
                        <></>
                        :
                        <>
                            <Text style={styles.title}>{strings('lang.Enterthediscountcode')}</Text>
                            <View style={{ width: '90%', alignSelf: "center", height: screenHeight / 15, paddingStart: 5, flexDirection: "row", justifyContent: "space-between", borderWidth: 1, borderEndWidth: 0, borderColor: MediumGrey, borderRadius: 25, overflow: 'hidden', marginBottom: screenHeight / 50 }}>
                                <View style={{ width: '65%', height: '100%', justifyContent: "center", }}>
                                    <Input editable={cart.coupon ? false : true} value={discount} onChangeText={(text) => setDiscount(text)}
                                        placeholderTextColor={'#707070'} placeholder={strings('lang.Enterthediscountcode')}
                                        style={{ textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFont, fontSize: screenWidth / 28, alignSelf: 'center' }}
                                    />
                                </View>
                                <Button onPress={() => {
                                    if (cart.coupon) {
                                        removeCoupon()
                                    }
                                    else {
                                        addCoupon()
                                    }
                                }} style={{ width: '35%', height: '100%', backgroundColor: cart.coupon ? Red1 : Red, alignItems: "center", justifyContent: "center", alignSelf: "center" }}>
                                    <Text style={styles.textWhite1}>{cart.coupon ? strings('lang.Delete') : strings('lang.Enter')}</Text>
                                </Button>
                            </View>
                        </>
                    }

                    {/* </>
                    } */}

                    {/* {order == '1' &&
                        <>
                            <Text style={styles.title}>{strings('lang.Yourpriceowed')}</Text>
                            <View style={{ width: '95%', alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '2%' }}>
                                <View style={{ width: '75%', }}>
                                    <Text style={styles.textGrey1}>{strings('lang.Thepriceisinclusiveofqtm')}</Text>
                                </View>
                                <View style={{ width: '25%', alignItems: "flex-end" }}>
                                    <Text style={styles.textGreen1}> {1600}  {strings('lang.SAR')}</Text>
                                </View>
                            </View>

                            <Text style={styles.title}>{strings('lang.Pricepayabletothepartner')}</Text>
                            <View style={{ width: '95%', alignSelf: "center", flexDirection: "row", marginBottom: 10, backgroundColor: WhiteGery, padding: '2%' }}>
                                <View style={{ width: '75%', flexDirection: 'row' }}>
                                    <Text style={styles.textGrey1}>{strings('lang.Thepriceisinclusiveofqtm')}</Text>
                                    <Text style={styles.textGreen1}>(تم الدفع)</Text>
                                </View>
                                <View style={{ width: '25%', alignItems: "flex-end" }}>
                                    <Text style={styles.textRed1}> {1600}  {strings('lang.SAR')}</Text>
                                </View>
                            </View>
                        </>
                    } */}


                    {shared
                        ?
                        order.payment_type == "Full"
                            ?
                            <></>
                            :
                            <>
                                <Text style={styles.title}>{strings('lang.Chooseyourpaymentmethod')}</Text>
                                {payment.map((item, index) => {
                                    return (
                                        <Pressable onPress={() => { setMethodId(item.id) }} style={item.id == methodId ? styles.activeSubContainer : styles.inactiveSubContainer}>
                                            <View style={styles.iconContainer}>
                                                <Image style={styles.icon} source={require('../images/modrek/cash.png')} />
                                            </View>
                                            <View style={styles.detailsContainer}>
                                                <Text numberOfLines={1} style={styles.textAddress}>{item.name}</Text>
                                                {item.id == 4 ?
                                                    <Text numberOfLines={1} style={styles.textAddress}>{settingss.cod} {strings('lang.SAR')}</Text>
                                                    : <></>
                                                }
                                            </View>
                                        </Pressable>
                                    )
                                })}

                            </>
                        :
                        <>
                            <Text style={styles.title}>{strings('lang.Chooseyourpaymentmethod')}</Text>
                            {payment.map((item, index) => {
                                return (
                                    <Pressable onPress={() => { setMethodId(item.id) }} style={item.id == methodId ? styles.activeSubContainer : styles.inactiveSubContainer}>
                                        <View style={styles.iconContainer}>
                                            {item.id == 1 ?
                                                <Image style={styles.icon} source={require('../images/newIcon/madalogo.png')} />
                                                :
                                                item.id == 2 ?
                                                    <>

                                                        <Image style={[styles.icon, { width: '30%' }]} source={require('../images/newIcon/MasterCardlogo.png')} />
                                                        <Image style={[styles.icon, { width: '30%' }]} source={require('../images/newIcon/Visalogo.png')} />
                                                    </>
                                                    :
                                                    item.id == 3 ?
                                                        <Image style={styles.icon} source={require('../images/newIcon/Applepaylogo.png')} />
                                                        :
                                                        <Image style={styles.icon} source={require('../images/newIcon/tamaralogo.png')} />
                                            }
                                        </View>
                                        <View style={styles.detailsContainer}>
                                            <Text numberOfLines={1} style={styles.textAddress}>{item.name}</Text>
                                            {/* {item.id == 3 ?
                                                <Text numberOfLines={1} style={styles.textAddress}>{settingss.cod} {strings('lang.SAR')}</Text>
                                                : <></>
                                            } */}
                                        </View>
                                    </Pressable>
                                )
                            })}

                        </>
                    }



                    <View style={{ flexDirection: I18nManager.isRTL ? 'row' : 'row-reverse', alignItems: 'flex-end', justifyContent: "space-between", width: screenWidth / 1.1, alignSelf: 'center', height: screenHeight / 19, marginVertical: '10%' }}>
                        <Button onPress={() => { props.navigation.push("CompleteOrder1", { shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: timeLeft }) }} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: WhiteGery, alignItems: "center", justifyContent: "center", borderRadius: screenWidth / 10, }}>
                            <Image source={require('../images/modrek/arrowRight.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: Black, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} />
                            <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: Black, marginHorizontal: '1%' }}>{strings('lang.back')}</Text>
                        </Button>
                        <Button onPress={() => {
                            if (shared) {
                                if (order.payment_type == "Full") {
                                    props.navigation.navigate("CompleteOrder3", { screen: 'CompleteOrder2', shared: props.route.params.shared, order: props.route.params.order })

                                } else {
                                    nav()
                                }
                            } else {
                                nav()
                                // props.navigation.navigate("CompleteOrder3", { screen: 'CompleteOrder2', shared: props.route.params.shared, order: props.route.params.order })

                            }
                        }} style={{ width: '45%', alignSelf: "center", height: 40, backgroundColor: Red, alignItems: "center", flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', justifyContent: "center", borderRadius: screenWidth / 10 }}>
                            <Image source={require('../images/modrek/arrowLeft.png')} style={{ transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }], tintColor: White, resizeMode: 'contain', width: '15%', height: '55%', alignItems: 'center' }} />
                            <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: White, marginHorizontal: '1%' }}>{strings('lang.next')}</Text>
                        </Button>
                    </View>

                </KeyboardAwareScrollView>

                {!keyboardIsOpen &&
                    <>
                        <View style={{ height: screenHeight / 10, }}></View>
                        {/* <MyFooter navigation={props.navigation} /> */}
                    </>
                }
                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: White
                        }
                    }}
                    closeOnDragDown={true}
                >
                    <View style={{ width: '90%', height: screenHeight / 12, }} >
                        <Image source={require('../images/Silal/Group4.png')} style={{ tintColor: Red }} />
                    </View>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: DarkGrey, alignSelf: 'flex-start', marginStart: '5%', marginVertical: screenHeight / 50 }}>{strings('lang.Takeadvantageoftheexcellencediscount')}</Text>
                    <View style={{ height: screenHeight / 10, width: '90%', marginVertical: screenHeight / 50, justifyContent: 'space-between' }}>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 26, color: DarkGrey, alignSelf: 'flex-start', }}>{strings('lang.Pleaseenterthemobilenumberofthecustomer')}</Text>
                        <View style={{ height: 45, }}>
                            <Input value={phone} onChangeText={(text) => setPhone(text)}
                                placeholderTextColor={'#707070'} placeholder={strings('lang.Pleaseenterthemobilenumberofthecustomer')}
                                style={{ textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFont, fontSize: screenWidth / 28, alignSelf: 'center', borderWidth: 1, borderColor: MediumGrey, borderRadius: 15, }}
                            />
                        </View>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: "center", justifyContent: "space-between", width: '90%', alignSelf: "center", marginTop: screenHeight / 20, }}>
                        <Button onPress={() => refRbSheet1.current.open()} style={{ width: '47%', alignSelf: "center", height: 40, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 15 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Send')}</Text>
                        </Button>
                        <Button onPress={() => refRbSheet.current.close()} style={{ width: '47%', alignSelf: "center", height: 40, backgroundColor: White, borderColor: Red, borderWidth: 1, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 15 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Red, }}>{strings('lang.back')}</Text>
                        </Button>
                    </View>
                </RBSheet >

                <RBSheet
                    ref={refRbSheet1}
                    height={screenHeight / 1.6}
                    openDuration={280}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderTopRightRadius: 30,
                            borderTopLeftRadius: 30,
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: White
                        }
                    }}
                    closeOnDragDown={true}
                >
                    <View style={{ width: '90%', height: screenHeight / 12, }} >
                        <Image source={require('../images/Silal/Group4.png')} />
                    </View>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: DarkGrey, alignSelf: 'flex-start', marginStart: '5%', marginVertical: screenHeight / 50 }}>{strings('lang.Takeadvantageoftheexcellencediscount')}</Text>
                    <Text style={{ fontFamily: appFont, fontSize: screenWidth / 26, color: DarkGrey, alignSelf: 'flex-start', marginStart: '5%', marginVertical: screenHeight / 100 }}>{strings('lang.Pleaseenterthe4digitcodesenttoyou')}</Text>
                    <View style={{ height: screenHeight / 7, width: '90%', }}>
                        <CodeInput
                            ref={refCod}
                            activeColor={Black}
                            inactiveColor='#343a5e'
                            className={'border-box'}
                            autoFocus={true}
                            ignoreCase={true}
                            inputPosition='center'
                            onFulfill={(code) => { }}
                            space={screenWidth / 15}
                            codeLength={4}
                            size={screenWidth / 6}
                            textAlign="center"
                            containerStyle={{ flexDirection: I18nManager.isRTL == 'ar' ? 'row-reverse' : 'row', justifyContent: "center", }}
                            codeInputStyle={{ borderRadius: 5, borderWidth: 1, borderColor: MediumGrey }}
                            keyboardType={'number-pad'}
                        />
                    </View>

                    <View style={{ width: '90%', flexDirection: 'row', alignItems: "center", marginVertical: screenHeight / 50 }}>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 26, color: Black, }}>{strings('lang.Resendthecodewithin')}</Text>
                        <Button style={{ padding: 0, backgroundColor: White, elevation: 0 }}>
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 26, color: Red, marginStart: 5 }}>{strings('lang.Resendthecode')}</Text>
                        </Button>
                    </View>


                    <View style={{ flexDirection: 'row', alignItems: "center", justifyContent: "space-between", width: '90%', alignSelf: "center", marginVertical: screenHeight / 80, }}>
                        <Button onPress={() => { refRbSheet1.current.close(); refRbSheet.current.close(); }} style={{ width: '47%', alignSelf: "center", height: 45, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Confirm')}</Text>
                        </Button>
                        <Button onPress={() => { refRbSheet1.current.close(); }} style={{ width: '47%', alignSelf: "center", height: 45, backgroundColor: WhiteGery, borderColor: Red, borderWidth: 1, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: 20 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Red, }}>{strings('lang.back')}</Text>
                        </Button>
                    </View>
                </RBSheet >

            </View >
        )
    }
}

export default CompleteOrder2;
const styles = StyleSheet.create({
    textBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start' },
    textBlack0: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-end' },
    textBlack1: { fontFamily: appFont, color: Black, fontSize: screenWidth / 24, alignSelf: 'flex-start' },
    textAddress: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26 },
    textRed1: { fontFamily: appFontBold, color: Red, fontSize: screenWidth / 24 },
    textGrey: { fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 26, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey2: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },
    textGrey1: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 },
    textWhite1: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 24, textAlignVertical: 'center' },
    textGreen1: { fontFamily: appFontBold, color: 'green', fontSize: screenWidth / 24 },

    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    inactiveSubContainer: { width: screenWidth / 1.1, height: screenHeight / 16, alignSelf: "center", flexDirection: 'row', alignItems: "center", borderWidth: .5, borderColor: MediumGrey, borderRadius: 25, marginTop: 15 },
    activeSubContainer: { width: screenWidth / 1.1, height: screenHeight / 16, alignSelf: "center", flexDirection: 'row', alignItems: "center", borderWidth: 1, borderColor: Red, borderRadius: 25, backgroundColor: MediumBlue, marginTop: 15 },
    iconContainer: { width: '20%', height: '90%', alignItems: "center", justifyContent: "center", flexDirection: 'row' },
    detailsContainer: { width: '80%', height: '80%', justifyContent: "space-between", flexDirection: 'row', alignSelf: "center", alignItems: "center", paddingEnd: '5%', flexDirection: 'row' },
    icon: { width: '70%', height: '100%', resizeMode: "contain" },
});
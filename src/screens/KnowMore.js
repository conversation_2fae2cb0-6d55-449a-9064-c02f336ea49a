import React, { useRef, useState } from 'react';
import {
    View, Text, Image, ImageBackground,
    StyleSheet, I18nManager, Pressable, RefreshControl,
} from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor2, appColor1, Black, Red, Red1, WhiteBlue, DarkBlue, MediumBlue } from '../components/Styles';
import { Button } from 'native-base';
import { strings } from './i18n';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import LinearGradient from 'react-native-linear-gradient';
import Carousel, { Pagination, ParallaxImage } from 'react-native-snap-carousel';
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import RBSheet from 'react-native-raw-bottom-sheet';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as settingAction from '../../Store/Actions/settings';
import { ActivityIndicator } from 'react-native';

const KnowMore = props => {

    const [entries, setEntries] = useState(['https://hatrabbits.com/wp-content/uploads/2017/01/random.jpg', 'https://helpx.adobe.com/content/dam/help/en/photoshop/using/convert-color-image-black-white/jcr_content/main-pars/before_and_after/image-before/Landscape-Color.jpg', 'https://www.pixsy.com/wp-content/uploads/2021/04/ben-sweet-2LowviVHZ-E-unsplash-1.jpeg']);
    const [DATA, setDATA] = useState({});
    const [farm, setFarm] = useState({});
    const [cart, setCart] = useState({});
    const [buttonId, setButtonId] = useState('0');
    const [data, setData] = useState([]);
    const [selectedSheeps, setSelectedSheeps] = useState([]);
    const [selectedSheepsIds, setSelectedSheepsIds] = useState([]);
    const [subTotal, setSubTotal] = useState(0);
    const [time, setTime] = useState({});
    const [seconds, setSeconds] = useState(60);
    const [disable, setDisable] = useState(false);
    const refRbSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [loadingMoreData, setLoadingMoreData] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [lastPageNumber, setLastPageNumber] = useState(1);
    const [Reload, setReload] = useState(false);
    const dispatch = useDispatch();


    // console.log('nativ2', NativeModules.getConstants());
    useEffect(() => {

        const getArticles = async () => {
            try {
                setLoading(true)
                let response = await dispatch(settingAction.getArticles(1));
                if (response.success == true) {
                    setData(response.data.items)
                    setLastPageNumber(response.data.last_page)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getArticles();
    }, [Reload]);


    const onRefresh = async () => {
        try {
            setLoading(true)
            let response = await dispatch(farmesActions.getArticles(1));
            if (response.success == true) {
                setData(response.data.items)
                setLastPageNumber(response.data.last_page)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    }

    const LoadMore = async () => {
        console.log('pageNumber', pageNumber);
        console.log('lastPageNumber', lastPageNumber);
        if (pageNumber < lastPageNumber) {
            setLoadingMoreData(true)
            try {
                let response = await dispatch(farmesActions.getArticles(pageNumber + 1,));
                if (response.success == true) {
                    setData([...data, ...response.data.items]);
                    setPageNumber(pageNumber + 1);
                    setLoadingMoreData(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMoreData(false);
                }
            } catch (err) {
                setLoadingMoreData(false);
            }

        }
        else {
        }
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.knowmore')} backPress={() => { props.navigation.goBack() }} />


                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 3,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 3,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>
                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 3,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'KnowMore'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.knowmore')} backPress={() => { props.navigation.goBack() }} />

                <FlatList
                    data={data}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    }
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, key }) =>
                        <View style={{ overflow: "hidden", flexWrap: "wrap", alignSelf: "center", justifyContent: "flex-start", height: screenHeight / 3, width: screenWidth / 1.05, backgroundColor: White, borderRadius: 10, borderWidth: 1, borderColor: MediumGrey, marginBottom: '4%' }}>
                            <View style={{ width: '100%', height: '70%', alignItems: 'center', justifyContent: 'center' }}>
                                <Image source={{ uri: item.image }} style={{ resizeMode: 'cover', width: '100%', height: '100%', }} />
                            </View>
                            <View style={{ flexDirection: 'row', height: '30%', backgroundColor: White, width: '90%', alignSelf: 'center', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 25, }}>{item.title}</Text>
                                <Button onPress={() => { props.navigation.navigate('ArticleDetails', { item: item }) }} style={{ width: screenWidth / 12, alignSelf: "center", height: screenWidth / 12, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 6 }}>
                                    <Image source={require('../images/modrek/details.png')} style={{ resizeMode: 'contain', width: '80%', height: '80%', }} />
                                    {/* <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.thedetails')}</Text> */}
                                </Button>
                            </View>
                        </View>
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", width: screenWidth / 1.05, overflow: "hidden", flexDirection: "column", marginTop: 10, flexWrap: "wrap", }}
                    onEndReachedThreshold={0.1}
                    onEndReached={LoadMore}
                // numColumns={2}
                />

                {loadingMoreData ? <ActivityIndicator /> : <></>}



                <View style={{ height: screenHeight / 8 }}></View>

                <MyFooter current={'KnowMore'} navigation={props.navigation} />

            </View>
        )
    }
}


export default KnowMore;
const styles = StyleSheet.create({
    activeButton: { height: 80, width: screenWidth / 2.4, backgroundColor: MediumBlue, borderWidth: 1, borderColor: Blue, borderRadius: 15, alignItems: 'center', justifyContent: 'center' },
    button: { height: 80, width: screenWidth / 2.4, backgroundColor: MediumGrey, borderRadius: 15, alignItems: 'center', justifyContent: 'center', },
    rbsheetText: { fontFamily: appFontBold, fontSize: screenWidth / 25, color: Black, marginVertical: '5%' },
    textBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, textDecorationLine: 'line-through', textDecorationStyle: 'dashed', textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },

    priceGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 28 },
    priceGreenBig: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 24 },
    priceBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28 },
    priceBlackBig: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 24 },

    activeConfirm: {
        backgroundColor: Red,
        width: screenWidth / 1.4,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 10,
        marginTop: '10%'
    },
    disapleConfirm: {
        backgroundColor: Red,
        width: screenWidth / 1.4,
        height: 45,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 10,
        marginTop: '10%',
        opacity: .5
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },

    imageContainer: {
        flex: 1,
        marginBottom: Platform.select({ ios: 0, android: 1 }), // Prevent a random Android rendering issue
        backgroundColor: White,
        borderRadius: 0,
        justifyContent: "center",
        borderWidth: .8,
        borderColor: MediumGrey
        // overflow:'hidden'
    },
    image: {
        // ...StyleSheet.absoluteFillObject,
        resizeMode: "cover",
        height: '100%',
        width: '100%',
        alignSelf: "center",
    },
});
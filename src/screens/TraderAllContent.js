import React, { useRef, useState } from 'react';
import { View, Text, Image, ImageBackground, StyleSheet, I18nManager, BackHandler, Share, Pressable } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkYellow, LightGreen, MediumGrey, DarkGrey, WhiteGery, screenHeight, Black, appColor2, Red, Red1 } from '../components/Styles';
import { Button, Toast } from 'native-base';
import MyFooter from '../components/MyFooter';
import Header from '../components/Header';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { strings } from './i18n';
import Loading from '../components/Loading';
import LoadingMore from '../components/LoadingMore';
import PartnershipContainer from '../components/PartnershipContainer';
import OrdersContainer from '../components/OrdersContainer';
import { useDispatch } from 'react-redux';
import { useEffect } from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import * as cartActions from '../../Store/Actions/cart';
import * as ordersActions from '../../Store/Actions/orders';
import Toaster from '../components/Toaster';
import FarmContainer2 from '../components/FarmContainer2';
import * as farmesActions from '../../Store/Actions/farms';
import RBSheet from 'react-native-raw-bottom-sheet';
import StarRating from 'react-native-star-rating';
import ImageViewer from 'react-native-image-zoom-viewer';

const TraderAllContent = props => {

    const [buttonId, setButtonId] = useState('0');
    const [farms, setFarms] = useState([]);
    const [item, setItem] = useState({});
    const [lastOrders, setLastOrders] = useState([]);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [lastPageNumber, setLastPageNumber] = useState(1);
    const [pageNumber1, setPageNumber1] = useState(1);
    const [lastPageNumber1, setLastPageNumber1] = useState(1);
    const [Reload, setReload] = useState(false);
    const [subscriped, setSubscriped] = useState(false);
    // const [subscriped, setSubscriped] = useState(props.route.params.item.trader.is_subscribed_to);
    const [noResault, setNoResault] = useState('');
    const [sheepImages, setSheepImages] = useState([]);

    const [imageUrl, setImageUrl] = useState('https://iiif.wellcomecollection.org/image/A0000824/full/full/0/default.jpg');
    const [iddd, setIddd] = useState(0);
    const refRbSheet = useRef();
    const [viewId, setViewId] = useState(0);
    const dispatch = useDispatch();
    const [activeLive, setActiveLive] = useState(0);
    const [sheep, setSheep] = useState({});
    const [trader, setTrader] = useState({});

    useEffect(() => {
        setItem({})

        const getTraderFarms = async () => {
            try {
                setLoading(true)
                let response = await dispatch(farmesActions.getTraderFarms(props.route.params.trader_id));
                if (response.success == true) {
                    if (response.data.farms.length == 0) {
                        setNoResault(strings('lang.No_Results'));
                        setFarms([]);
                        setTrader(response.data.trader);
                    }
                    else {
                        setNoResault('');
                        setFarms(response.data.farms);
                        setTrader(response.data.trader);
                    }

                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getTraderFarms();
    }, []);



    const LoadMore = async () => {
        console.log('pageNumber', pageNumber);
        console.log('lastPageNumber', lastPageNumber);
        if (pageNumber < lastPageNumber) {
            setLoadingMore(true)
            try {
                let response = await dispatch(ordersActions.getCurrentOrders(pageNumber + 1));

                if (response.success == true) {
                    setCurrentOrders([...currentOrders, ...response.data.items]);
                    setPageNumber(pageNumber + 1);
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false);
                }
            } catch (err) {
                setLoadingMore(false);
            }
        }
        else {
        }
    }

    const toggleSubscription = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(farmesActions.traderoggleSubscription(item.trader.id));
            if (response.success == true) {
                setSubscriped(response.data.is_subscribed)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
        }
        catch (err) {
            setLoadingMore(false)
            console.log('err', err)
        }
    };

    const shareTrader = async () => {
        try {
            const result = await Share.share({
                title: 'App link',
                // message: 'https://play.google.com/store/apps/details?id=com.ModrkClient',
                // message: 'ModrkClient:https://modrk.greencodet.com',
                // url: ':ModrkClient:https://modrk.greencodet.com'
                message: `https://modrk.greencodet.com/trader/${trader.id}`,
                url: `https://modrk.greencodet.com/trader/${trader.id}`
            });
            if (result.action === Share.sharedAction) {
                if (result.activityType) {
                    // shared with activity type of result.activityType
                    console.log('aasdasd', result.activityType);

                } else {
                    // shared
                }
            } else if (result.action === Share.dismissedAction) {
                // dismissed
            }
        } catch (error) {
            alert(error.message);
        }
    };


    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.AllContentTrader')} backPress={() => {
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >

                    < View style={{ marginBottom: screenHeight / 100, width: '100%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 12,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>


                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 6,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'MyOrders'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart', { shared: false }) }} title={strings('lang.AllContentTrader')} backPress={() => {
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                }}
                />

                {loadingMore ?
                    <LoadingMore />
                    :
                    <></>
                }
                <View style={{ alignItems: "center", width: '100%', justifyContent: 'space-between', alignSelf: "center", flexDirection: 'row', backgroundColor: WhiteGery, paddingHorizontal: '5%', marginBottom: 5 }}>
                    <View style={{ width: screenWidth / 9, height: screenWidth / 9, borderRadius: screenWidth / 20, backgroundColor: MediumGrey, overflow: 'hidden', marginVertical: 10, paddingTop: 5 }}>
                        <Image source={require('../images/modrek/userr.png')} style={{ width: '100%', height: '100%', resizeMode: 'contain', tintColor: Red }} />
                    </View>
                    <View style={{ width: screenWidth / 2, height: screenWidth / 9, alignItems: 'flex-start' }}>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey }}>{strings('lang.Trader')}</Text>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black }}>{trader && trader.name}</Text>
                    </View>
                    <TouchableOpacity onPress={() => { shareTrader() }} transparent style={{ marginEnd: '5%', backgroundColor: Red, height: screenHeight / 35, overflow: 'hidden', flexDirection: 'row', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', paddingHorizontal: 5 }}>
                        {/* <TouchableOpacity onPress={() => { toggleSubscription() }} transparent style={{ marginEnd: '5%', backgroundColor: Red, height: screenHeight / 35, overflow: 'hidden', flexDirection: 'row', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', paddingHorizontal: 5 }}> */}
                        <Image source={subscriped ? require('../images/modrek/subscripe.png') : require('../images/modrek/share.png')} style={{ resizeMode: 'contain', alignSelf: 'center', width: screenWidth / 25, height: '60%', tintColor: White, }} />
                        {/* <Image source={subscriped ? require('../images/modrek/subscripe.png') : require('../images/modrek/subscriped.png')} style={{ resizeMode: 'contain', alignSelf: 'center', width: screenWidth / 20, height: '70%', tintColor: White, }} /> */}
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 38, color: White, marginStart: 2 }}>{strings('lang.Share')}</Text>
                        {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 38, color: White, marginStart: 2 }}>{subscriped ? strings('lang.Subscriped') : strings('lang.Subscripe')}</Text> */}
                    </TouchableOpacity>
                </View>

                <FlatList
                    data={farms}
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, index }) =>
                        <FarmContainer2 index={index} activeLive={activeLive} press={() => { setActiveLive(item.id) }} item={item} detailsPress={async () => { await setItem(item); refRbSheet.current.open() }} livePress={() => { props.navigation.push('Live', { screen: 'Trader', item: item, link: item.camera_url }) }} traderPress={() => { props.navigation.navigate('TraderAllContent', { item: item, farms: farms }) }} />
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: '100%', backgroundColor: White, paddingVertical: 5, }}
                // onEndReached={() => LoadMore()}
                // onEndReachedThreshold={0.1}
                // numColumns={2}

                />


                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 1.5}
                    openDuration={250}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderTopEndRadius: 25,
                            borderTopStartRadius: 25,
                            width: '100%',
                            alignSelf: 'center',
                            // bottom: '25%'
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey
                        }
                    }}
                    closeOnDragDown={true}
                    onClose={() => { setViewId(0); setIddd(0); setSheep({}); setSheepImages([]) }}
                >
                    <View style={{ flexDirection: 'column', width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', paddingBottom: '1%' }}>

                        <View style={{ flexDirection: 'row', height: screenHeight / 18, width: '100%', alignItems: 'center', justifyContent: 'space-between', }}>
                            <Text style={styles.text}> {strings('lang.thedetails')}</Text>
                            <Button transparent onPress={() => { refRbSheet.current.close(); setViewId(0); setIddd(0); setSheep({}); setSheepImages([]) }} style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={require('../images/modrek/x.png')} style={{ width: '50%', height: '50%', resizeMode: 'contain', }} />
                            </Button>
                        </View>

                        <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                        <View style={{ height: screenHeight / 18, flexDirection: "row", alignItems: 'center', alignSelf: 'flex-start' }}>
                            <StarRating
                                disabled={true}
                                maxStars={5}
                                starSize={screenHeight / 40}
                                starStyle={{ marginEnd: 2, alignSelf: 'flex-start' }}
                                rating={item.rating}
                                fullStarColor={DarkYellow}
                            />
                            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>{`(${item.rating_count})`} </Text>
                        </View>

                        <View style={{ width: '110%', height: 3, backgroundColor: WhiteGery, }}></View>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', marginBottom: 3 }}>{strings('lang.sheepAvailable')}</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ minHeight: screenHeight / 16, alignSelf: 'flex-start', }}>
                            {item.sheep && item.sheep.map((item, index) => (
                                <Pressable
                                    onPress={() => {
                                        setViewId(1);
                                        setIddd(item.id);
                                        setSheep(item);
                                        let sheepImages = [];
                                        for (let itemm of item.images) {
                                            sheepImages = [...sheepImages, { url: itemm }]
                                        };
                                        setSheepImages(sheepImages)
                                    }}
                                    style={{ backgroundColor: iddd == item.id ? LightGreen : null, width: screenWidth / 9, height: screenHeight / 16, borderTopEndRadius: 10, borderTopStartRadius: 10, paddingTop: 5, alignItems: 'center' }}
                                >
                                    <View style={{ width: screenWidth / 13, height: screenWidth / 13, borderRadius: screenWidth / 26, backgroundColor: item.collar_color ? item.collar_color : Black, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}>{index + 1}</Text>
                                    </View>
                                </Pressable>
                            ))}
                        </ScrollView>
                        {viewId == 1
                            ?
                            <>
                                <View style={{ backgroundColor: LightGreen, width: '100%', borderRadius: 0, paddingTop: 10, flexDirection: 'row', minHeight: screenHeight / 5 }}>
                                    <View style={{ width: '20%', height: '100%', alignItems: 'center' }}>
                                        <Image source={require('../images/modrek/qr.png')} style={{ width: '100%', height: screenHeight / 13, resizeMode: 'contain', }} />
                                        {sheepImages.length == 0
                                            ?
                                            <></>
                                            :
                                            <Pressable onPress={() => { setViewId(2); console.log('sheep.images', sheepImages); }} style={{ width: screenWidth / 6.5, height: screenHeight / 13, resizeMode: 'contain', borderRadius: 5 }}>
                                                {/* <Image source={require('../images/modrek/Rectangle-7.png')} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', }} /> */}
                                                <Image source={{ uri: sheepImages[0].url }} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', borderRadius: 5 }} />
                                                <Image source={require('../images/modrek/scanner2.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', position: 'absolute', top: '15%', start: '15%' }} />
                                            </Pressable>
                                        }

                                    </View>
                                    <View style={{ width: '80%', height: '100%', alignItems: 'center', paddingEnd: 10, paddingBottom: 10 }}>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Weight')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{`${item.weight && item.weight.from} kg -${item.weight && item.weight.to} kg`}</Text>
                                            </View>
                                        </View>
                                        {/* <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.OrderNumber')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{'2'}</Text>
                                        </View>
                                    </View> */}
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Gender')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{item.sheep_category && item.sheep_category.name}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>

                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.sealcolor')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.stamp_color}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.collarcolor')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: sheep.collar_color, alignSelf: 'flex-start' }}>{sheep.collar_color}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.referencenumber')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.id}</Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>

                                <Button
                                    onPress={() => { refRbSheet.current.close(); props.navigation.push('Live', { screen: 'Trader', item: item, link: item.camera_url }) }}
                                    style={styles.buttonContainer}>
                                    <Text style={styles.buttonText}>{strings('lang.Gotobookingg')}</Text>
                                </Button>
                            </>
                            :
                            viewId == 2
                                ?
                                <View style={{ width: '100%', height: screenHeight / 3, }}>
                                    <Button transparent onPress={() => { setViewId(1) }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                                        <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                                    </Button>
                                    {/* <Image source={require('../images/modrek/Rectangle-7.png')} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', }} /> */}

                                    <ImageViewer onChange={(imageIndex) => { setImageUrl(sheepImages[imageIndex].url) }} enableSwipeDown={false} onSwipeDown={() => { setModalImageVisible(false) }} backgroundColor='white' style={{ backgroundColor: 'White', maxHeight: '75%', borderRadius: 10, overflow: 'hidden' }} imageUrls={sheepImages} />
                                    <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', backgroundColor: White, zIndex: 100, padding: 5 }} >
                                        {sheepImages.map((item, index) => {
                                            return (
                                                <Button transparent onPress={() => { console.log('1'); setImageUrl(sheepImages[index].url) }} style={styles.imageActive}>
                                                    <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', borderRadius: 5 }} />
                                                </Button>
                                            )
                                        })}
                                    </ScrollView>
                                </View>
                                :
                                <></>
                        }
                    </View>

                </RBSheet>

                <View style={{ height: screenHeight / 9 }}></View>



                <MyFooter current={'MyOrders'} navigation={props.navigation} />
            </View>
        )
    }
}
export default TraderAllContent;
const styles = StyleSheet.create({
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modal2: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.2,
        height: screenHeight / 3.5,
        position: 'absolute',
        top: screenHeight / 2.8,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10,
        paddingVertical: '8%',
        justifyContent: 'space-between',
        paddingHorizontal: '5%'
    },
    modaltext: {
        fontFamily: appFontBold,
        color: Red,
        fontSize: screenWidth / 22,
        alignSelf: 'center',
        marginTop: '0%',
        textAlign: 'center'
    },
    modalbuttonContainer: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: White,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
        borderWidth: 1,
        borderColor: Red,
    },
    modalbuttonContainer2: {
        width: screenWidth / 4,
        height: '75%',
        backgroundColor: Red,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    modalbuttonText2: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    modalbuttonText: {
        color: Red,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    textBlackCenter: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 29, },
    textBlackCenter2: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 27, },
    text: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        color: Black
    },
    buttonContainer: {
        backgroundColor: Red,
        width: '100%',
        height: screenHeight / 18,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '5%',

    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    imageActive: { height: 80, width: screenWidth / 6, borderColor: MediumGrey, borderRadius: 15, overflow: 'hidden', alignSelf: 'center', marginHorizontal: 5 },
    imageUnactive: { height: 75, width: screenWidth / 6, borderWidth: 1, borderColor: MediumGrey, borderRadius: 10, overflow: 'hidden', alignSelf: 'center', marginHorizontal: 5 },
});
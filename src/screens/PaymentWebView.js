import { useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import React, { useEffect, useRef, useState } from 'react';
import { Image, StyleSheet, Text, View } from 'react-native';
import RBSheet from 'react-native-raw-bottom-sheet';
import WebView from 'react-native-webview';
import { useDispatch } from 'react-redux';
import Header from '../components/Header';
import Loading from '../components/Loading';
import {
    Black,
    DarkGreen,
    Red,
    White,
    WhiteGery,
    appFontBold,
    screenHeight,
    screenWidth
} from '../components/Styles';
import Toaster from '../components/Toaster';
import { strings } from './i18n';

const PaymentWebView = props => {
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [modalVisible, setModalVisible] = useState(false)
    const dispatch = useDispatch();
    const isFocused = useIsFocused();
    const [Url, setUrl] = useState('')
    const refRBSheet = useRef();
    const [order, setOrder] = useState(props.route.params.order)
    const webViewRef = useRef(null);

    useEffect(() => {
        console.log('props.route.params.orderId', props.route.params.orderId);
        console.log('props.route.params.orderId', props.route.params.order);

        if (props.route.params.methodId == 4) {
            setUrl(`https://modrk.greencodet.com/payment/tamara-pay?order_id=${props.route.params.orderId}`)
        } else {
            setUrl(`https://modrk.greencodet.com/payment/pay?order_id=${props.route.params.orderId}&test=1`)
            // setUrl(`https://modrk.greencodet.com/payment/pay?order_id=${props.route.params.orderId}`)
        }


    }, []);


    const _onNavigationStateChange = async (webViewState) => {
        if (!isFocused) return; // 👈 Block callback when not on screen

        console.log('webViewState', webViewState)
        console.log('webViewState.url', webViewState.url)
        if (webViewState.url.includes('success')) {
            console.log("finish")
            Toaster(
                'top',
                'success',
                DarkGreen,
                strings('lang.Paid'),
                White,
                1500,
                screenHeight / 50,
            );
            props.navigation.navigate("CompleteOrder3", { screen: 'CompleteOrder2', shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId })

        }
        else if (webViewState.url.includes('failed')) {
            Toaster(
                'top',
                'danger',
                Red,
                strings('lang.Unpaid'),
                White,
                1500,
                screenHeight / 50,
            );
            // setUrl(`https://modrk.greencodet.com/payment/pay?order_id=${props.route.params.order}`)
            props.navigation.push("CompleteOrder2", { screen: 'CompleteOrder1', shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: props.route.params.timeLeft })

        }

        else {

        }
    }


    return (
        <View style={{ flex: 1, backgroundColor: White }}>
            <Header
                title={strings('lang.Payment')}
                backPress={() => {
                    props.navigation.push("CompleteOrder2", { screen: 'CompleteOrder1', shared: props.route.params.shared, order: props.route.params.order, sheepTypeId: props.route.params.sheepTypeId, timeLeft: props.route.params.timeLeft })
                }}
            />

            {loadingMore
                ?
                <Loading />
                :
                <View></View>
            }

            {Url ? (
                <WebView
                    ref={webViewRef}
                    // source={{ uri: '[domain.com]/payment/index.php?page=myfatoorah&arabia_hook_id='+this.state.system_hook_id+'&arabia_rent_request_id='+this.state.rent_request_id+'&arabia_json_responses=1' }}
                    style={{ height: screenHeight / 1.2, width: screenWidth, }}
                    source={{ uri: Url }}
                    onNavigationStateChange={_onNavigationStateChange}
                    javaScriptEnabled={true}
                    domStorageEnabled={true}
                    startInLoadingState={false}
                />
            ) : (
                <View></View>
            )}
            <RBSheet
                ref={refRBSheet}
                height={screenHeight / 2}
                openDuration={280}
                customStyles={{
                    container: {
                        // justifyContent: "center",
                        alignItems: "center",
                        width: '90%',
                        marginBottom: screenHeight / 4,
                        alignSelf: 'center',
                        borderRadius: 5

                    },
                    wrapper: {

                    },
                    draggableIcon: {
                        // width: '25%',
                        // backgroundColor: DarkGrey
                    }
                }}
            // onClose={() => { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }) }}
            // closeOnDragDown={true}
            >
                <View style={{ alignItems: 'center', alignSelf: 'center', justifyContent: 'center', width: '100%', paddingVertical: '3%', }}>

                    {/* <View style={{ width: '80%', marginStart: 15, height: '40%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', }}>
                            <Image source={require('../images/modrek/animal.png')} style={{ resizeMode: 'contain', width: '80%', height: '90%', tintColor: Red, }} />
                        </View> */}
                    <View style={{ width: '80%', height: '40%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', }}>
                        <Image source={require('../images/modrek/surface-7.png')} style={{ resizeMode: 'contain', width: '60%', height: '60%', }} />
                    </View>

                    <View style={{ height: '30%', width: '90%', alignItems: 'center', alignSelf: 'center', justifyContent: 'center', flexDirection: 'row' }}>
                        <Text style={{ fontSize: screenWidth / 20, fontFamily: appFontBold, color: Red, }}>{strings('lang.Buyingsucceeded')}</Text>
                        {/* <Image source={require('../images/modrek/surface-7.png')} style={{ resizeMode: 'contain', width: '15%', height: '20%', }} /> */}
                    </View>

                    <View style={{ alignItems: "center", justifyContent: 'center', width: '90%', alignSelf: "center", height: '30%' }}>
                        <Button onPress={() => {
                            refRBSheet.current.close();
                            props.navigation.navigate("OrderTracking", { item: order })
                        }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: White, }}>{strings('lang.Ordertracking')}</Text>
                        </Button>
                        <Button onPress={() => {
                            refRBSheet.current.close();
                            props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                        }} style={{ width: '90%', alignSelf: "center", height: 45, backgroundColor: WhiteGery, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 10, marginVertical: 5 }}>
                            <Image source={require('../images/modrek/svgexport-1.png')} style={{ resizeMode: 'contain', width: '10%', height: '50%', tintColor: Black, }} />
                            <Text style={{ fontSize: screenWidth / 28, fontFamily: appFontBold, color: Black, }}>{strings('lang.Home')}</Text>
                        </Button>
                    </View>
                </View>
            </RBSheet>
        </View >
    );
};

const styles = StyleSheet.create({


});

export default PaymentWebView;


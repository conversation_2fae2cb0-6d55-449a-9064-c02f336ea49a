import React, { useRef, useState } from 'react';
import { View, Modal, Animated, Text, Image, ImageBackground, StyleSheet, I18nManager, Alert } from "react-native";
import { appFont, appFontBold, Green, WhiteGreen, screenWidth, White, DarkGreen, Blue, MediumGrey, DarkGrey, WhiteGery, screenHeight, appColor1, Black, appColor2, LightGreen, Red, Red1 } from '../components/Styles';
import { Button, Input, Textarea, Toast } from 'native-base';
import { FlatList, ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import Header from '../components/Header';
import { strings } from './i18n';
import MyFooter from '../components/MyFooter';
import { KeyboardAwareScrollView, ScrollableComponent } from 'react-native-keyboard-aware-scroll-view';
import RBSheet from 'react-native-raw-bottom-sheet';
import LoadingMore from '../components/LoadingMore';
import { useDispatch, useSelector } from 'react-redux';
import { useEffect } from 'react';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import LiveModal from '../components/LiveModal';
import LiveModal2 from '../components/LiveModal2';
import Toaster from '../components/Toaster';
import * as ordersActions from '../../Store/Actions/orders';
import ImageViewer from 'react-native-image-zoom-viewer';

const MoveOrderToSlaughter = props => {

    const [data, setData] = useState([{}, {}]);
    const [address, setAddress] = useState({});
    const [discount, setDiscount] = useState('');
    const [methodId, setMethodId] = useState(0);
    const [photo, setPhoto] = useState('');
    const refRBSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [sheepCooling, setSheepCooling] = useState(false);
    const [sheepCutting, setSheepCutting] = useState(false);
    const dispatch = useDispatch();
    const statusess = useSelector(state => state.settings.statuses)

    const [statuses, setStatuses] = useState(statusess[3] ? statusess[3].children : []);
    // const [statuses, setStatuses] = useState(props.route.params.statuses);
    // const [orderStatuses, setOrderStatuses] = useState(props.route.params.orderStatuses);
    const [orderStatuses, setOrderStatuses] = useState([]);
    const [ModalVisible, setModalVisible] = useState(false);
    const [visible, setIsVisible] = useState(false);
    const [sheepImagesCooling, setSheepImagesCooling] = useState([]);
    const [sheepImagesCutting, setSheepImagesCutting] = useState([]);
    const [IsOpen, setIsOpen] = useState(false);


    useEffect(() => {
        console.log('props.route.params.statuses', props.route.params.statuses);
        console.log('props.route.params.orderStatuses', props.route.params.orderStatuses);
        for (let item of props.route.params.orderStatuses) {
            console.log('item', item);
        }

        const getCurrentOrder = async () => {
            try {
                setLoading(true)
                let response = await dispatch(ordersActions.getOrder(props.route.params.id));
                if (response.success == true) {
                    if (response.data && response.data.cooling_image == null) {

                    } else {
                        setSheepCooling(true)
                        let sheepImagesCooling = '';
                        sheepImagesCooling = [...sheepImagesCooling, { url: response.data.cooling_image && response.data.cooling_image }]
                        console.log('images', sheepImagesCooling);
                        setSheepImagesCooling(sheepImagesCooling)
                    }
                    if (response.data.cutting_image.length == 0) {

                    } else {
                        setSheepCutting(true)
                        let sheepImagesCutting = '';
                        for (let image of response.data.cutting_image) {
                            sheepImagesCutting = [...sheepImagesCutting, { url: image }]
                        }
                        setSheepImagesCutting(sheepImagesCutting)
                    }


                    console.log('mostafa', response.data.statuses[3].children);
                    setOrderStatuses(response.data.statuses[3] ? response.data.statuses[3].children : [])

                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red1,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getCurrentOrder()
        opacityy();

        if (IsOpen == false) {
            const interval = setInterval(() => {

                console.log('statusess', statusess);
                getCurrentOrder();
                opacityy();
            }, 40000); // 60000ms = 1 minute
            // Cleanup interval on component unmount
            return () => clearInterval(interval);
        } else {

        }
    }, [IsOpen, opacity]);

    const opacity = useRef(new Animated.Value(1)).current; // Initialize opacity as an Animated Value

    const opacityy = () => {
        const fadeAnimation = Animated.loop(
            Animated.sequence([
                Animated.timing(opacity, {
                    toValue: 0.5, // Target opacity
                    duration: 1000, // Duration in ms
                    useNativeDriver: true, // Optimize for native performance
                }),
                Animated.timing(opacity, {
                    toValue: 1, // Reset to full opacity
                    duration: 1000, // Duration in ms
                    useNativeDriver: true,
                }),
            ])
        );

        fadeAnimation.start(); // Start the animation

        return () => fadeAnimation.stop();
    }

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => props.navigation.push('Cart', { shared: false })} title={strings('lang.Stagesoftheslaughterhouseproductionline')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >
                    < View style={{ marginTop: screenHeight / 50, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 2,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Home'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => props.navigation.push('Cart', { shared: false })} title={strings('lang.Stagesoftheslaughterhouseproductionline')} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    showsVerticalScrollIndicator={false}
                    style={{ width: '100%' }}
                >

                    {statuses.length != 0
                        &&
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 26, marginStart: '5%', alignSelf: 'flex-start' }}>{strings('lang.Ordertracking')}</Text>
                    }


                    <View style={{ flexDirection: 'row', width: '90%', alignSelf: 'center', marginTop: 5 }}>
                        <View style={{ flexDirection: 'column', width: '10%', alignItems: 'center', justifyContent: 'flex-start', marginTop: 6, }}>
                            {statuses.map((item, index) => (
                                <>
                                    <View style={{ width: 20, height: 20, borderRadius: 25, backgroundColor: orderStatuses.find(x => x.id === item.id) ? Red : White, borderWidth: 1, alignItems: 'center', justifyContent: 'center', }}>
                                        <Image source={require('../images/modrek/true.png')} style={{ tintColor: orderStatuses.find(x => x.id === item.id) ? White : Red, resizeMode: 'contain', width: '80%', height: '60%', }} />
                                    </View>
                                    {index == (statuses.length - 1)
                                        ?
                                        <></>
                                        :
                                        <View style={styles.line}>
                                            {/* <Image source={require('../images/modrek/line.png')} style={{ tintColor: index + 1 <= orderStatuses.length ? Red : MediumGrey, resizeMode: 'cover', width: '100%', height: '100%', }} /> */}
                                            <View style={{ backgroundColor: index + 1 <= orderStatuses.length ? Red : DarkGrey, width: 2, height: '100%', }} ></View>
                                            {/* <Image source={require('../images/modrek/line.png')} style={{ tintColor: index + 1 <= orderStatuses.length ? Red : MediumGrey, resizeMode: 'cover', width: '100%', height: '100%', }} /> */}
                                        </View>
                                    }

                                </>
                            ))}
                        </View>

                        <View style={{ flexDirection: 'column', width: '90%', }}>
                            {statuses.map((item, index) => (
                                <View style={styles.titlecontainer}>
                                    <View style={{ flexDirection: 'column', width: item.id == 9 || item.id == 10 ? '50%' : '65%', }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 32, textAlign: I18nManager.isRTL ? 'left' : 'right', color: orderStatuses.find(x => x.id === item.id) ? Red : MediumGrey }}>{item.name}</Text>
                                        <Text style={{ fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' }}>{orderStatuses.find(x => x.id === item.id) ? orderStatuses.find(x => x.id === item.id).date : ''}</Text>
                                    </View>
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 7 &&
                                        <View style={styles.button}>
                                            <View
                                                style={{ width: '90%', height: 30, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.Skinningstages')}</Text>
                                            </View>
                                        </View>
                                    }
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id != 7 &&
                                        // orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id != 10 &&
                                        // orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id != 9 &&
                                        < Animated.View style={[styles.button, {
                                            opacity,
                                            width:
                                                orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 9
                                                    ||
                                                    orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 10
                                                    ?
                                                    '25%'
                                                    :
                                                    '35%'
                                        }]}>
                                            <Button
                                                onPress={() => { props.navigation.navigate('LiveModal2', { sheeps: props.route.params.sheeps, sheep: props.route.params.sheep, status: orderStatuses[orderStatuses.length - 1], link: '', id: props.route.params.id }) }}
                                                style={{ width: '90%', height: 30, backgroundColor: Red1, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Image source={require('../images/modrek/svgexport-183.png')} style={{ tintColor: White, resizeMode: 'contain', width: '12%', height: '100%', marginEnd: 5 }} />
                                                <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.watch')}</Text>
                                            </Button>
                                        </Animated.View>
                                    }
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 10 &&
                                        <Animated.View style={[styles.button, { opacity }]}>
                                            <Button
                                                onPress={() => {
                                                    if (sheepCooling == true) {
                                                        setIsOpen(!IsOpen); setPhoto('cooling_image'); setIsVisible(!visible)
                                                    } else {
                                                        console.log('sd');
                                                        Toaster(
                                                            'top',
                                                            'danger',
                                                            Red1,
                                                            'لا يوجد صورة',
                                                            White,
                                                            1500,
                                                            screenHeight / 15,
                                                        );
                                                    }
                                                }}
                                                style={{ width: '90%', height: 30, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.openthephoto')}</Text>
                                            </Button>
                                        </Animated.View>
                                    }
                                    {/* {orderStatuses.find(x => x.id === item.id) && (item.id == 9 || item.id == 10) && item.id == orderStatuses[orderStatuses.length - 2].id &&
                                        <Animated.View style={[styles.button, { opacity }]}>
                                            <Button
                                                onPress={() => {
                                                    if (sheepCooling == true) {
                                                        setIsOpen(!IsOpen); setPhoto('cooling_image'); setIsVisible(!visible)
                                                    } else {
                                                        console.log('no image1');
                                                    }
                                                }
                                                }
                                                style={{ width: '90%', height: 30, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.openthephoto')}</Text>
                                            </Button>
                                        </Animated.View>
                                    } */}
                                    {orderStatuses.find(x => x.id === item.id) && item.id == orderStatuses[orderStatuses.length - 1].id && item.id == 9 &&
                                        <Animated.View style={[styles.button, { opacity }]}>
                                            <Button
                                                onPress={() => {
                                                    if (sheepCooling == true) {
                                                        setIsOpen(!IsOpen); setPhoto('cutting_image'); setIsVisible(!visible)
                                                    } else {
                                                        Toaster(
                                                            'top',
                                                            'danger',
                                                            Red1,
                                                            'لا يوجد صورة',
                                                            White,
                                                            1500,
                                                            screenHeight / 15,
                                                        );
                                                    }
                                                }}
                                                style={{ width: '90%', height: 30, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                                                <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.openthephoto')}</Text>
                                            </Button>
                                        </Animated.View>
                                    }
                                </View>
                            ))}
                        </View>
                    </View>
                </ScrollView >

                <Modal visible={visible} transparent={true}>
                    <Button transparent onPress={() => { setIsVisible(false); setPhoto(''); setIsOpen(!IsOpen) }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                        <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                    </Button>
                    {photo == 'cooling_image'
                        ?
                        <>

                            <View style={{
                                flex: 1,
                                width: screenWidth,
                                height: screenHeight,
                                backgroundColor: White,
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                                <FlatList
                                    data={sheepImagesCooling}
                                    keyExtractor={(item) => item.id}
                                    horizontal
                                    pagingEnabled
                                    showsHorizontalScrollIndicator={false}
                                    // initialScrollIndex={selectedIndex}
                                    style={{ width: screenWidth, height: '100%' }}
                                    getItemLayout={(data, index) => ({
                                        length: screenWidth, // Width of each image
                                        offset: screenWidth * index,
                                        index,
                                    })}
                                    renderItem={({ item }) => (
                                        <Image source={{ uri: item.url }} style={{
                                            width: screenWidth,
                                            height: screenHeight,
                                            resizeMode: 'contain'
                                            // marginHorizontal: 10,
                                            // borderRadius: 10,
                                        }} />
                                    )}
                                />
                            </View>
                            {/* <ImageViewer onChange={(imageIndex) => { setSheepImagesCooling(sheepImagesCooling[imageIndex].url) }} enableSwipeDown={true} onSwipeDown={() => { setIsVisible(false); setPhoto(''); setIsOpen(!IsOpen) }} backgroundColor='white' style={{ backgroundColor: 'White', height: '100%', borderRadius: 10, overflow: 'hidden' }} imageUrls={sheepImagesCooling} />
                            <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', zIndex: 100, padding: 5 }} >
                                {sheepImagesCooling.map((item, index) => {
                                    return (
                                        <Button transparent onPress={() => { console.log('1'); setSheepImagesCooling(sheepImagesCooling[index].url) }} style={styles.imageActive}>
                                            <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', borderRadius: 5 }} />
                                        </Button>
                                    )
                                })}
                            </ScrollView> */}
                        </>
                        :
                        <>

                            <View style={{
                                flex: 1,
                                width: screenWidth,
                                height: screenHeight,
                                backgroundColor: White,
                                justifyContent: 'center',
                                alignItems: 'center'
                            }}>
                                <FlatList
                                    data={sheepImagesCutting}
                                    keyExtractor={(item) => item.id}
                                    horizontal
                                    pagingEnabled
                                    showsHorizontalScrollIndicator={false}
                                    // initialScrollIndex={selectedIndex}
                                    style={{ width: screenWidth, height: '100%' }}
                                    getItemLayout={(data, index) => ({
                                        length: screenWidth, // Width of each image
                                        offset: screenWidth * index,
                                        index,
                                    })}
                                    renderItem={({ item }) => (
                                        <Image source={{ uri: item.url }} style={{
                                            width: screenWidth,
                                            height: screenHeight,
                                            resizeMode: 'contain'
                                            // marginHorizontal: 10,
                                            // borderRadius: 10,
                                        }} />
                                    )}
                                />
                            </View>
                            {/* <ImageViewer onChange={(imageIndex) => { setSheepImagesCutting(sheepImagesCutting[imageIndex].url) }} enableSwipeDown={true} onSwipeDown={() => { setIsVisible(false); setPhoto(''); setIsOpen(!IsOpen) }} backgroundColor='white' style={{ backgroundColor: 'White', height: '100%', borderRadius: 10, overflow: 'hidden' }} imageUrls={sheepImagesCutting} />
                            <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', zIndex: 100, padding: 5 }} >
                                {sheepImagesCutting.map((item, index) => {
                                    return (
                                        <Button transparent onPress={() => { console.log('1'); setSheepImagesCutting(sheepImagesCutting[index].url) }} style={styles.imageActive}>
                                            <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', borderRadius: 5 }} />
                                        </Button>
                                    )
                                })}
                            </ScrollView> */}
                        </>
                    }

                </Modal>
                {/* <LiveModal2 sheep={props.route.params.sheep} status={orderStatuses[orderStatuses.length - 1]} link={''} ModalVisible={ModalVisible} modalPress={() => { setModalVisible(!ModalVisible) }} /> */}


                < View style={{ height: screenHeight / 8, }}></View >
                <MyFooter navigation={props.navigation} />
            </View >
        )
    }
}

export default MoveOrderToSlaughter;
const styles = StyleSheet.create({
    button: { width: '25%', alignSelf: 'center', alignItems: 'center', justifyContent: 'center', height: '100%', },
    titlecontainer: { marginStart: '5%', height: 50, flexDirection: 'row', width: '100%', alignItems: 'center' },
    containerline: { width: 20, height: 20, borderRadius: 4, borderWidth: 1, borderColor: Red, alignItems: 'center', justifyContent: 'center' },
    line: { width: '10%', height: 30, },
    textBlack: { fontFamily: appFont, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-start' },
    textBlack0: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, alignSelf: 'flex-end' },
    textBlack1: { fontFamily: appFont, color: Black, fontSize: screenWidth / 24, alignSelf: 'flex-start' },
    textAddress: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 26 },
    textRed1: { fontFamily: appFontBold, color: Red, fontSize: screenWidth / 24 },
    textRed2: { fontFamily: appFont, color: Red, fontSize: screenWidth / 30 },
    textGrey: { fontFamily: appFontBold, color: MediumGrey, fontSize: screenWidth / 26, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey2: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },
    textGrey1: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey3: { fontFamily: appFont, color: DarkGrey, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 30 },
    textWhite1: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 24 },
    title: {
        color: Black,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28,
        marginStart: '5%',
        marginBottom: 5,
        alignSelf: 'flex-start'
    },
    squareGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: 2, marginEnd: '3%' },
    circleGreen: { width: screenHeight / 50, height: screenHeight / 50, backgroundColor: appColor1, borderRadius: screenHeight / 100, marginStart: '20%', marginEnd: '3%' },
});
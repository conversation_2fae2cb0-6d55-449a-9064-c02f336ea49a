import React, { useRef, useState } from 'react';
import { BackHandler, I18nManager, Image, Modal, RefreshControl, StyleSheet, Text, View } from "react-native";
import { Black, DarkGrey, DarkYellow, LightGreen, MediumGrey, Red, Red1, White, WhiteGery, appFont, appFontBold, screenHeight, screenWidth } from '../components/Styles';
// import HomeHeader from '../components/HomeHeader';
import { useFocusEffect, useIsFocused } from '@react-navigation/native';
import { Button } from 'native-base';
import { useEffect } from 'react';
import { FlatList, ScrollView } from 'react-native-gesture-handler';
import RBSheet from 'react-native-raw-bottom-sheet';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';
import StarRating from 'react-native-star-rating';
import Pressable from 'react-native/Libraries/Components/Pressable/Pressable';
import { useDispatch } from 'react-redux';
import * as farmesActions from '../../Store/Actions/farms';
import FarmContainer from '../components/FarmContainer';
import Header from '../components/Header';
import MyFooter from '../components/MyFooter';
import { strings } from './i18n';


const Favourites = props => {
    const [visible, setIsVisible] = useState(false);

    const [checked, setChecked] = useState(false);
    const [page, setPage] = useState(1);
    const [last_page, setLast_page] = useState(1);
    const [has_more_pages, setHas_more_pages] = useState(false);
    const [favorite, setFavorite] = useState([]);
    const [item, setItem] = useState({});
    const refRbSheet = useRef();
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [pageNumber, setPageNumber] = useState(1);
    const [lastPageNumber, setLastPageNumber] = useState(1);
    const [Reload, setReload] = useState(false);
    const [viewId, setViewId] = useState(0);
    const [iddd, setIddd] = useState(0);
    const [sheep, setSheep] = useState({});
    const [sheepImages, setSheepImages] = useState([]);
    const [imageUrl, setImageUrl] = useState('https://iiif.wellcomecollection.org/image/A0000824/full/full/0/default.jpg');

    const dispatch = useDispatch();
    const IsFoucused = useIsFocused();

    useEffect(() => {
        const getFavoriteFarms = async () => {
            try {
                setLoading(true)
                let response = await dispatch(farmesActions.getFavoriteFarms(1));
                if (response.success == true) {
                    setFavorite(response.data.items)
                } else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 15,
                        );
                    }
                }
                setLoading(false);
            } catch (err) {
                console.log('err', err)
                setLoading(false);
            }
        };
        getFavoriteFarms()
    }, [IsFoucused, Reload]);

    const onRefresh = async () => {
        console.log('reload');
        try {
            setLoading(true)
            let response = await dispatch(farmesActions.getFavoriteFarms(1));
            if (response.success == true) {
                setFavorite(response.data.items)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoading(false);
        } catch (err) {
            console.log('err', err)
            setLoading(false);
        }
    }

    const LoadMore = async () => {
        console.log('pageNumber', pageNumber);
        console.log('lastPageNumber', lastPageNumber);
        if (pageNumber < lastPageNumber) {
            setLoadingMore(true)
            try {
                let response = await dispatch(farmesActions.getFavoriteFarms(pageNumber + 1));

                if (response.success == true) {
                    setFavorite([...favorite, ...response.data.items]);
                    setPageNumber(pageNumber + 1);
                    setLoadingMore(false);
                }
                else {
                    if (response.message) {
                        Toaster(
                            'top',
                            'danger',
                            Red,
                            response.message,
                            White,
                            1500,
                            screenHeight / 50,
                        );
                    }
                    setLoadingMore(false);
                }
            } catch (err) {
                setLoadingMore(false);
            }

        }
        else {
        }
    }

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                // BackHandler.exitApp()
                props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    if (loading) {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart') }} title={strings('lang.Favourites')} drawerPress={() => { props.navigation.navigate('More') }} backPress={() => { props.navigation.goBack() }} />

                <ScrollView
                    style={{ width: '100%', height: '100%', marginBottom: '2%' }}
                    showsVerticalScrollIndicator={false}
                >


                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    < View style={{ marginTop: screenHeight / 100, width: '95%', alignSelf: 'center' }}>
                        <SkeletonPlaceholder
                            highlightColor={MediumGrey}
                            backgroundColor={WhiteGery}
                            speed={1200}
                        >
                            <View
                                style={{
                                    width: '100%',
                                    height: screenHeight / 4.8,
                                    backgroundColor: WhiteGery,
                                    alignSelf: 'center'
                                }}
                            />
                        </SkeletonPlaceholder>
                    </View>

                    <View style={{ height: screenHeight / 8 }}></View>
                </ScrollView >
                <MyFooter current={'Favourites'} navigation={props.navigation} />
            </View >
        );

    }
    else {
        return (
            <View style={{ flex: 1, alignItems: 'center', backgroundColor: White }}>
                <Header cart cartPress={() => { props.navigation.push('Cart') }} title={strings('lang.Favourites')} drawerPress={() => { props.navigation.navigate('More') }}
                    backPress={() => { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }) }}
                />



                {favorite.length == 0 &&
                    <Text style={{ fontSize: screenWidth / 18, fontFamily: appFontBold, color: Red1, marginTop: screenHeight / 20, alignSelf: 'center' }}>{strings('lang.No_Results')}</Text>
                }

                <FlatList
                    data={favorite}
                    refreshControl={
                        <RefreshControl refreshing={Reload} onRefresh={onRefresh} />
                    }
                    showsVerticalScrollIndicator={false}
                    renderItem={({ item, index }) =>
                        // <FarmContainer index={index} item={item} detailsPress={async () => { }} livePress={() => { }} />
                        <FarmContainer fav index={index} item={item} detailsPress={async () => { await setItem(item); refRbSheet.current.open() }}
                            livePress={() => { props.navigation.push('Live', { screen: '', item: item, link: item.camera_url, sheepTypeId: 1 }) }}
                        // livePress={() => { props.navigation.navigate('Live', { item: item, link: 'rtsp://admin:<EMAIL>:4444/cam/realmonitor?channel=17&subtype=1' }) }} 
                        />
                    }
                    keyExtractor={item => item.id}
                    style={{ alignSelf: "center", overflow: "hidden", width: screenWidth, backgroundColor: White, marginTop: screenHeight / 80 }}                        // onEndReached={() => this.LoadMore()}
                // onEndReachedThreshold={0.1}
                // numColumns={2}

                />

                {/* <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 2}
                    openDuration={250}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderRadius: 10,
                            width: '90%',
                            alignSelf: 'center',
                            bottom: '30%'
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            // width: '25%',
                            // backgroundColor: DarkGrey
                        }
                    }}
                // closeOnDragDown={true}
                >
                    <View style={{ flexDirection: 'column', height: '100%', width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'flex-start' }}>
                        <View style={{ flexDirection: 'row', height: '10%', width: '100%', alignItems: 'center', justifyContent: 'space-between' }}>
                            <Text style={styles.text}> {strings('lang.farmdetails')}</Text>
                            <Pressable onPress={() => refRbSheet.current.close()} style={{ width: screenWidth / 12, height: screenWidth / 12, alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={require('../images/modrek/x.png')} style={{ width: '40%', height: '40%', resizeMode: 'contain', }} />
                            </Pressable>
                        </View>

                        <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                        <View style={{ height: screenHeight / 30, flexDirection: "row", alignItems: "flex-start", alignSelf: 'flex-start', marginVertical: '5%' }}>
                            <StarRating
                                disabled={true}
                                maxStars={5}
                                starSize={screenHeight / 40}
                                starStyle={{ marginEnd: 2 }}
                                rating={5}
                                fullStarColor={DarkYellow}
                            />
                            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>(10) 4 </Text>
                        </View>

                        <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>
                        <View style={{ flexDirection: 'column', height: screenHeight / 4.5, alignSelf: 'flex-start', alignItems: 'flex-start', justifyContent: 'flex-start' }}>
                            <View style={{ width: '50%', alignItems: 'center', alignSelf: 'flex-start', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <Text style={styles.title}>{strings('lang.Weight')} </Text>
                                <Text style={styles.title2}>{item.weight} {strings('lang.kg')}</Text>
                            </View>
                            <View style={{ width: '50%', alignItems: 'center', alignSelf: 'flex-start', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <Text style={styles.title}>{strings('lang.Age')} </Text>
                                <Text style={styles.title2}>{item.age} {strings('lang.year')}</Text>
                            </View>
                            <View style={{ width: '50%', alignItems: 'center', alignSelf: 'flex-start', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <Text style={styles.title}>{strings('lang.Quantity')} </Text>
                                <Text style={styles.title2}>{item.sheep_count}</Text>
                            </View>
                            <View style={{ width: '50%', alignItems: 'center', alignSelf: 'flex-start', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <Text style={styles.title}>{strings('lang.price')} </Text>
                                <Text style={styles.title2}>{item.sheep_avg_price} {strings('lang.SR')}</Text>
                            </View>
                            <View style={{ width: '50%', alignItems: 'center', alignSelf: 'flex-start', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <Text style={styles.title}>{strings('lang.barn')} </Text>
                                <Text style={styles.title2}>{item.code}</Text>
                            </View>
                            <View style={{ width: '50%', alignItems: 'center', alignSelf: 'flex-start', justifyContent: 'space-between', flexDirection: 'row' }}>
                                <Text style={styles.title}>{strings('lang.Gender')} </Text>
                                <Text style={styles.title2}>{item.type}</Text>
                            </View>
                        </View>

                        <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery, marginVertical: '5%' }}></View>

                        <Button onPress={() => { refRbSheet.current.close(); props.navigation.navigate('Live') }} style={{ width: '60%', alignSelf: "center", height: screenHeight / 20, backgroundColor: White, borderColor: Red, borderWidth: 1, alignItems: "center", justifyContent: "center", borderRadius: screenWidth / 10 }}>
                            <Image source={require('../images/modrek/vedio.png')} style={{ resizeMode: 'contain', width: '10%', height: '65%', marginHorizontal: 5 }} />
                            <Text style={{ fontSize: screenWidth / 32, fontFamily: appFontBold, color: Red, }}>{strings('lang.Direct')}</Text>
                        </Button>

                    </View>

                </RBSheet> */}

                <RBSheet
                    ref={refRbSheet}
                    height={screenHeight / 1.5}
                    openDuration={250}
                    customStyles={{
                        container: {
                            // justifyContent: "center",
                            alignItems: "center",
                            borderTopEndRadius: 25,
                            borderTopStartRadius: 25,
                            width: '100%',
                            alignSelf: 'center',
                            // bottom: '25%'
                        },
                        wrapper: {

                        },
                        draggableIcon: {
                            width: '25%',
                            backgroundColor: DarkGrey
                        }
                    }}
                    closeOnDragDown={true}
                >
                    <View style={{ flexDirection: 'column', width: '90%', alignSelf: 'center', alignItems: 'center', justifyContent: 'space-between', paddingBottom: '1%' }}>

                        <View style={{ flexDirection: 'row', height: screenHeight / 18, width: '100%', alignItems: 'center', justifyContent: 'space-between', }}>
                            <Text style={styles.text}> {strings('lang.thedetails')}</Text>
                            <Button transparent onPress={() => refRbSheet.current.close()} style={{ width: '10%', alignItems: 'center', justifyContent: 'center', }}>
                                <Image source={require('../images/modrek/x.png')} style={{ width: '50%', height: '50%', resizeMode: 'contain', }} />
                            </Button>
                        </View>

                        <View style={{ width: '110%', height: 2, backgroundColor: WhiteGery }}></View>

                        <View style={{ height: screenHeight / 18, flexDirection: "row", alignItems: 'center', alignSelf: 'flex-start' }}>
                            <StarRating
                                disabled={true}
                                maxStars={5}
                                starSize={screenHeight / 40}
                                starStyle={{ marginEnd: 2, alignSelf: 'flex-start' }}
                                rating={5}
                                fullStarColor={DarkYellow}
                            />
                            <Text style={{ fontFamily: appFont, fontSize: screenWidth / 30, color: Black }}>(1) </Text>
                        </View>

                        <View style={{ width: '110%', height: 3, backgroundColor: WhiteGery, }}></View>
                        <Text style={{ fontFamily: appFont, fontSize: screenWidth / 32, color: Black, alignSelf: 'flex-start', marginBottom: 3 }}>{strings('lang.sheepAvailable')}</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={{ minHeight: screenHeight / 16, alignSelf: 'flex-start', }}>
                            {item.sheep && item.sheep.map((item, index) => (
                                <Pressable
                                    onPress={() => {
                                        setViewId(1);
                                        setIddd(item.id);
                                        setSheep(item);
                                        let sheepImages = [];
                                        for (let itemm of item.images) {
                                            sheepImages = [...sheepImages, { url: itemm }]
                                        };
                                        setSheepImages(sheepImages)
                                    }}
                                    style={{ backgroundColor: iddd == item.id ? LightGreen : null, width: screenWidth / 9, height: screenHeight / 16, borderTopEndRadius: 10, borderTopStartRadius: 10, paddingTop: 5, alignItems: 'center' }}
                                >
                                    <View style={{ width: screenWidth / 13, height: screenWidth / 13, borderRadius: screenWidth / 26, backgroundColor: item.collar_color ? item.collar_color : Black, alignItems: 'center', justifyContent: 'center' }}>
                                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: White, }}>{index + 1}</Text>
                                    </View>
                                </Pressable>
                            ))}
                        </ScrollView>
                        {viewId == 1
                            ?
                            <>

                                <Modal visible={visible}
                                    transparent={true}
                                    onRequestClose={() => setIsVisible(false)}
                                    animationType="fade"
                                >
                                    <View style={{
                                        flex: 1,
                                        width: screenWidth,
                                        height: screenHeight,
                                        backgroundColor: White,
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}>

                                        <Button transparent onPress={() => { setIsVisible(false); }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                                            <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                                        </Button>
                                        <FlatList
                                            data={sheepImages}
                                            keyExtractor={(item) => item.id}
                                            horizontal
                                            pagingEnabled
                                            showsHorizontalScrollIndicator={false}
                                            // initialScrollIndex={selectedIndex}
                                            style={{ width: screenWidth, height: '100%' }}
                                            getItemLayout={(data, index) => ({
                                                length: screenWidth, // Width of each image
                                                offset: screenWidth * index,
                                                index,
                                            })}
                                            renderItem={({ item }) => (
                                                <Image source={{ uri: item.url }} style={{
                                                    width: screenWidth,
                                                    height: screenHeight,
                                                    resizeMode: 'contain'
                                                    // marginHorizontal: 10,
                                                    // borderRadius: 10,
                                                }} />
                                            )}
                                        />
                                    </View>

                                    {/* <ImageViewer
                        // onChange={(imageIndex) => { setSheepImages(sheepImages[imageIndex].url) }} 
                        enableSwipeDown={false} onSwipeDown={() => { setIsVisible(false); setIsOpen(!IsOpen) }} backgroundColor='white' style={{ backgroundColor: 'White', height: '100%', borderRadius: 10, overflow: 'hidden' }} imageUrls={sheepImages} />
                    <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', zIndex: 100, padding: 5 }} >
                        {sheepImages.map((item, index) => {
                            return (
                                <Button
                                    // transparent onPress={() => { console.log('1'); setSheepImages(sheepImages[index].url) }}
                                    style={styles.imageActive}
                                >
                                    <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', borderRadius: 5 }} />
                                </Button>
                            )
                        })}
                    </ScrollView> */}
                                </Modal>

                                <View style={{ backgroundColor: LightGreen, width: '100%', borderRadius: 0, paddingTop: 10, flexDirection: 'row', minHeight: screenHeight / 5 }}>
                                    <View style={{ width: '20%', height: '100%', alignItems: 'center' }}>
                                        <Image source={require('../images/modrek/qr.png')} style={{ width: '100%', height: screenHeight / 13, resizeMode: 'contain', }} />
                                        {sheepImages.length == 0
                                            ?
                                            <></>
                                            :
                                            <Pressable onPress={() => { setIsVisible(true); console.log('sheep.images', sheepImages); }} style={{ width: screenWidth / 6.5, height: screenHeight / 13, resizeMode: 'contain', }}>
                                                <Image source={{ uri: sheepImages[0].url }} style={{ width: '100%', height: '100%', opacity: .8, resizeMode: 'contain', borderRadius: 5 }} />
                                                <Image source={require('../images/modrek/scanner2.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', position: 'absolute', top: '15%', start: '15%' }} />
                                            </Pressable>
                                        }
                                    </View>
                                    <View style={{ width: '80%', height: '100%', alignItems: 'center', paddingEnd: 10, paddingBottom: 10 }}>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Weight')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: DarkGrey, alignSelf: 'flex-start' }}>{`${item.weight && item.weight.from} kg -${item.weight && item.weight.to} kg`}</Text>
                                            </View>
                                        </View>
                                        {/* <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                        <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.OrderNumber')}</Text>
                                        </View>
                                        <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{'2'}</Text>
                                        </View>
                                    </View> */}
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.Gender')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{item.sheep_category && item.sheep_category.name}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>

                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.sealcolor')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.stamp_color}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.collarcolor')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: sheep.collar_color, alignSelf: 'flex-start' }}>{sheep.collar_color}</Text>
                                            </View>
                                        </View>
                                        <View style={{ width: '100%', height: screenHeight / 40, flexDirection: 'row', alignItems: 'center' }}>
                                            <View style={{ width: '40%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{strings('lang.referencenumber')}</Text>
                                            </View>
                                            <View style={{ width: '60%', height: screenHeight / 40, alignItems: 'center', }}>
                                                <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, alignSelf: 'flex-start' }}>{sheep.id}</Text>
                                            </View>
                                        </View>
                                    </View>
                                </View>

                                <Button
                                    onPress={() => { refRbSheet.current.close(); props.navigation.push('Live', { item: item, link: item.camera_url }) }}
                                    style={styles.buttonContainer}>
                                    <Text style={styles.buttonText}>{strings('lang.Gotobooking')}</Text>
                                </Button>
                            </>
                            :
                            // viewId == 2
                            //     ?
                            //     <View style={{ width: '100%', height: screenHeight / 3, }}>
                            //         <Button transparent onPress={() => { setViewId(1) }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
                            //             <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
                            //         </Button>

                            //         <ImageViewer onChange={(imageIndex) => { setImageUrl(sheepImages[imageIndex].url) }} enableSwipeDown={false} onSwipeDown={() => { setModalImageVisible(false) }} backgroundColor='white' style={{ backgroundColor: 'White', maxHeight: '75%', borderRadius: 10, }} imageUrls={sheepImages} />
                            //         <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', backgroundColor: White, zIndex: 100, padding: 5 }} >
                            //             {sheepImages.map((item, index) => {
                            //                 return (
                            //                     <Button transparent onPress={() => { console.log('1'); setImageUrl(sheepImages[index].url) }} style={styles.imageActive}>
                            //                         <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', }} />
                            //                     </Button>
                            //                 )
                            //             })}
                            //         </ScrollView>
                            //     </View>
                            //     :
                            <></>
                        }
                    </View>

                </RBSheet>


                <View style={{ height: screenHeight / 9 }}></View>

                <MyFooter current={'Favourites'} navigation={props.navigation} />
            </View>
        )
    }
}

export default Favourites;
const styles = StyleSheet.create({
    // imageContainer: {
    //     flex: 1,
    //     marginBottom: Platform.select({ ios: 0, android: 1 }), // Prevent a random Android rendering issue
    //     backgroundColor: White,
    //     borderRadius: 0,
    //     justifyContent: "center",
    //     borderWidth: .8,
    //     borderColor: MediumGrey
    //     // overflow:'hidden'
    // },
    container: {
        flexDirection: 'row',
        alignSelf: 'flex-start',
        justifyContent: 'space-between',
        marginVertical: '1.5%'
    },

    title: {
        fontFamily: appFont,
        fontSize: screenWidth / 25,
        color: Black

    },
    title2: {
        fontFamily: appFont,
        fontSize: screenWidth / 28,
        color: Black,
    },
    text: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 25,
        color: Black
    },
    title1: {
        marginStart: '15%',
        fontFamily: appFont,
        fontSize: screenWidth / 25
    },

    image: {
        // ...StyleSheet.absoluteFillObject,
        resizeMode: "cover",
        height: '100%',
        width: '100%',
        alignSelf: "center",
    },
    activeButton: { height: '80%', minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: Red, borderBottomWidth: 2 },
    activeLabel: { fontFamily: appFontBold, fontSize: screenWidth / 32, color: Black, },
    Button: { height: '80%', minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', borderBottomColor: Black, borderBottomWidth: .2 },
    label: { fontFamily: appFont, fontSize: screenWidth / 32, color: 'black', },

    activeButton1: { height: '60%', backgroundColor: Red, borderRadius: 20, minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', marginHorizontal: 5, },
    activeLabel1: { fontFamily: appFont, fontSize: screenWidth / 30, color: White, marginHorizontal: 10 },
    Button1: { height: '60%', backgroundColor: WhiteGery, borderRadius: 15, minWidth: screenWidth / 5, alignItems: "center", justifyContent: "center", alignSelf: 'center', marginHorizontal: 5, borderColor: WhiteGery, borderWidth: 1 },
    label1: { fontFamily: appFont, fontSize: screenWidth / 32, color: 'black', marginHorizontal: 10 },
    label2: { fontFamily: appFont, fontSize: screenWidth / 32, color: 'black', end: "90%", marginVertical: '7%' },
    buttonContainer: {
        backgroundColor: Red,
        width: '100%',
        height: screenHeight / 18,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,
        marginTop: '5%'
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    Button2: { height: '80%', width: screenWidth / 2.5, flexDirection: 'row', backgroundColor: WhiteGery, borderTopRightRadius: 20, borderBottomRightRadius: 20, alignItems: 'flex-start', justifyContent: 'flex-start', alignSelf: 'flex-start', borderColor: WhiteGery, borderWidth: 1 },

});
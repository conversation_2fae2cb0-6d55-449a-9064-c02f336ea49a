import { Button, Input } from 'native-base';
import React, { useState } from 'react';
import { Image, View, Pressable, Text, StyleSheet, I18nManager, BackHandler } from "react-native";
import { getUniqueId } from 'react-native-device-info';
import { TouchableOpacity } from 'react-native-gesture-handler';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { screenHeight, DarkBlue, screenWidth, DarkGrey, White, appFont, appFontBold, DarkGreen, Red, Green, appColor2, appColor1, Black, Red1, MediumGrey, } from "../components/Styles";
import { strings } from './i18n';
import * as authActions from '../../Store/Actions/auth';
import Toaster from '../components/Toaster';
import { useDispatch } from 'react-redux';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import I18n from './i18n';
import RNRestart from 'react-native-restart';
import LoadingMore from '../components/LoadingMore';
import { NativeModules } from 'react-native'
const { LiveModule } = NativeModules;
console.log(LiveModule);

const Login = props => {
    const [locale, setLocal] = useState("");
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const [countries, setCountries] = useState([]);
    const [phone, setPhone] = useState('');
    const [error_phone, setError_phone] = useState('');
    const [error_password, setError_password] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [activeText, setActiveText] = useState(0);
    const [loginStatus, setLoginStatus] = useState(0);
    const [error_name, setError_name] = useState('');
    const [name, setName] = useState('');
    const [error_email, setError_email] = useState('');
    const [email, setEmail] = useState('');
    const [conditions, setConditions] = useState(false);
    const [error_conditions, setError_conditions] = useState('');
    const dispatch = useDispatch();



    const Submit = async () => {
        if (!phone || !password) {
            if (!phone) {
                setError_phone(strings('lang.Please_insert_field'))
            }
            if (password == '') {
                setError_password(strings('lang.Please_insert_password'))

            }
            else if (password.length < 6) {
                setError_password(strings('lang.Password_must_be_more_6_characters'))

            }
        }
        else {
            setLoading(true)
            let deviceId = await getUniqueId();
            console.log('deviceId', deviceId)

            let Login = await dispatch(authActions.Login(deviceId, phone, password));
            console.log('LoginResponse', Login);

            if (Login.success == true) {
                if (Login.data.token) {
                    AsyncStorage.setItem("token", JSON.stringify(Login.data.token));
                    AsyncStorage.setItem("email", JSON.stringify(Login.data.user.email));
                    AsyncStorage.setItem("name", JSON.stringify(Login.data.user.name));
                    AsyncStorage.setItem("phone", JSON.stringify(Login.data.user.phone));
                    AsyncStorage.setItem("user", JSON.stringify(Login.data.user));
                    AsyncStorage.setItem("access_id", JSON.stringify(Login.data.user.id));
                    props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' })
                    setLoading(false)
                    Toaster(
                        'top',
                        'success',
                        'green',
                        (strings('lang.Login_Successfuly')),
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
                else {
                    props.navigation.navigate('ConfirmationCode', { phone: phone, forget: false })
                }
            }
            else {
                setLoading(false)
                Toaster(
                    'top',
                    'danger',
                    Red1,
                    Login.message,
                    White,
                    1500,
                    screenHeight / 15,
                );
            }
        }
    };

    const set_en = async () => {
        let lan = await AsyncStorage.getItem("lan")
        if (lan == 'ar') {
            // this.setState({
            //   lan: 'en',
            // })
            await AsyncStorage.setItem('lan', 'en')
            await AsyncStorage.setItem('stat', '1')
            I18n.locale = "en";
            await I18nManager.forceRTL(false);
            RNRestart.Restart();
        }
        else {
            await AsyncStorage.setItem('lan', 'ar')
            await AsyncStorage.setItem('stat', '1')
            I18n.locale = "ar";
            await I18nManager.forceRTL(true);
            RNRestart.Restart();
        }
    };

    const set_ar = async () => {
        let lan = await AsyncStorage.getItem("lan")
        if (lan == 'en') {
            // this.setState({
            //   lan: 'ar',
            //   modalDeleteVisible: false
            // })
            await AsyncStorage.setItem('lan', 'ar')
            await AsyncStorage.setItem('stat', '1')
            I18n.locale = "ar";
            await I18nManager.forceRTL(true);
            RNRestart.Restart();
        }
        else {
            await AsyncStorage.setItem('lan', 'en')
            await AsyncStorage.setItem('stat', '1')
            I18n.locale = "en";
            await I18nManager.forceRTL(false);
            RNRestart.Restart();
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            const onBackPress = () => {
                BackHandler.exitApp()
                return true;
            };

            BackHandler.addEventListener('hardwareBackPress', onBackPress);

            return () =>
                BackHandler.removeEventListener('hardwareBackPress', onBackPress);
        }, []),
    );

    return (
        <View style={{ width: '100%', alignSelf: "center", alignItems: "center", justifyContent: "center", flexDirection: "column", marginTop: "0%" }}>


            <KeyboardAwareScrollView
                showsVerticalScrollIndicator={false}
                style={{ width: '100%' }}
            >
                {loading ? <LoadingMore /> : <></>}

                <View style={{ height: screenHeight / 3, width: '100%', marginBottom: screenHeight / 40, alignItems: 'center', justifyContent: 'center', backgroundColor: Red }}>
                    <Image source={require('../images/modrek/logo.png')} style={{
                        width: '40%',
                        height: '100%',
                        alignSelf: 'center',
                        resizeMode: 'contain'
                        // tintColor:'white'
                    }} />
                </View>

                <View style={{ width: '90%', alignSelf: 'center', alignItems: "center", justifyContent: "flex-start", flexDirection: "column", height: screenHeight }}>


                    {/* <Pressable
                    style={{ width: screenWidth / 15, height: screenHeight / 15, position: 'absolute', top: Platform.OS == 'ios' ? '7%' : '5%', end: '5%', }}
                    onPress={() => { I18nManager.isRTL ? set_en() : set_ar() }}
                >
                    <Image
                        style={{ width: '100%', height: '100%', resizeMode: 'contain', tintColor: Red }}
                        source={require('../images/modrek/earth.png')} />
                </Pressable> */}

                    <Text style={styles.label}>{strings('lang.MobileNumber')}</Text>
                    <View style={activeText == 1 ? styles.activeInput : styles.input} >
                        <Input value={phone} onChangeText={(text) => { setPhone(text); setError_phone('') }}
                            // placeholderTextColor={'#707070'} placeholder={strings('lang.MobileNumber')}
                            style={styles.inputText}
                            onFocus={() => setActiveText(1)}
                            onBlur={() => { setActiveText(0) }}
                        />
                    </View>
                    {error_phone ?
                        <Text style={styles.loginError}>{error_phone}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <Text style={styles.label}>{strings('lang.Password')}</Text>
                    <View style={activeText == 2 ? styles.activeInput : styles.input} >
                        <Input value={password} onChangeText={(text) => { setPassword(text); setError_password('') }}
                            // placeholderTextColor={'#707070'} placeholder={(strings('lang.Password'))}
                            style={styles.inputText}
                            secureTextEntry={showPassword ? false : true}
                            onFocus={() => setActiveText(2)}
                            onBlur={() => { setActiveText(0) }}
                        />
                        <Pressable
                            style={styles.eyeContainer}
                            onPress={() => setShowPassword(!showPassword)}
                        >
                            <Image
                                source={require('../images/eye.png')}
                                style={styles.eyeIcon}
                            />
                        </Pressable>
                    </View>
                    {error_password ?
                        <Text style={styles.loginError}>{error_password}</Text>
                        :
                        <View style={{ height: 0 }}></View>
                    }

                    <View style={styles.forgetTextcontiner}>
                        <Pressable onPress={() => { props.navigation.navigate("ForgetPassword") }}>
                            <Text style={styles.forgetText}>{strings('lang.Forgotyourpassword')}</Text>
                        </Pressable>
                    </View>
                    <View style={{ height: screenHeight / 10, width: '100%', marginTop: '5%' }}>
                        <Pressable
                            onPress={() => {
                                Submit()
                                // props.navigation.navigate('Test')
                            }}
                            style={styles.buttonContainer}>
                            <Text style={styles.buttonText}>{strings('lang.Login')}</Text>
                        </Pressable>
                    </View>
                    <View style={{ alignSelf: 'center' }}>
                        <Pressable style={styles.skipContainer} onPress={() => { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }) }}>
                            <Image source={I18nManager.isRTL ? require('../images/Silal//svgexport-67.png') : require('../images/Silal/svgexport-66.png')} style={{ resizeMode: "contain", alignSelf: "center", tintColor: Red, marginHorizontal: 10, transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }] }} />
                            <Text style={styles.skipText}>{strings('lang.Skip')}</Text>
                        </Pressable >
                    </View>

                    <View style={styles.registerationContainer}>
                        <Text style={styles.registerationText}>{strings('lang.Donthaveanaccountpleasedo')}</Text>
                        <Pressable onPress={() => { props.navigation.navigate("Regestration") }}>
                            <Text style={styles.registerationText1}>{strings('lang.Signup')}</Text>
                        </Pressable>
                    </View>
                </View>
            </KeyboardAwareScrollView>
        </View>
    )
}

export default Login;
const styles = StyleSheet.create({
    eyeContainer: {
        width: screenWidth / 10,
        alignItems: 'center',
        justifyContent: 'center',
    },
    eyeIcon: {
        width: '40%',
        resizeMode: 'contain',
        tintColor: DarkGrey,
    },
    registerationContainer: {
        flexDirection: 'row',
        marginTop: screenHeight / 50,
        alignSelf: 'center'

    },
    registerationText: {
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
        color: Black
    },
    registerationText1: {
        color: Red,
        marginHorizontal: 5,
        fontFamily: appFontBold,
        fontSize: screenWidth / 30,
    },
    skipContainer: {
        marginVertical: screenHeight / 60,
        flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row',
    },
    skipText: {
        fontSize: screenWidth / 25,
        fontFamily: appFontBold,
        color: Red,
        alignSelf: 'center',
        // marginHorizontal: 5
    },
    forgetTextcontiner: {
        marginVertical: screenHeight / 60,
        alignSelf: 'flex-end',
        marginEnd: '0%'
    },
    forgetText: {
        alignSelf: 'flex-end',
        fontSize: screenWidth / 30,
        fontFamily: appFontBold,
        color: Red1
    },
    label: {
        fontSize: screenWidth / 30,
        fontFamily: appFontBold,
        alignSelf: 'flex-start',
        marginStart: '2%',
        marginBottom: 5,
        color: Black
    },
    container: {
        flex: 1,
        alignItems: 'center',
        height: screenHeight
    },
    activeInput: {
        height: 45, borderWidth: 1, borderRadius: 20, borderColor: Red, flexDirection: 'row', justifyContent: 'center',
        width: "100%", marginBottom: 10,
    },
    input: {
        height: 45, borderWidth: 1, borderRadius: 20, borderColor: MediumGrey, flexDirection: 'row', justifyContent: 'center',
        width: "100%", marginBottom: 10,
    },
    codeInput: {
        height: '100%', borderEndWidth: 1, borderRadius: 2, borderColor: DarkGrey, flexDirection: 'row', justifyContent: 'center',
        width: "45%"
    },
    inputText: {
        textAlign: I18nManager.isRTL ? 'right' : 'left', color: DarkGrey, width: '100%', fontFamily: appFontBold,
        fontSize: screenWidth / 30, alignSelf: 'center'
    },
    loginError: {
        color: 'red', fontFamily: appFont, alignSelf: 'flex-end', fontSize: screenWidth / 35
    },
    socialLoginContainer: { width: '100%', height: '100%', borderColor: DarkGrey, borderWidth: .5, alignItems: "center", borderWidth: 1, borderColor: "#ddd", borderRadius: 10, justifyContent: "center" },
    socialLoginContainer2: { width: '100%', height: '100%', borderColor: DarkGrey, borderWidth: .5, flexDirection: 'row', alignItems: "center", borderWidth: 1, borderColor: DarkBlue, borderRadius: 10 },
    buttonContainer: {
        backgroundColor: Red,
        width: '100%',
        height: screenHeight / 18,
        alignItems: "center",
        justifyContent: "center",
        alignSelf: "center",
        borderRadius: 25,

    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    buttonContainer2: {
        width: '60%',
        height: '7%',
        backgroundColor: DarkBlue,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        borderWidth: 1,
        borderRadius: 15

    },
});
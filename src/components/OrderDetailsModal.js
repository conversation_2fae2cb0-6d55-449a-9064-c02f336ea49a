import React, { useEffect, useState, useRef } from 'react';
import { View, Pressable, I18nManager, FlatList, ScrollView, StyleSheet, Modal, ImageBackground, Animated, Text, Image } from 'react-native';
import { Black, Blue, DarkGrey, MediumGrey, Red, Red1, White, appFontBold, screenHeight, screenWidth } from './Styles';
import { Button } from 'native-base';
import { strings } from '../screens/i18n';
import { TouchableOpacity } from 'react-native-gesture-handler';
import ImageResizer from 'react-native-image-resizer';



const OrderDetailsModal = (props) => {
  const [sheepImages, setSheepImages] = useState([]);
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const compressImages = async () => {
      try {
        const compressedImages = await Promise.all(
          props.order.all_images.map(async (uri) => {
            const response = await ImageResizer.createResizedImage(
              uri,       // Image URI
              800,       // New width
              600,       // New height
              'JPEG',    // Format (JPEG or PNG)
              100,        // Quality (0 to 100)
              90,         // Rotation
            );
            return response.uri; // Return the new compressed image URI
          })
        );
        let images = []
        // if (props.order.all_images.length != 0) {

        for (let item of compressedImages) {
          images = [...images, item]

        }
        // }

        console.log('images', images);

        setSheepImages(images)
        console.log('Compressed Images:', compressedImages);
        return compressedImages; // Array of compressed image URIs
      } catch (error) {
        console.error('Compression Error:', error);
      }
    };
    if (props.order.all_images.length != 0) {
      compressImages()
    }

    console.log('sheepImages', sheepImages);
    toggleExpand()
  }, [])

  const [expanded, setExpanded] = useState(false);
  const animationHeight = useRef(new Animated.Value(0)).current;

  const toggleExpand = () => {
    if (expanded) {
      Animated.timing(animationHeight, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      Animated.timing(animationHeight, {
        toValue: screenHeight, // You can adjust this or calculate dynamically
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
    setExpanded(!expanded);
  };


  return (
    <View>
      {/* <Modal
        transparent={true}
        animationType='slide'
        visible={props.ModalVisible}
      > */}

      {/* <Button transparent onPress={props.modalPress} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
      </Button> */}

      <View style={styles.modal2}>
        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 20, alignSelf: 'flex-start', marginBottom: 10 }}>{strings('lang.Bill')}</Text>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: '100%' }}

        >
          {sheepImages.length != 0
            ?
            <TouchableOpacity onPress={toggleExpand} style={styles.tileHeader}>
              <Text style={styles.titleText}>{'الصور'}</Text>
              <Text style={styles.arrow}>{expanded ? '▲' : '▼'}</Text>
            </TouchableOpacity>
            :
            <></>
          }


          {sheepImages.length != 0
            ?
            <Animated.View style={{
              width: '100%',
              maxHeight: animationHeight,
              alignItems: 'center',
              justifyContent: 'space-between',
              overflow: 'hidden',
              flexDirection: 'row',
              marginBottom: 20,
              flexWrap: 'wrap',
              // borderWidth: 1,
              // borderColor: Red,
              // paddingHorizontal: 5,
              borderBottomRightRadius: 5,
              borderBottomLeftRadius: 5,
            }}>

              {/* <Animated.View style={[styles.content, { height: animationHeight }]}>
              <View style={styles.innerContent}> */}

              {sheepImages.map((item, index) => {
                return (
                  <Pressable
                    onPress={() => { setVisible(!visible); console.log('asd'); }}
                    style={{
                      width: '32%',
                      height: screenHeight / 10,
                      alignItems: 'center',
                      justifyContent: 'center',
                      overflow: 'hidden',
                      marginBottom: 4,
                      borderRadius: 15, borderWidth: 1,
                      borderColor: MediumGrey,
                    }}>
                    <Image source={{ uri: item }} style={{ width: '100%', height: '100%', resizeMode: 'stretch', }} />
                  </Pressable>
                )
              })}
              {/* </View> */}
            </Animated.View>
            :
            <></>
          }
          {props.order.user &&
            <View style={styles.detailsContainer}>
              <View style={styles.viewTextContainer}>
                <Text style={styles.blackText}>{strings('lang.client')}</Text>
              </View>
              <View style={styles.viewTextContainer2}>
                <Text style={styles.greyText}>{props.order.user.name}</Text>
              </View>
            </View>
          }

          {props.order.address &&
            <View style={styles.detailsContainer}>
              <View style={styles.viewTextContainer}>
                <Text style={styles.blackText}>{strings('lang.addressD')}</Text>
              </View>
              <View style={styles.viewTextContainer2}>
                <Text style={styles.greyText}>{props.order.address.address}</Text>
              </View>
            </View>
          }

          {props.order &&
            <View style={styles.detailsContainer}>
              <View style={styles.viewTextContainer}>
                <Text style={styles.blackText}>{strings('lang.OrderNumber')}</Text>
              </View>
              <View style={styles.viewTextContainer2}>
                <Text style={styles.greyText}>{props.order.id}</Text>
              </View>
            </View>
          }

          {props.order.sheep && props.order.sheep[0] &&
            props.order.sheep[0].sheep &&
            props.order.sheep[0].sheep.sharing_type != null
            ?
            props.order.sheep[0].sheep.sharing_type.count == 4
              ?
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{strings('lang.Numberofsheep')}</Text>
                </View>
                <View style={styles.viewTextContainer2}>
                  <Text style={styles.greyText}>{strings('lang.Aquarterofasacrifice')}</Text>
                </View>
              </View>
              :
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{strings('lang.Numberofsheep')}</Text>
                </View>
                <View style={styles.viewTextContainer2}>
                  <Text style={styles.greyText}>{strings('lang.Halfasacrifice')}</Text>
                </View>
              </View>
            :
            <View style={styles.detailsContainer}>
              <View style={styles.viewTextContainer}>
                <Text style={styles.blackText}>{strings('lang.Numberofsheep')}</Text>
              </View>
              <View style={styles.viewTextContainer2}>
                <Text style={styles.greyText}>{props.order.sheep.length}</Text>
              </View>
            </View>
          }

          {props.order.sheep && props.order.sheep.map((item) => {
            return (
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{strings('lang.referencenumber')}</Text>
                </View>
                <View style={styles.viewTextContainer2}>
                  <Text style={styles.greyText}>{item.sheep.id}</Text>
                </View>
              </View>

            )
          })}

          {props.order.sheep && props.order.sheep.map((item) => {
            return (
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{strings('lang.collarcolor')}</Text>
                </View>
                <View style={[styles.viewTextContainer2, {}]}>
                  <Text style={[styles.greyText, { color: item.sheep.collar_color }]}>{item.sheep.collar_color_label}</Text>
                </View>
              </View>

            )
          })}

          {props.order.sheep && props.order.sheep.map((item) => {
            return (
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{strings('lang.Trader')}</Text>
                </View>
                <View style={styles.viewTextContainer2}>
                  <Text style={styles.greyText}>{item.sheep.farm.trader.name}</Text>
                </View>
              </View>

            )
          })}


          {props.order.sheep && props.order.sheep.map((item) => {
            return (
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{strings('lang.typeofsheep')}</Text>
                </View>
                <View style={styles.viewTextContainer2}>
                  <Text style={styles.greyText}>{item.sheep.sheep_category.name}</Text>
                </View>
              </View>

            )
          })}

          {props.order.user_options && props.order.user_options.map((item, index) => {
            return (
              <View style={styles.detailsContainer}>
                <View style={styles.viewTextContainer}>
                  <Text style={styles.blackText}>{item.type == "CuttingOption" ?
                    strings('lang.chopping')
                    :
                    item.type == "PreparingOption"
                      ?
                      strings('lang.processing')
                      :
                      strings('lang.head')
                  }</Text>
                </View>
                <View style={styles.viewTextContainer2}>
                  <Text style={styles.greyText}>{item.name}</Text>
                </View>
              </View>
            )
          })}

          {props.order.sheep && props.order.sheep[0] &&
            <View style={styles.detailsContainer}>
              <View style={styles.viewTextContainer}>
                <Text style={styles.blackText}>{strings('lang.Weight')}</Text>
              </View>
              <View style={styles.viewTextContainer2}>
                <Text style={styles.greyText}>{`${props.order.sheep[0].final_weight} ${strings('lang.kg')} `}</Text>
              </View>
            </View>

          }


        </ScrollView>
        <Button style={styles.modalbuttonContainer2} onPress={props.backPress}>
          <Text style={styles.modalbuttonText2}>{strings('lang.back')}</Text>
        </Button>
      </View>
      <Modal visible={visible} transparent={true}>
        {/* <Button transparent onPress={() => { setVisible(false); }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
          <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
        </Button> */}
        <View style={{
          flex: 1,
          width: screenWidth,
          height: screenHeight,
          backgroundColor: White,
          justifyContent: 'center',
          alignItems: 'center'
        }}>

          <Button transparent onPress={() => { setVisible(false); }} style={{ width: '20%', height: '15%', zIndex: 1000, justifyContent: "center", alignItems: 'center', position: 'absolute', start: 0, top: 0, }} >
            <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={{ width: '70%', height: '70%', resizeMode: 'contain', tintColor: Black }} />
          </Button>
          <FlatList
            data={sheepImages}
            keyExtractor={(item) => item.id}
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            // initialScrollIndex={selectedIndex}
            style={{ width: screenWidth, height: '100%' }}
            getItemLayout={(data, index) => ({
              length: screenWidth, // Width of each image
              offset: screenWidth * index,
              index,
            })}
            renderItem={({ item }) => (
              <Image source={{ uri: item }} style={{
                width: screenWidth,
                height: screenHeight,
                resizeMode: 'contain'
                // marginHorizontal: 10,
                // borderRadius: 10,
              }} />
            )}
          />
        </View>

        {/* <>
            <ImageViewer
              // onChange={(imageIndex) => { setSheepImages(sheepImages[imageIndex].url) }} 
              enableSwipeDown={false} onSwipeDown={() => { setVisible(false); }} backgroundColor='white' style={{ backgroundColor: 'White', height: '100%', borderRadius: 10, overflow: 'hidden' }} imageUrls={sheepImages} />
            <ScrollView horizontal={true} contentContainerStyle={{ flexGrow: 1 }} showsHorizontalScrollIndicator={false} style={{ position: 'absolute', bottom: 0, height: '24%', maxWidth: '90%', alignSelf: 'center', zIndex: 100, padding: 5 }} >
              {sheepImages.map((item, index) => {
                return (
                  <Button
                    // transparent onPress={() => { console.log('1'); setSheepImages(sheepImages[index].url) }}
                    style={styles.imageActive}
                  >
                    <Image source={{ uri: item.url }} style={{ width: '100%', height: '100%', resizeMode: 'cover', borderRadius: 5 }} />
                  </Button>
                )
              })}
            </ScrollView>
          </> */}

      </Modal>
      {/* </Modal> */}



    </View>
  );
}

export default OrderDetailsModal;

const styles = StyleSheet.create({
  detailsContainer: {
    with: '90%',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  viewTextContainer: {
    width: '30%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    // backgroundColor: Red,
    paddingVertical: 5

  },
  viewTextContainer2: {
    width: '70%',
    alignItems: 'flex-start',
    justifyContent: 'center',
    // backgroundColor: Red1,
    paddingVertical: 5

  },
  blackText: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: Black
  },
  greyText: {
    fontFamily: appFontBold,
    fontSize: screenWidth / 30,
    color: DarkGrey,
    textAlign: I18nManager.isRTL ? 'left' : 'right'
  },
  modal2: {
    flex: 1,
    alignItems: 'center',
    width: screenWidth,
    height: screenHeight,
    alignSelf: 'center',
    backgroundColor: White,
    borderRadius: 10,
    paddingVertical: '4%',
    justifyContent: 'flex-start',
    paddingHorizontal: '5%'
  },
  modalbuttonContainer2: {
    width: screenWidth / 1.3,
    height: screenHeight / 15,
    backgroundColor: Red1,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    marginTop: 7,
  },
  modalbuttonText2: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28
  },
  tileContainer: {
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    backgroundColor: '#f9f9f9',
  },
  content: {
    overflow: 'hidden',
  },
  innerContent: {
    padding: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
    overflow: 'hidden',
    flexDirection: 'row',
    marginBottom: 20,
    flexWrap: 'wrap',
    width: '100%',
    maxHeight: screenHeight,
  },
  tileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 15,
    // backgroundColor: Red,
    alignItems: 'center',
  },
  titleText: {
    fontSize: screenWidth / 25,
    fontFamily: appFontBold,
    color: Black
  },
  arrow: {
    fontSize: screenWidth / 25,
    fontFamily: appFontBold,
    color: Black
  },
});

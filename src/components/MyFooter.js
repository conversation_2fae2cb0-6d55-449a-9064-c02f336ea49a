import { Button } from "native-base";
import React, { useEffect, useState } from "react";
import {
  Text,
  StyleSheet,
  View,
  Image,
  TouchableHighlight,
  Platform,
  ImageBackground,
  Pressable,
} from "react-native";
import { TouchableOpacity } from "react-native-gesture-handler";
import Svg, { Circle, Path, } from "react-native-svg";
import { strings } from "../screens/i18n";
import { Red, appColor2, appFontBold, DarkBlue, DarkGrey, MediumGrey, screenHeight, screenWidth, White, Red1 } from "./Styles";
import Toaster from "./Toaster";
import AsyncStorage from "@react-native-async-storage/async-storage";

const MyFooter = props => {
  const [pathX, setPathX] = useState("357");
  const [pathY, setPathY] = useState("675");
  const [pathA, setPathA] = useState("689");
  const [pathB, setPathB] = useState("706");

  const [Token, setToken] = useState('');

  useEffect(() => {

    const token = async () => {
      let token = await AsyncStorage.getItem('token')
      setToken(token)
    }
    token()
  }, []);
  const skipLogin = () => {
    Toaster(
      'top',
      'warning',
      Red1,
      strings('lang.login_continuou'),
      White,
      1500,
      screenHeight / 50,
    );
    props.navigation.navigate('Login')
  }
  return (
    <View style={{ height: screenHeight / 9, width: screenWidth, position: 'absolute', bottom: Platform.OS == 'ios' ? -screenHeight / 100 : 0, marginTop: 5, zIndex: 10 }}>

      <ImageBackground source={require('../images/modrek/Footer.png')} style={{ width: screenWidth * 1.3, paddingHorizontal: screenWidth / 6.6, height: '100%', resizeMode: 'cover', alignSelf: 'center', justifyContent: 'flex-end', }} >



        <View style={{ width: screenWidth, height: screenHeight / 11.5, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <View style={styles.halfFooterContainer}>
            <Pressable onPress={() => { props.navigation.push('Home', { weightId: null, sheepCatId: null, link: '' }) }} style={styles.quarterFooterContainer}>
              <Image style={props.current == 'Home' ? { ...styles.icon, tintColor: Red } : styles.icon} source={require('../images/modrek/svgexport-1.png')} />
              <Text numberOfLines={1} style={props.current == 'Home' ? { ...styles.text, color: Red } : styles.text}>{strings('lang.Home')}</Text>
            </Pressable>
            <Pressable onPress={() => {
              // if (props.token) {
              if (Token) {
                props.navigation.navigate('KnowMore')
              } else {
                skipLogin()
              }
            }} style={styles.quarterFooterContainer}>
              <Image style={props.current == 'KnowMore' ? { ...styles.icon, tintColor: Red } : styles.icon} source={require('../images/modrek/svgexport-72.png')} />
              <Text numberOfLines={1} style={props.current == 'KnowMore' ? { ...styles.text, color: Red } : styles.text}>{strings('lang.knowmore')}</Text>
            </Pressable>
          </View>
          <Button onPress={() => { props.navigation.navigate('MyOrders') }} style={{ width: screenHeight / 16, overflow: 'hidden', height: screenHeight / 16, flexDirection: 'column', borderRadius: screenHeight / 30, backgroundColor: Red, zIndex: 100, alignSelf: 'flex-start', alignItems: 'center', justifyContent: 'center' }}>
            <Image style={{ width: '40%', height: '40%', resizeMode: 'contain', tintColor: White }} source={require('../images/modrek/svgexport-187.png')} />
            <Text numberOfLines={1} style={{ fontFamily: appFontBold, fontSize: screenWidth / 50, color: White, zIndex: 100, }}>{strings('lang.myorders')}</Text>
          </Button>
          <View style={styles.halfFooterContainer}>
            <Pressable onPress={() => {
              // if (props.token) {
              if (Token) {
                props.navigation.navigate('Favourites')
              } else {
                skipLogin()
              }
            }} style={styles.quarterFooterContainer}>
              <Image style={props.current == 'Favourites' ? { ...styles.icon, tintColor: Red } : styles.icon} source={require('../images/modrek/book.png')} />
              <Text numberOfLines={1} style={props.current == 'Favourites' ? { ...styles.text, color: Red } : styles.text}>{strings('lang.Favourites')}</Text>
            </Pressable>

            <Pressable onPress={() => { props.navigation.navigate('More') }} style={styles.quarterFooterContainer}>
              <Image style={props.current == 'More' ? { ...styles.icon, tintColor: Red } : styles.icon} source={require('../images/more.png')} />
              <Text numberOfLines={1} style={props.current == 'More' ? { ...styles.text, color: Red } : styles.text}>{strings('lang.More')}</Text>
            </Pressable>
          </View>
        </View>
      </ImageBackground>

    </View>
  );
}
export default MyFooter;

const styles = StyleSheet.create({
  content: {
    flexDirection: "column",
    zIndex: 0,
    width: screenWidth,
    height: screenHeight / 10,
    marginBottom: Platform.OS == 'ios' ? "3%" : 0,
    position: "absolute",
    bottom: "0%",
    elevation: 10,
    // backgroundColor:'red'
  },
  subContent: {
    flexDirection: "row",
    marginLeft: 15,
    marginRight: 15,
    // marginBottom: 10,
    zIndex: 1,
    position: "absolute",
    bottom: '-10%',
    justifyContent: 'space-between'
  },
  icon: {
    width: 25,
    height: 25, resizeMode: "contain",
    tintColor: DarkGrey
  },
  activeIcon: {
    width: 25,
    height: 25, resizeMode: "contain",
    tintColor: Red
  },
  text: {
    color: DarkGrey,
    fontFamily: appFontBold,
    fontSize: screenWidth / 40,
    textAlign: 'center'
  },
  activeText: {
    color: Red,
    fontFamily: appFontBold,
    fontSize: screenWidth / 40,
  },
  halfFooterContainer: { width: '40%', height: '100%', flexDirection: 'row', justifyContent: 'space-between', },
  quarterFooterContainer: { width: screenWidth / 5, height: '100%', alignItems: 'center', padding: 5, },
  icon: { width: 25, height: 25, resizeMode: 'contain', tintColor: MediumGrey, zIndex: 100, },
  text: { fontFamily: appFontBold, fontSize: screenWidth / 35, color: MediumGrey, zIndex: 100, marginTop: 2 },

});
import React, { useEffect, useRef, useState } from 'react';
import { View, Text, Animated, StyleSheet, Platform } from 'react-native';
import { Black, Red } from './Styles';

const LoadingBar = ({ duration }) => {
    const animation = useRef(new Animated.Value(0)).current;

    useEffect(() => {
        Animated.timing(animation, {
            toValue: 1,
            duration: duration, // in milliseconds
            useNativeDriver: Platform.OS === 'android', // Use native driver on Android for better performance
        }).start(() => {

        });
    }, [animation, duration,]);

    const widthInterpolation = animation.interpolate({
        inputRange: [0, 1],
        outputRange: ['0%', '100%'],
    });


    return (
        <View style={styles.container}>
            <Animated.View style={[styles.loadingBar, { width: widthInterpolation, }]} />
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        width: '100%',
        height: 20,
        backgroundColor: '#e0e0e0',
        borderRadius: 10,
        overflow: 'hidden',

    },
    loadingBar: {
        height: '100%',
        backgroundColor: Red,
        borderRadius: 10,
    },
});

export default LoadingBar;
import { View } from 'native-base';
import React from 'react';
import { Dimensions, ActivityIndicator } from 'react-native';
import { appColor1, DarkBlue, DarkGreen, DarkGrey, Green, MediumGrey, Red, screenHeight, screenWidth, White, } from '../components/Styles';

const { height } = Dimensions.get('window');

const LoadingMore = () => (
  <View style={{ backgroundColor: MediumGrey, opacity: 0.6, position: "absolute", zIndex: 1000000, alignSelf: 'center', alignItems: 'center', width: screenWidth, height: 2 * screenHeight, }}>
    <ActivityIndicator size={50} color={Red} style={{ marginTop: screenHeight / 2 }} />
  </View>
);
export default LoadingMore;
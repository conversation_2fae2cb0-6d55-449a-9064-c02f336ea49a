import React, { useState } from 'react';
import { View, StyleSheet, Image, Text, I18nManager, Modal, Platform, Pressable } from "react-native";
import { TextInput, TouchableOpacity } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, Green, mSize, Blue, Red, Black, MediumGrey, Gold, WhiteGery, DarkGrey } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button, Input, Left, Right } from "native-base";
import { strings } from '../screens/i18n';

const HomeHeader = props => {

    const [name, setName] = useState('');
    const [search, setsearch] = useState('');



    return (
        <View style={styles.headercontainer}>
            <View style={{ width: '11%', alignItems: 'center', justifyContent: 'center', paddingStart: '3%' }}>
                <View transparent style={styles.imgContainer} >
                    <Image source={require('../images/modrek/image1.png')} style={styles.backImage} />
                </View>
            </View>

            <View style={{ width: '60%', justifyContent: 'center', }}>
                <View style={{ width: '90%', overflow: 'hidden', alignSelf: "center", borderColor: MediumGrey, backgroundColor: WhiteGery, borderWidth: 0, height: '60%', borderRadius: 15, flexDirection: 'row', paddingHorizontal: 5 }}>
                    <Pressable onPress={props.searchPress} style={{ height: '100%', width: screenWidth / 1.4, flexDirection: "row", alignItems: "center" }}>
                        <Image source={require('../images/Silal/search.png')} style={{ width: '5%', height: '50%', resizeMode: "contain", alignSelf: "center", marginHorizontal: '1%', tintColor: MediumGrey }} />
                        <View style={{ width: '65%', overflow: 'hidden' }}>
                            <Input
                                style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: DarkGrey, overflow: 'hidden', textAlign: I18nManager.isRTL ? 'right' : 'left' }}
                                placeholderTextColor={'#707070'} placeholder={(strings('lang.Searchbybarncode'))}
                            />
                        </View>
                    </Pressable>
                </View>
            </View>

            <View style={{ flexDirection: 'row', width: '25%', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: '1%', height: '100%', }}>
                <Pressable onPress={props.cartPress} style={styles.backButton} >
                    <Image source={require('../images/modrek/33333.png')} style={{ width: "70%", height: "70%", resizeMode: "contain", alignSelf: 'center' }} />
                </Pressable>
                <Pressable onPress={props.notificationPress} style={styles.backButton} >
                    <Image source={require('../images/modrek/notif.png')} style={{ resizeMode: "contain", width: '50%', height: '50%', alignSelf: 'center' }} />
                </Pressable>
                {/* <Image source={require('../images/modrek/notif.png')} style={{ resizeMode: "contain", width: '30%', height: '45%', tintColor: Red, alignSelf: 'center' }} /> */}
                <Pressable onPress={props.profilePress} style={styles.backButton} >
                    <Image source={require('../images/modrek/Group10038.png')} style={{ resizeMode: "contain", width: '100%', height: '100%', alignSelf: 'center' }} />
                </Pressable>
                {/* <Image source={require('../images/modrek/Group10038.png')} style={{ resizeMode: "contain", width: '38%', height: '100%', alignSelf: 'center' }} /> */}
            </View>

        </View>
    )



};
export default HomeHeader;
const styles = StyleSheet.create({

    headercontainer: {
        backgroundColor: White, height: screenHeight / 15, alignSelf: 'flex-start', alignItems: 'center', flexDirection: "row", width: '100%', marginTop: Platform.OS == 'ios' ? screenHeight / 18 : 10
    },


    title: {
        fontFamily: appFontBold, color: Black, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    title2: {
        fontFamily: appFontBold, color: Red, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    imgContainer: {
        width: '100%', height: '100%', justifyContent: "center"
    },
    backButton: {
        width: '33%', height: '100%', justifyContent: "center",
    },
    backImage: {
        width: "80%", height: "50%", resizeMode: "contain", marginBottom: '20%'
    },
    drawerImage: {
        width: "90%", height: "70%", resizeMode: "contain", alignSelf: 'center'
    }
});



import React, { Component } from 'react';
import { View, StyleSheet, Image, Text, I18nManager } from "react-native";
import { TouchableOpacity } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, Green, mSize, Blue } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button, Left, Right } from "native-base";
export default class ProfileHeader extends Component {


    constructor(props) {
        super(props)
        this.state = {
            lan: '',
        }
    }

    async componentDidMount() {
        let lan = await AsyncStorage.getItem('lan')
        this.setState({ lan: lan })
    }

    render() {
        return (
            <View style={styles.headercontainer}>
                <View style={{flexDirection:'row',width:'100%',height:'60%'}}>
                <Left>
                    {this.props.back ?
                    <Button transparent onPress={this.props.backPress} style={styles.backButton} >
                    <Image source={I18nManager.isRTL ?  require('../images/surface-900.png') : require('../images/surface-891.png')} style={styles.backImage} />
                </Button>
                :
                null
                  }
                
                </Left>
                <Text style={styles.title}>{this.props.title}</Text>
                <Right></Right>
                </View>
            </View>
        )

    }

};

const styles = StyleSheet.create({

    headercontainer: {
        backgroundColor: Blue, height: screenHeight / 5, justifyContent: "center",flexDirection:"row",borderBottomRightRadius:30,borderBottomLeftRadius:30
    },


    title: {
        textAlign: "center", fontFamily: appFontBold, color: '#fff', fontSize: mSize,textAlignVertical:"center"
    },

    backButton:{
        width:60,height:40,alignSelf:"flex-start",justifyContent:"center"
    },
    backImage:{
        width:"100%",height:"100%",tintColor:"#fff",resizeMode:"contain"
    }
});



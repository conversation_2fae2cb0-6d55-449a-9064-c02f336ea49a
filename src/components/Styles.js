import { Dimensions, Platform, StatusBar, StyleSheet } from "react-native";

const STATUSBAR_HEIGHT = Platform.OS === 'ios' ? 20 : StatusBar.currentHeight;
export const { height: screenHeight, width: screenWidth } = Dimensions.get('window');
// export const appFontBold = 'Cairo-Bold'
// export const appFontMedium = 'Cairo-Medium'
// export const appFont = 'Cairo-Regular'
// export const appFontBold = 'Cairo-Bold'
export const appFontBold = 'Cairo-Bold'
export const appFontNawar = 'Nawar Font'
export const appFontTajawal = 'Tajawal-Bold'
export const appFontMedium = 'Cairo-Bold'
export const appFont = 'Cairo-Bold'
export const mSize = screenWidth / 22



export const WhiteGery = '#F3F3F3'
export const MediumGrey = '#cecdcd'
// export const DarkGrey = '#707070'
export const DarkGrey = '#656565'
export const lightGrey = '#D0D0D0'

export const WhiteYellow = '#FFE88F'
export const MediumYellow = '#ffc107'
export const DarkYellow = '#f0c91f'
export const Orange = '#ff8502'

export const White = '#ffffff'
export const Black = '#000000'

export const LighttGreen = '#E6F2DF'
export const LightGreen = '#D3EAE2'
export const WhiteGreen = '#a8d6c7'
export const DarkGreen = '#006442'

// export const Red = '#539DB3'204452
// export const Red = '#204452'
export const Red = '#123340'
// export const Red = '#007788'
// export const Red1 = '#c20404'
export const Red1 = '#680E0E'
export const WhiteRed = '#f8e3e3'
export const Gold = '#efc75e'
export const DarkRed = '#C41D24'

export const WhiteBlue = '#CBF0E4'
export const MediumBlue = '#daeef2'
export const Blue = '#1db5d0'
export const DarkBlue = '#283897'

export const appColor1 = '#8dc63f'
export const appColor2 = '#00aeef'
export const appColor3 = '#E5B44E';
export const babyBlue = '#89CFF0'




const styles = StyleSheet.create({
  statusBar: {
    height: STATUSBAR_HEIGHT / 10
  },
})


export default styles

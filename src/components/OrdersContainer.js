import React, { useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Platform } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1, appColor2, Red1, DarkYellow } from './Styles';
import { strings } from '../screens/i18n';
import { Button } from 'native-base';
import LoadingMoreSmall from './LoadingMoreSmall';
import StarRating from 'react-native-star-rating';
import MapView from 'react-native-maps';

const OrdersContainer = props => {

    const [fav, setFav] = useState(false)


    return (
        <View style={{ flexDirection: 'row', width: '95%', maxHeight: screenHeight / 6, borderColor: MediumG<PERSON>, overflow: 'hidden', borderWidth: 1, justifyContent: 'space-between', borderRadius: 10, alignItems: "flex-start", alignSelf: 'center', marginVertical: '2%' }}>
            <View style={{ flexDirection: 'row', alignItems: 'flex-start', marginVertical: '2%', width: '65%', }}>
                <View style={styles.textsContainer}>
                    <Text numberOfLines={1} style={styles.text}>{strings('lang.OrderNumber')}</Text>
                    <Text numberOfLines={1} style={styles.text}>{strings('lang.Date')}</Text>
                    <Text numberOfLines={1} style={styles.text}>{strings('lang.Status')}</Text>
                </View>
                <View style={styles.textsContainer1}>
                    <Text numberOfLines={1} style={styles.text}>{props.item.id}</Text>
                    <Text numberOfLines={1} style={styles.text}>{props.item.created_at_formatted}</Text>
                    <Text style={styles.text}>{props.item.statuses[0] ? props.item.statuses[((props.item.statuses).length - 1)].name : ''}</Text>
                    {/* <Text style={styles.text}>{props.item.statuses[0] && props.item.statuses[0].name}</Text> */}
                </View>
            </View>

            <Button onPress={props.TrackingPress} style={{ marginEnd: '2%', width: '33%', alignSelf: "center", height: 35, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                <Image source={require('../images/modrek/eyee.png')} style={{ tintColor: White, resizeMode: 'contain', width: '12%', height: '100%', marginEnd: 5 }} />
                <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.thedetails')}</Text>
            </Button>
        </View>
    )
};

export default OrdersContainer;
const styles = StyleSheet.create({
    textsContainer: {
        marginStart: '2%',
        width: '25%',
    },
    textsContainer1: {
        marginStart: '2%',
        width: '71%',
    },
    text: { fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' },

});





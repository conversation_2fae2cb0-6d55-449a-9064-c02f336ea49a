import React, { } from 'react';
import { View, StyleSheet, Image, Text, I18nManager, Modal, Platform } from "react-native";
import { TouchableOpacity } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, Green, mSize, Blue, Red, Black, DarkGrey, MediumGrey } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Button, Left, Right } from "native-base";
import { strings } from '../screens/i18n';
const Header = props => {


    return (
        <View style={{ backgroundColor: Red, height: screenHeight / 10, justifyContent: "center", flexDirection: "row", paddingHorizontal: 10, width: '100%', paddingTop: Platform.OS == 'ios' ? screenHeight / 30 : 0, marginBottom: props.noMargin ? 0 : 5 }}>
            {/* <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center',backgroundColor:'red'}}> */}
            <Button transparent onPress={props.backPress} style={{ width: '15%', height: '100%', justifyContent: "center", }} >
                <Image source={I18nManager.isRTL ? require('../images/modrek/arrow.png') : require('../images/modrek/arrow.png')} style={styles.backImage} />
            </Button>
            {/* </View> */}

            <View style={{ width: '70%', justifyContent: 'center', paddingStart: '3%', }}>
                <Text style={styles.title}>{props.title}</Text>
            </View>
            {/* <View style={{ width: '20%', alignItems: 'center', justifyContent: 'center' ,backgroundColor:'green'}}>
                    {this.props.cancel &&
                        <Button transparent onPress={this.props.cancelPress} style={styles.backButton} >
                            <Text style={styles.title2}>{strings('lang.Cancel')}</Text>
                        </Button>
                    }
                </View> */}
            {/* {props.cart ?
                <Button transparent onPress={props.cartPress} style={{ width: '15%', alignItems: 'center', justifyContent: 'center', height: '100%', backgroundColor: Red }}>
                    <Image source={require('../images/modrek/33333.png')} style={{ width: "50%", height: "80%", resizeMode: "contain", tintColor: White }} />
                   
                </Button>
                :
                <View style={{ width: '15%', alignItems: 'center', justifyContent: 'center', height: '100%', }}>
                </View>
            } */}
        </View>
    )
};
export default Header;
const styles = StyleSheet.create({

    headercontainer: {
        backgroundColor: Red, height: screenHeight / 10, justifyContent: "center", flexDirection: "row", paddingHorizontal: 10, width: '100%', paddingTop: Platform.OS == 'ios' ? screenHeight / 30 : 0, marginBottom: 5
    },
    title: {
        fontFamily: appFontBold, color: White, fontSize: screenWidth / 20, textAlignVertical: "center", alignSelf: 'flex-start'
    },
    title2: {
        fontFamily: appFontBold, color: White, fontSize: screenWidth / 25, textAlignVertical: "center", alignSelf: 'flex-start'
    },
    backButton: {
        width: '15%', height: '100%', justifyContent: "center",
    },
    backImage: {
        width: "50%", height: "70%", tintColor: White, resizeMode: "contain", alignSelf: 'center', marginStart: 5, transform: [{ scaleX: I18nManager.isRTL ? 1 : -1 }]
    },
    drawerImage: {
        width: "80%", height: "80%", resizeMode: "contain"
    }
});



import React, { useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Platform, Pressable } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, <PERSON>, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1, appColor2, Red1, DarkYellow } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { strings } from '../screens/i18n';
import { Button } from 'native-base';
import LoadingMoreSmall from './LoadingMoreSmall';
import StarRating from 'react-native-star-rating';
import * as farmesActions from '../../Store/Actions/farms';
import { useDispatch } from 'react-redux';

const FarmContainer2 = props => {

    const [fav, setFav] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();

    const toggleFav = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(farmesActions.farmToggleFavorite(props.item.id));
            if (response.success == true) {
                setFav(response.data.is_favorited)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
        }
        catch (err) {
            setLoadingMore(false)
            console.log('err', err)
        }
    };
    return (
        <Pressable onPress={props.livePress} style={{ height: screenHeight / 9, overflow: 'hidden', flexWrap: 'wrap', marginVertical: '4%', backgroundColor: White, alignSelf: "center", marginTop: 0, width: '95%', flexDirection: 'row', justifyContent: 'space-between' }}>
            <Pressable onPress={props.livePress} style={{ width: '30%', height: '100%', borderRadius: 10, overflow: 'hidden', borderColor: MediumGrey, borderWidth: 0.5, }}>
                {props.item.image &&
                    <Image source={{ uri: props.item.image }} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
                }
                <Image source={require('../images/modrek/livee.png')} style={{ width: screenWidth / 12, height: screenHeight / 20, zIndex: 1000, resizeMode: 'contain', position: 'absolute', right: 5, top: -6 }} />
            </Pressable>
            {loadingMore ? <LoadingMoreSmall style={{}} /> : <View></View>}

            <View style={{ width: '68%', height: '100%', justifyContent: 'center' }}>
                <View style={{ width: '100%', height: '30%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Pressable onPress={props.livePress} style={{ flexDirection: 'row', alignItems: 'center', }}>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '1%', color: Black }}>{props.item.code}</Text>
                    </Pressable>
                </View>
                <View style={{ width: '100%', height: '30%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                        <Pressable onPress={props.detailsPress} style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Image source={require('../images/modrek/svgexport-133.png')} style={{ width: screenWidth / 25, resizeMode: 'contain', height: screenHeight / 30, marginEnd: '2%', }} />
                            <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 35, color: DarkGrey }}>{props.item.rating}</Text>
                        </Pressable>
                        <View style={{ width: 1, height: screenHeight / 50, backgroundColor: MediumGrey, marginHorizontal: '2%' }}></View>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Red }}>{`${props.item.price} ` + strings('lang.SAR')}</Text>
                        <View style={{ width: 1, height: screenHeight / 50, backgroundColor: MediumGrey, marginHorizontal: '2%' }}></View>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Red }}>{`${props.item.sheep_category.name}`}</Text>
                        <View style={{ width: 1, height: screenHeight / 50, backgroundColor: MediumGrey, marginHorizontal: '2%' }}></View>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Red }}>{`${props.item.weight.from} ` + strings('lang.kg') + ` ${props.item.weight.to} ` + strings('lang.kg')}</Text>
                    </View>
                </View>
                <View style={{ width: '100%', height: '40%', flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Pressable onPress={props.detailsPress} transparent style={{ marginEnd: '5%', backgroundColor: Red, height: screenHeight / 35, width: screenHeight / 35, overflow: 'hidden', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', }}>
                        <Image source={require('../images/modrek/details.png')} style={{ width: '40%', resizeMode: 'contain', height: screenHeight / 30, }} />
                    </Pressable>
                    <Pressable onPress={() => { toggleFav() }} transparent style={{ marginEnd: '5%', height: screenHeight / 35, width: screenHeight / 35, overflow: 'hidden', alignItems: 'center', justifyContent: 'center', }}>
                        <Image source={fav ? require('../images/modrek/bookk.png') : require('../images/modrek/book.png')} style={{ resizeMode: 'contain', alignSelf: 'center', width: '100%', height: '100%', tintColor: fav ? DarkYellow : DarkGrey }} />
                    </Pressable>
                </View>
            </View>
        </Pressable>
    )
};

export default FarmContainer2;
const styles = StyleSheet.create({


});





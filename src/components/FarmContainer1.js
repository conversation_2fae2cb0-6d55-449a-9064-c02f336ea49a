import React, { useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Platform, Pressable } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, <PERSON>, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1, appColor2, Red1, DarkYellow } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { strings } from '../screens/i18n';
import { Button } from 'native-base';
import LoadingMoreSmall from './LoadingMoreSmall';
import StarRating from 'react-native-star-rating';
import * as farmesActions from '../../Store/Actions/farms';
import { useDispatch } from 'react-redux';

const FarmContainer1 = props => {

    const [fav, setFav] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadingMore, setLoadingMore] = useState(false);
    const dispatch = useDispatch();

    const toggleFav = async () => {
        setLoadingMore(true)
        try {
            let response = await dispatch(farmesActions.farmToggleFavorite(props.item.id));
            if (response.success == true) {
                setFav(response.data.is_favorited)
            } else {
                if (response.message) {
                    Toaster(
                        'top',
                        'danger',
                        Red,
                        response.message,
                        White,
                        1500,
                        screenHeight / 15,
                    );
                }
            }
            setLoadingMore(false)
        }
        catch (err) {
            setLoadingMore(false)
            console.log('err', err)
        }
    };


    return (
        <Pressable onPress={props.detailsPress} style={{ height: screenHeight / 8, overflow: 'hidden', flexWrap: 'wrap', marginBottom: '1%', backgroundColor: props.farmId == props.item.id ? WhiteGery : White, alignSelf: "center", paddingVertical: 0, width: '100%', flexDirection: 'row', justifyContent: 'flex-start', paddingHorizontal: '2.5%' }}>
            <View style={{ width: '30%', height: '85%', borderRadius: 10, overflow: 'hidden', alignSelf: 'center', marginStart: '1%', borderColor: MediumGrey, borderWidth: 0.5 }}>
                {props.item.image &&
                    // <Image source={require('../images/modrek/sheepp.jpeg')} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
                    <Image source={{ uri: props.item.image }} style={{ width: '100%', resizeMode: 'cover', height: '100%' }} />
                }
                <Image source={require('../images/modrek/livee.png')} style={{ width: screenWidth / 12, height: screenHeight / 20, zIndex: 1000, resizeMode: 'contain', position: 'absolute', right: 6, top: -6 }} />
            </View>
            {loadingMore ? <LoadingMoreSmall style={{}} /> : <View></View>}
            {/* svgexport612 */}

            <View style={{ width: '60%', height: '100%', alignItems: 'flex-start', }}>
                <View style={{ width: '99%', height: '25%', flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-end' }}>
                    {/* <Image source={require('../images/modrek/userrr.png')} style={{ width: '10%', resizeMode: 'contain', height: '100%', tintColor: Red1, marginEnd: '1%', }} /> */}
                    {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '1%', color: Black }}>{strings('lang.Trader') + ' ' + props.item.trader.name}</Text> */}
                    {/* <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '1%', color: Black }}>{'اصل الجودة - 1'}</Text> */}
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, marginHorizontal: '1%', color: Black }}>{props.item.code}</Text>
                </View>
                <View style={{ width: '98%', height: '35%', flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-end' }}>
                    <Image source={require('../images/modrek/svgexport-133.png')} style={{ width: '8%', resizeMode: 'contain', height: '100%', marginEnd: '2%', }} />
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 35, color: DarkGrey }}>{props.item.rating}</Text>
                    <View style={{ width: 1, height: '50%', backgroundColor: MediumGrey, marginHorizontal: '5%' }}></View>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 30, color: Red }}>{props.item.price} {strings('lang.SAR')}</Text>
                </View>
                <View style={{ width: '98%', height: '40%', flexDirection: 'row', alignItems: 'center', alignSelf: 'flex-end' }}>
                    <Pressable onPress={props.modalPress} transparent style={{ marginEnd: '5%', backgroundColor: Red, height: screenHeight / 35, width: screenHeight / 35, overflow: 'hidden', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', }}>
                        <Image source={require('../images/modrek/details.png')} style={{ width: '40%', resizeMode: 'contain', height: screenHeight / 30, }} />
                    </Pressable>
                </View>
            </View>

            {props.farmId == props.item.id
                ?
                <View style={{ width: '27%', height: '100%', justifyContent: 'center', alignItems: 'center', position: 'absolute', right: 0 }}>
                    <Pressable onPress={props.livePress} transparent style={{ backgroundColor: Red, height: screenHeight / 30, width: screenWidth / 4.5, overflow: 'hidden', borderRadius: screenHeight / 70, alignItems: 'center', justifyContent: 'center', }}>
                        <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 48, color: White, }}>{props.reservation ? strings('lang.MoveToAddareservation') : strings('lang.Gotobookingg')}</Text>
                    </Pressable>
                </View>
                :
                <View style={{ width: '30%', height: '100%', justifyContent: 'center', alignItems: 'center' }}>
                </View>

            }


        </Pressable>
    )
};

export default FarmContainer1;
const styles = StyleSheet.create({


});





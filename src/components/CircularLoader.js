import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import Svg, { Circle } from 'react-native-svg';

const CircularLoader = ({ size, strokeWidth, duration }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const [progressValue, setProgressValue] = useState(0);
    const animatedValue = new Animated.Value(0);

    const AnimatedCircle = Animated.createAnimatedComponent(Circle);

    useEffect(() => {
        Animated.timing(animatedValue, {
            toValue: 100,
            duration: duration,
            useNativeDriver: false,
        }).start();

        animatedValue.addListener((v) => {
            const progress = Math.round(v.value);
            setProgressValue(progress);
        });

        return () => {
            animatedValue;
        };
    }, [duration]);

    const strokeDashoffset = animatedValue.interpolate({
        inputRange: [0, 100],
        outputRange: [circumference, 0],
    });

    return (
        <View style={styles.container}>
            <Svg width={size} height={size}>
                <Circle
                    stroke="#e0e0e0"
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    strokeWidth={strokeWidth}
                />
                <AnimatedCircle
                    stroke="#4CAF50"
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    strokeWidth={strokeWidth}
                    strokeDasharray={circumference}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                />
            </Svg>
            <Text style={styles.percentageText}>{`${progressValue}%`}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    percentageText: {
        position: 'absolute',
        fontSize: 28,
        fontWeight: '600',
        color: '#4CAF50',
    },
});

export default CircularLoader;
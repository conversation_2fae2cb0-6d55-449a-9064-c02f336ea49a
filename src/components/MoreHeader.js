import React, {  } from 'react';
import { View, StyleSheet, Image, Text, I18nManager, Modal } from "react-native";
import { TouchableOpacity } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, Green, mSize, Blue, Red, Black, appColor2 } from './Styles';
import { Button, Left, Right } from "native-base";
import { strings } from '../screens/i18n';
const MoreHeader =props=> {

   
        return (
            <View style={styles.headercontainer}>
                <View style={{ width: '10%', alignItems: 'center', justifyContent: 'center' }}>
                    {/* {this.props.back ? */}
                    <Button transparent onPress={props.backPress} style={styles.backButton} >
                        <Image source={I18nManager.isRTL ? require('../images/arrow-right.png') : require('../images/arrow-right.png')} style={styles.backImage} />
                    </Button>
                </View>
                <View style={{ width: '70%',paddingStart: '1.5%',paddingTop:'5%' }}>
                        <Text style={styles.title}>{strings('lang.Profile')}</Text>
                </View>
                <View style={{ width: '20%', alignItems: 'center', justifyContent: 'center' }}>
                    
                </View>

            </View>
        )
    };

export default MoreHeader;
const styles = StyleSheet.create({

    headercontainer: {
        backgroundColor: Red, height: 80, justifyContent: "center", flexDirection: "row", paddingHorizontal: 10, width: '100%',marginBottom:'2%'
    },


    title: {
        fontFamily: appFontBold, color: White, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    title2: {
        fontFamily: appFontBold, color: Red, fontSize: screenWidth / 25, textAlignVertical: "center"
    },
    backButton: {
        width: '100%', height: '100%',justifyContent:"center",
    },
    backImage: {
        width: "40%", height: "40%", tintColor: White, resizeMode: "contain",alignSelf:"flex-start",marginTop:'18%'
    },
    drawerImage: {
        width: "50%", height: "50%", resizeMode: "contain"
    }
});



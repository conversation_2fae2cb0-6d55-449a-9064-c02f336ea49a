import React, { useEffect, useState } from 'react';
import { View, Text, Button, TouchableOpacity, StyleSheet } from 'react-native';
import { copilot, CopilotStep, walkthroughable } from 'react-native-copilot';
import { Red, WhiteGery, appFontBold, screenHeight, screenWidth, White } from './Styles';
import { strings } from '../screens/i18n';
// import MyComponent from './MyComponent';


// const MyComponent = ({
//   isFirstStep,
//   isLastStep,
//   handleNext,
//   handlePrev,
//   handleStop,
//   currentStep,
// }) => (
//   <View style={styles.tooltipContainer}>
//     <Text style={styles.tooltipText}>{currentStep.text}</Text>
//     <View style={styles.buttonContainer}>
//       <TouchableOpacity onPress={handleNext} style={[styles.button, {
//         backgroundColor: WhiteGery
//       }]}>
//         <Text style={[styles.buttonText, { color: Red }]}>{strings('lang.Skip')}</Text>
//       </TouchableOpacity>
//       {/* {!isFirstStep && (
//         <TouchableOpacity onPress={handleNext} style={[styles.button, {
//           backgroundColor: WhiteGery
//         }]}>
//           <Text style={[styles.buttonText, { color: Red }]}>Back</Text>
//         </TouchableOpacity>
//       )} */}
//       {isLastStep ? (
//         <TouchableOpacity onPress={handleStop} style={[styles.button, {
//           backgroundColor: Red
//         }]}>
//           <Text style={[styles.buttonText, { color: White }]}>{strings('lang.ending')}</Text>
//         </TouchableOpacity>
//       ) : (
//         <TouchableOpacity onPress={handleNext} style={[styles.button, {
//           backgroundColor: Red,
//         }]}>
//           <Text style={[styles.buttonText, { color: White }]}>{strings('lang.next')}</Text>
//         </TouchableOpacity>
//       )}
//     </View>
//   </View>
// );

const MyComponentCopilot = ({ props,
  isFirstStep,
  isLastStep,
  handleNext,
  handlePrev,
  handleStop,
  currentStep,

}) => {
  const CopilotText = walkthroughable(Text);

  const [showCopilot, setShowCopilot] = useState(false);

  useEffect(() => {
    const checkIfFirstLaunch = async () => {
      try {
        const hasLaunched = await AsyncStorage.getItem('hasLaunched');
        if (!hasLaunched) {
          setShowCopilot(true);
          await AsyncStorage.setItem('hasLaunched', 'true');
        }
      } catch (error) {
        console.error('Failed to check first launch status:', error);
      }
    };

    checkIfFirstLaunch();
  }, []);

  useEffect(() => {
    // if (showCopilot) {
    props.copilotEvents.on();
    props.start();
    // }

  }, [showCopilot]);


  const handleStepChange = (step) => {
    console.log(`step change:${step.name}`);
  }
  return (
    <View style={{ transform: [{ rotate: '90deg' }] }}>
      <CopilotStep
        text={props.text}
        order={props.orderId}
        name={props.name}>
        <CopilotText>
          {props.component}
        </CopilotText>
      </CopilotStep>

      <View style={styles.tooltipContainer}>
        <Text style={styles.tooltipText}>{currentStep.text}</Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity onPress={handleNext} style={[styles.button, {
            backgroundColor: WhiteGery
          }]}>
            <Text style={[styles.buttonText, { color: Red }]}>{strings('lang.Skip')}</Text>
          </TouchableOpacity>
          {/* {!isFirstStep && (
        <TouchableOpacity onPress={handleNext} style={[styles.button, {
          backgroundColor: WhiteGery
        }]}>
          <Text style={[styles.buttonText, { color: Red }]}>Back</Text>
        </TouchableOpacity>
      )} */}
          {isLastStep ? (
            <TouchableOpacity onPress={handleStop} style={[styles.button, {
              backgroundColor: Red
            }]}>
              <Text style={[styles.buttonText, { color: White }]}>{strings('lang.ending')}</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity onPress={handleNext} style={[styles.button, {
              backgroundColor: Red,
            }]}>
              <Text style={[styles.buttonText, { color: White }]}>{strings('lang.next')}</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

export default copilot({
  // Set the custom tooltip component
})(MyComponentCopilot);

const styles = StyleSheet.create({
  tooltipContainer: {
    paddingVertical: 15,
    borderRadius: 5,
    width: screenWidth / 2,
  },
  tooltipText: {
    color: Red,
    fontSize: screenWidth / 30,
    fontFamily: appFontBold,
    marginBottom: 20,
    textAlign: 'center'
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    padding: 10,
    backgroundColor: '#007bff',
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    height: screenHeight / 22,
    width: '40%',

    // marginHorizontal: 10
  },
  buttonText: {
    color: Red,
    fontSize: screenWidth / 35,
    fontFamily: appFontBold,
  },
});
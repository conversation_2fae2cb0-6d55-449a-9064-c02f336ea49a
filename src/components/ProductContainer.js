import React, { Component } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Platform } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1, appColor2 } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { strings } from '../screens/i18n';
import { Button } from 'native-base';

export default class ProductContainer extends Component {


    constructor(props) {
        super(props)
        this.state = {
            lan: '',
            quantity: 0
        }
    }

    async componentDidMount() {
        let lan = await AsyncStorage.getItem('lan')
        this.setState({ lan: lan })
    }

    // setModalStoreVisible() {
    //     this.setState({ ModalStoreVisible: !this.state.ModalStoreVisible })
    // }

    // setModalCancelVisible() {
    //     this.setState({ ModalCancelVisible: !this.state.ModalCancelVisible })
    // }



    render() {
        return (
            <View style={this.props.home ? styles.homeContainer : styles.container}>

                <TouchableOpacity onPress={this.props.onPress} style={{ width: screenWidth / 2.5, height: screenHeight / 5.5 }} >
                    <View style={{ width: '98%', alignSelf: "center", height: '65%', backgroundColor: White, overflow: 'hidden', alignItems: "center", justifyContent: "center" }}>
                        <Image source={require('../images/Silal/biscc.png')} style={{ width: '100%', height: '100%', resizeMode: "contain", }} />
                    </View>
                    <View style={{ width: '100%', height: '35%', justifyContent: "center" }}>
                        <Text style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30, marginStart: '5%' }}>لواكير</Text>
                        {this.props.hasOptions
                            ?
                            <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", paddingHorizontal: '5%' }}>
                                <Text numberOfLines={1} style={{ color: Red, fontFamily: appFontBold, fontSize: screenWidth / 30 }}>{strings('lang.ByOption')}</Text>
                            </View>
                            :
                            <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center", paddingHorizontal: '5%' }}>
                                <Text numberOfLines={1} style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 33 }}>{'5.00 '}{strings('lang.SR')}</Text>
                                <Text numberOfLines={1} style={{ color: DarkGrey, fontFamily: appFontBold, fontSize: screenWidth / 33, textDecorationLine: 'line-through', textDecorationStyle: 'dashed' }}>{'7.00 '}{strings('lang.SR')}</Text>
                            </View>
                        }

                    </View>
                </TouchableOpacity>
                {this.state.quantity == 0
                    ?
                    <Button
                        onPress={
                            this.props.hasOptions ?
                                this.props.onPress
                                :
                                () => this.setState({ quantity: this.state.quantity + 1 })
                        }
                        style={{ width: '90%', height: screenHeight / 22, alignSelf: "center", backgroundColor: appColor2, justifyContent: "center", alignItems: "center" }}
                    >
                        <Text numberOfLines={1} style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 33 }}>{strings('lang.Add_To_Cart')}</Text>
                    </Button>
                    :
                    <View style={{ width: '90%', height: screenHeight / 22, alignSelf: "center", backgroundColor: White, justifyContent: 'space-between', flexDirection: 'row', alignItems: "center", paddingHorizontal: '2%' }} >
                        <Button onPress={() => { this.setState({ quantity: this.state.quantity + 1 }) }} style={{ width: screenHeight / 25, height: screenHeight / 25, borderRadius: screenHeight / 50, alignSelf: "center", backgroundColor: appColor2, justifyContent: "center", alignItems: "center" }}>
                            <Image source={require('../images/Silal/plus.png')} style={{ width: '100%', height: '100%', resizeMode: "contain", tintColor: White }} />
                        </Button>
                        <Text numberOfLines={1} style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30 }}>{this.state.quantity}</Text>
                        <Button onPress={() => { this.setState({ quantity: this.state.quantity - 1 }) }} style={{ width: screenHeight / 25, height: screenHeight / 25, borderRadius: screenHeight / 50, alignSelf: "center", backgroundColor: appColor2, justifyContent: "center", alignItems: "center" }}>
                            <Image source={require('../images/Silal/min.png')} style={{ width: '100%', height: '100%', resizeMode: "contain", tintColor: White }} />
                        </Button>
                    </View>
                }

                {this.props.discount &&
                    <View style={{ position: 'absolute', start: 10, top: 5, height: '12%', backgroundColor: Red, borderRadius: 2, flexDirection: 'row', justifyContent: 'space-between', alignItems: "center", paddingStart: 5 }}>
                        <Text numberOfLines={1} style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 33 }}>{strings('lang.Discount')}</Text>
                        <Text numberOfLines={1} style={{ color: White, fontFamily: appFontBold, fontSize: screenWidth / 33, marginHorizontal: 5 }}>{'10%'}</Text>
                    </View>
                }

                {this.props.fav &&
                    <Button style={{ position: 'absolute', end: 10, top: 5, height: screenWidth/11,width:screenWidth/11, backgroundColor: MediumGrey, borderRadius: screenWidth/22,  justifyContent: "center", alignItems: "center"}}>
                        <Image style={{width:'80%',height:'80%',resizeMode:"contain",tintColor:Red}} source={require('../images/Silal/fav.png')} />
                    </Button>
                }


            </View >
        )

    }

};

const styles = StyleSheet.create({
    homeContainer: {
        backgroundColor: WhiteGery,
        width: screenWidth / 2.5,
        height: screenHeight / 4,
        alignSelf: 'center',
        borderWidth: .5,
        borderColor: MediumGrey,
        transform: Platform.OS == 'android' ? I18nManager.isRTL ? [{ scaleX: -1 }] : [{ scaleX: 1 }] : [{ scaleX: 1 }],
        marginHorizontal: 10,
        alignItems: "center",
        justifyContent: "space-between",
        paddingBottom: 5
    },
    container: {
        backgroundColor: WhiteGery,
        width: screenWidth / 2.5,
        height: screenHeight / 4,
        alignSelf: 'center',
        borderWidth: .5,
        borderColor: MediumGrey,
        marginHorizontal: 10,
        alignItems: "center",
        justifyContent: "space-between",
        paddingBottom: 5,
        marginBottom: 10
    },
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.3,
        height: screenHeight / 3,
        position: 'absolute',
        top: screenHeight / 3.2,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10
    },
    text: {
        fontFamily: appFontBold,
        color: DarkBlue,
        fontSize: screenWidth / 22,
        marginTop: '4%'
    },
    buttonContainer: {
        width: screenWidth / 4,
        height: '65%',
        backgroundColor: DarkBlue,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    buttonContainer2: {
        width: screenWidth / 4,
        height: '65%',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: DarkBlue,
        alignSelf: 'center',
        justifyContent: 'center',
        marginHorizontal: 5,
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        // marginTop: 10,
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    buttonText2: {
        color: DarkBlue, marginVertical: 5,
        alignSelf: 'center', fontSize: screenWidth / 25, fontFamily: 'Cairo-Bold'
    },
    icon: { resizeMode: 'contain', width: '90%', height: '55%', },
    modelIconContainer: { width: '15%', height: '100%', justifyContent: 'center' },
    modelTextContainer: { width: '85%', height: '100%', justifyContent: 'center' },
    modelText: { fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 25, },

});



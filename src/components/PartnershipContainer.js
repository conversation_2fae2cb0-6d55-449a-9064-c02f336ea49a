import React, { useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Platform } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1, appColor2, Red1, DarkYellow } from './Styles';
import { strings } from '../screens/i18n';
import { Button } from 'native-base';
import LoadingMoreSmall from './LoadingMoreSmall';
import StarRating from 'react-native-star-rating';
import MapView from 'react-native-maps';

const PartnershipContainer = props => {

    const [fav, setFav] = useState(false)


    return (
        <View style={{ flexDirection: 'row', width: '95%', maxHeight: screenHeight / 6, borderColor: MediumGrey, borderWidth: 1, justifyContent: 'space-between', borderRadius: 10, alignItems: "flex-start", alignSelf: 'center', marginVertical: '2%' }}>
            <View style={{ flexDirection: 'row', alignItems: 'center', marginVertical: '2%' }}>
                <View style={styles.text}>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start' }}>{strings('lang.OrderNumber')}</Text>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Date')}</Text>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start' }}>{strings('lang.Status')}</Text>
                </View>
                <View style={styles.text}>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start' }}>115</Text>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start' }}>22 ماىس 2022</Text>
                    <Text style={{ fontFamily: appFontBold, fontSize: screenWidth / 33, color: Black, alignSelf: 'flex-start' }}>معلق</Text>
                </View>
            </View>
            <Button onPress={props.detailsPress} style={{ marginEnd: '2%', width: '30%', alignSelf: "center", height: 35, backgroundColor: Red, alignItems: "center", flexDirection: "row", justifyContent: "center", borderRadius: screenWidth / 5 }}>
                <Image source={require('../images/modrek/eyee.png')} style={{ tintColor: White, resizeMode: 'contain', width: '12%', height: '100%', marginEnd: 5 }} />
                <Text style={{ fontSize: screenWidth / 48, fontFamily: appFontBold, color: White, }}>{strings('lang.thedetails')}</Text>
            </Button>
        </View>
    )
};

export default PartnershipContainer;
const styles = StyleSheet.create({
    text: {
        flexDirection: 'column',
        marginStart: '10%',


    },

});





import React, { Component } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager, Platform } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1, appColor2 } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { strings } from '../screens/i18n';
import { Button } from 'native-base';

export default class CartProductContainer extends Component {


    constructor(props) {
        super(props)
        this.state = {
            lan: '',
            quantity: 1
        }
    }

    async componentDidMount() {
    }


    render() {
        return (
            <View style={{ alignSelf: "center", flexDirection: 'row', width: '90%', alignItems: "center", height: screenHeight / 8, borderWidth: 1, borderColor: Medium<PERSON>rey, backgroundColor: White, justifyContent: "space-between", marginBottom: screenHeight / 80 }}>
                <View style={{ width: '25%', height: '100%', alignItems: "center", justifyContent: "center", borderEndWidth: 1, borderEndColor: MediumGrey }}>
                    <Image style={{ width: '100%', height: '100%', resizeMode: "contain" }} source={require('../images/Silal/lize.png')} />
                </View>
                <View style={{ width: '73%', height: '100%', paddingTop: '2%' }}>
                    <Text numberOfLines={1} style={styles.textBlack}>ليز بنكة الزعتر </Text>
                    <View style={{ flexDirection: 'row', alignItems: "center" }}>
                        <Text numberOfLines={1} style={styles.textGreen}>{'5.00'} {strings('lang.SR')}</Text>
                    </View>
                </View>
                <View style={{ position: 'absolute', bottom: '5%', end: '3%', width: '30%', height: screenHeight / 20, backgroundColor: WhiteGery, justifyContent: 'space-between', flexDirection: 'row', alignItems: "center", paddingHorizontal: '7%', borderRadius: 8 }} >
                    <Button onPress={() => { this.setState({ quantity: this.state.quantity + 1 }) }} style={{ width: screenHeight / 30, height: screenHeight / 30, borderRadius: screenHeight / 60, alignSelf: "center", backgroundColor: appColor2, justifyContent: "center", alignItems: "center" }}>
                        <Image source={require('../images/Silal/plus.png')} style={{ width: '100%', height: '100%', resizeMode: "contain", tintColor: White }} />
                    </Button>
                    <Text numberOfLines={1} style={{ color: Black, fontFamily: appFontBold, fontSize: screenWidth / 30 }}>{this.state.quantity}</Text>
                    <Button onPress={() => { if (this.state.quantity > 1) { this.setState({ quantity: this.state.quantity - 1 }) } }} style={{ width: screenHeight / 30, height: screenHeight / 30, borderRadius: screenHeight / 60, alignSelf: "center", backgroundColor: appColor2, justifyContent: "center", alignItems: "center" }}>
                        <Image source={require('../images/Silal/min.png')} style={{ width: '100%', height: '100%', resizeMode: "contain", tintColor: White }} />
                    </Button>
                </View>
            </View>
        )

    }

};

const styles = StyleSheet.create({
    textBlack: { fontFamily: appFontBold, color: Black, fontSize: screenWidth / 28, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textWhite: { fontFamily: appFontBold, color: White, fontSize: screenWidth / 33, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGreen: { fontFamily: appFontBold, color: appColor1, fontSize: screenWidth / 30, textAlign: I18nManager.isRTL ? 'left' : 'right' },
    textGrey: { fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, textDecorationLine: 'line-through', textDecorationStyle: 'dashed', textAlign: I18nManager.isRTL ? 'left' : 'right', marginStart: 10 },

});



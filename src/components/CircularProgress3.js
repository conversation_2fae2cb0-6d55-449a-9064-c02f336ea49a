import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { Black, Red, White, screenWidth } from './Styles';

const CircularProgress3 = ({ size, strokeWidth, duration, color, fontSize }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = 2.5 * Math.PI * radius;
    const [progressValue, setProgressValue] = useState();
    const animatedValue = useState(new Animated.Value(0))[0];
    const rotationValue = useState(new Animated.Value(0))[0];

    const AnimatedCircle = Animated.createAnimatedComponent(Circle);

    useEffect(() => {
        // Animate the progress value
        Animated.timing(animatedValue, {
            toValue: 100,
            duration: duration,
            useNativeDriver: false,
        }).start();

        // Animate the rotation of the inner ring
        Animated.loop(
            Animated.timing(rotationValue, {
                toValue: 1,
                duration: 2000,
                useNativeDriver: true,
            })
        ).start();

        animatedValue.addListener((v) => {
            const progress = Math.round(v.value);
            // console.log('animatedValue', v.value);
            // console.log('animatedValue', progress);

            setProgressValue(progress);
        });

        return () => {
            animatedValue.removeAllListeners();
        };
    }, [duration, animatedValue, rotationValue]);

    const strokeDashoffset = animatedValue.interpolate({
        inputRange: [0, 100],
        outputRange: [circumference, 0],
    });

    const rotation = rotationValue.interpolate({
        inputRange: [0, 1],
        outputRange: ['0deg', '360deg'],
    });

    return (
        <View style={styles.container}>
            <Svg width={size} height={size}>
                {/* Background Circle */}
                {/* <Circle
                    stroke="#e0e0e0"
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    strokeWidth={strokeWidth}
                /> */}
                {/* Progress Circle */}
                <AnimatedCircle
                    stroke={Red}
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    // r={radius}
                    // strokeWidth={strokeWidth}
                    // strokeDasharray={circumference}
                    r={radius / 1.8}
                    strokeWidth={strokeWidth / 1.5}
                    strokeDasharray={`${circumference / 2}`}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                    transform={`rotate(${rotation}, ${size / 2}, ${size / 2})`}
                />
                {/* Inner Rotating Circle */}
                {/* <AnimatedCircle
                    stroke={Red}
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    r={radius / 1.5}
                    strokeWidth={strokeWidth}
                    strokeDasharray={`${circumference / 2}`}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                    transform={`rotate(${rotation}, ${size / 2}, ${size / 2})`}
                /> */}
            </Svg>
            <Text style={[styles.percentageText, {
                fontSize: fontSize ? fontSize : screenWidth / 35,
                color: color ? color : White
            }]}>{`${progressValue}%`}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    percentageText: {
        position: 'absolute',
        fontSize: screenWidth / 35,
        fontWeight: '600',
        color: White,
    },
});

export default CircularProgress3;
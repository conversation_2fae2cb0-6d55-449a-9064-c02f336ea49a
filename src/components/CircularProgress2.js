import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { Red, White } from './Styles';

const CircularProgress2 = ({ size, strokeWidth, duration }) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const [progressValue, setProgressValue] = useState(0);
    const animatedValue = useState(new Animated.Value(0))[0]; // Ensure animatedValue is not recreated on each render

    const AnimatedCircle = Animated.createAnimatedComponent(Circle);

    useEffect(() => {
        // Reset the animation value to 0 before starting the animation
        animatedValue.setValue(0);

        const animation = Animated.timing(animatedValue, {
            toValue: 100,
            duration: duration,
            useNativeDriver: false, // Set to false since we're animating SVG properties
        });

        animatedValue.addListener((v) => {
            const progress = Math.round(v.value);
            setProgressValue(progress);
        });

        animation.start();

        return () => {
            animatedValue;
        };
    }, [duration, animatedValue]); // Add animatedValue to the dependency array

    const strokeDashoffset = animatedValue.interpolate({
        inputRange: [0, 100],
        outputRange: [circumference, 90],
    });

    return (
        <View style={styles.container}>
            <Svg width={size} height={size}>
                <Circle
                    stroke="#e0e0e0"
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    strokeWidth={strokeWidth}
                    strokeDasharray="5,5" // Dashed background circle
                />
                <AnimatedCircle
                    stroke={Red}
                    fill="none"
                    cx={size / 2}
                    cy={size / 2}
                    r={radius}
                    strokeWidth={strokeWidth}
                    strokeDasharray={`${circumference / 1}, ${circumference / 1.5}`}
                    strokeDashoffset={strokeDashoffset}
                    strokeLinecap="round"
                />
            </Svg>
            <Text style={styles.percentageText}>{`${progressValue}%`}</Text>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        justifyContent: 'center',
        alignItems: 'center',
    },
    percentageText: {
        position: 'absolute',
        fontSize: 28,
        fontWeight: '600',
        color: White,
    },
});

export default CircularProgress2;
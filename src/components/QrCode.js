import React, { } from 'react';
import { View, StyleSheet, Image, Text, I18nManager, Modal, Platform } from "react-native";
import { TouchableOpacity } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, <PERSON>, mSize, <PERSON>, <PERSON>, <PERSON> } from './Styles';
import QRCodeScanner from 'react-native-qrcode-scanner';
import { RNCamera } from 'react-native-camera';


const QrCode = (props) => {

    function onSuccess(data) {
        props.sendDataToParent(data);
    }

    return (
        <QRCodeScanner
            onRead={onSuccess}
            reactivate={true}
            flashMode={RNCamera.Constants.FlashMode.auto}
            showMarker={true}
            ref={(node) => { node }}
            customMarker={<View style={styles.rectangleContainer}>
                <View style={styles.topOverlay}>
                    <Text style={{ fontSize: screenWidth / 15, fontFamily: appFontBold, color: "white" }}>
                        QR CODE SCANNER
                    </Text>
                </View>

                <View style={{ flexDirection: "row" }}>
                    <View style={styles.leftAndRightOverlay} />
                    <View style={styles.rectangle}>
                        <Image source={require('../images/modrek/scanner2.png')} style={{ resizeMode: 'contain', width: screenWidth, height: screenHeight, tintColor: White, }} />

                        <View
                            style={styles.scanBar}
                        // direction="alternate-reverse"
                        // iterationCount="infinite"
                        // duration={1700}
                        // easing="linear"
                        // animation={makeSlideOutTranslation(
                        //     "translateY",
                        //     screenWidth * -0.54
                        // )}
                        />
                    </View>

                    <View style={styles.leftAndRightOverlay} />
                </View>

                <View style={styles.bottomOverlay} >
                    {/* <TouchableOpacity
onPress={() => refRBSheet.current.close()}
>
<Text style={{ fontSize: screenWidth / 15, fontFamily: appFontBold, color: "white" }}>
  close QR CODE
</Text>
</TouchableOpacity> */}

                </View>
            </View>
            }
            cameraStyle={{ height: screenHeight, }}
        />
    )
};
export default QrCode;
const styles = StyleSheet.create({

    rectangleContainer: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "transparent"
    },

    rectangle: {
        height: screenWidth * 0.65,
        width: screenWidth * 0.65,
        borderWidth: screenWidth * 0.005,
        borderColor: "#fff",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "transparent"
    },

    topOverlay: {
        flex: 1,
        height: screenWidth,
        width: screenWidth,
        backgroundColor: "rgba(0,0,0,0.5)",
        justifyContent: "center",
        alignItems: "center"
    },

    bottomOverlay: {
        flex: 1,
        height: screenWidth,
        width: screenWidth,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: "rgba(0,0,0,0.5)",
        paddingBottom: screenWidth * 0.25
    },

    leftAndRightOverlay: {
        height: screenWidth * 0.65,
        width: screenWidth * 0.46,
        backgroundColor: "rgba(0,0,0,0.5)",
    },

    scanBar: {
        width: screenWidth * 0.46,
        height: screenWidth * 0.0025,
        backgroundColor: White,
        position: 'absolute'
    },
});



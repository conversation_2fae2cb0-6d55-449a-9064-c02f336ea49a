import React, { useEffect, useState } from 'react';
import { View, StatusBar, StyleSheet, Modal, ImageBackground, Text, Image, Pressable, I18nManager } from 'react-native';
import { Black, Blue, DarkGrey, MediumGrey, Red, Red1, White, appFontBold, screenHeight, screenWidth } from './Styles';
import { Button } from 'native-base';
import { strings } from '../screens/i18n';
import { ScrollView, TouchableOpacity } from 'react-native-gesture-handler';
import { VLCPlayer, VlCPlayerView } from 'react-native-vlc-media-player';
import Loading from './Loading';



const LiveModal1 = (props) => {
  const [selectedSheep, setSelectedSheep] = useState(0)
  const [farmImageUrl, setFarmImageUrl] = useState('')
  const [link, setLink] = useState(props.link)
  const [link2, setLink2] = useState(props.link2)
  const [linkId, setLinkId] = useState(false)
  const [loadingMore, setLoadingMore] = useState(false)


  useEffect(() => {
    console.log('props.status', props.status);
    console.log('props.sheep', props.sheep);
    if (props.sheep && props.sheep[0] && props.sheep[0].sheep) {
      setSelectedSheep(props.sheep[0].sheep.id)
      setFarmImageUrl(props.sheep[0].sheep.farm ? props.sheep[0].sheep.farm.image : '')
    }
  }, [])

  return (
    <Modal
      transparent={true}
      animationType="fade"
      visible={props.ModalVisible}
    >

      <Button transparent onPress={props.modalPress} style={{ backgroundColor: '#000', opacity: 0.5, width: screenWidth, height: screenHeight }} >
      </Button>




      <View style={styles.modal}>

        {/* <VLCPlayer
          source={{
            initType: 2,
            hwDecoderEnabled: 1,
            hwDecoderForced: 1,
            uri: link,
            initOptions: [
              '--no-audio',
              '--rtsp-tcp',
              '--network-caching=150',
              '--rtsp-caching=150',
              '--no-stats',
              '--tcp-caching=150',
              '--realrtsp-caching=150',
            ],
          }}
          style={styles.backgroundVideo}
          // source={{ uri: linkk }}
          videoAspectRatio="16:9"
          isLive={true}
          autoplay={true}
          autoReloadLive={true}
          hwDecoderEnabled={1}
          hwDecoderForced={1}
          mediaOptions={{
            ':network-caching': 0,
            ':live-caching': 300,
          }}
          onError={(err) => console.log("video error:", err)}
          resizeMode='contain'
        />

        <View style={{ transform: [{ rotate: '90deg' }], width: screenHeight / 1.1, height: screenWidth / 10, position: 'absolute', top: screenHeight / 2.05, right: -screenWidth / 35, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
          <View style={{ flexDirection: 'row', marginTop: 50 }}>
            <Button transparent onPress={props.modalPress} style={{ width: screenWidth / 12, height: screenWidth / 12, backgroundColor: White, borderRadius: 5, alignItems: 'center', justifyContent: 'center', marginEnd: screenHeight / 60 }}>
              <Image source={require('../images/modrek/arrow.png')} style={{ zIndex: 10000, width: screenWidth / 20, height: screenWidth / 20, resizeMode: 'contain', alignSelf: 'center', tintColor: Red }} />
            </Button>
            {props.status && props.status.name
              ?
              <View style={{ paddingHorizontal: 5, minWidth: screenHeight / 4.5, height: screenWidth / 12, backgroundColor: Red, borderRadius: 5, alignItems: 'center', justifyContent: 'center', }}>
                <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 33, }}>{props.status.name}</Text>
              </View>
              :
              <></>
            }
          </View>

          <View style={{ flexDirection: 'row', alignItems: 'center', }}>
            <View style={{ paddingHorizontal: 15, width: screenWidth / 5, height: screenWidth / 15, backgroundColor: Red, marginEnd: screenWidth / 7, marginTop: screenHeight / 20, borderRadius: 10, flexDirection: 'row', alignItems: 'center', justifyContent: 'center', }}>
              <View style={{ width: 8, height: 8, borderRadius: 10, backgroundColor: 'red', alignSelf: 'center', marginEnd: 5, marginTop: '2%' }}></View>
              <Text style={{ fontFamily: appFontBold, color: White, fontSize: screenWidth / 40, }}>{strings('lang.Direct')}</Text>
            </View>
            {farmImageUrl
              ?
              <View style={styles.farmImageContainer}>
                <Image source={{ uri: farmImageUrl }} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
              </View>
              :
              <></>
            }
            <Button
             onPress={() => { if (link != props.link) { console.log('link', link);
              console.log('props.link', props.link); setLink(props.link) } }}
               style={[styles.camContainer3, { backgroundColor: link == props.link ? White : Red }]}>
              <Image source={require('../images/modrek/cam.png')} style={[styles.camImage, { tintColor: link == props.link ? Red : White }]} />
            </Button>
          </View>




        </View>

        {props.sheep && props.sheep.length >= 1
          ?
          <View style={{ transform: [{ rotate: '90deg' }], width: screenHeight / 20, height: screenWidth / 2, position: 'absolute', bottom: - screenHeight / 14, start: screenWidth / 2.2, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' }}>
            <ScrollView
              horizontal={false}
              showsVerticalScrollIndicator={false}
              style={{ alignSelf: "center", }}
            >
              {props.sheep.map((item, index) => {
                return (
                  <View
                    // onPress={() => {
                    //   setSelectedSheep(item.id); console.log('item', item); setLink(
                    //     item.sheep ? item.sheep.farm && item.sheep.farm.camera_url : ''
                    //   ); setFarmImageUrl(item.sheep ? item.sheep.farm && item.sheep.farm.image : ''); console.log('1');

                    // }}
                    style={{ ...styles.colorContainer, ...{ backgroundColor: selectedSheepsIds.find(sheep => sheep == item.id) ? item.collar_color : White, borderWidth: 2.5, borderColor: selectedSheepsIds.find(sheep => sheep == item.id) ? null : item.collar_color } }} >
                    <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: White, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                  </View>
                )
              })}
            </ScrollView>

          </View>
          :
          <></>
        }

        <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'space-between', zIndex: 1000, transform: [{ rotate: '90deg' }], height: screenWidth / 20, width: screenHeight / 12, marginStart: screenWidth / 4.5 }}>
          <Button onPress={() => { if (link != props.link2) { console.log('link', link); console.log('props.sheep.sheep.farm.camera_url2', props.link2); setLink(props.link2) } }} style={[styles.camContainer2, { backgroundColor: link == props.link2 ? White : Red }]}>
            <Image source={require('../images/modrek/cam.png')} style={[styles.camImage1, { tintColor: link == props.link2 ? Red : White }]} />
          </Button>


        </View>
 */}

        <View style={{ height: screenHeight / 1, width: screenWidth, justifyContent: 'space-between', alignItems: 'center', transform: [{ rotate: I18nManager.isRTL ? '0deg' : '0deg' }], backgroundColor: Black }}>

          {/* {camera == 1
        ? */}

          {/* :
        <View></View>
    }  */}


          <View style={{ width: screenWidth / 1, height: screenHeight / 6, flexDirection: 'row', marginTop: -screenHeight / 100, zIndex: 10000 }}>
            <View style={{ ...styles.infoContainer, ...{ backgroundColor: null } }}>
              {/* <View style={{ width: screenWidth / 5, alignSelf: "center", flexDirection: 'row', height: screenHeight / 30, alignItems: "center", justifyContent: "center", }}>
            <View style={{ width: 8, height: 8, borderRadius: 10, backgroundColor: 'red', alignSelf: 'center', marginHorizontal: 5, marginTop: '2%' }}></View>
            <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text>
        </View> */}
            </View>

            <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'flex-end', zIndex: 1000, transform: [{ rotate: '90deg' }], height: screenWidth / 3, width: screenWidth / 5, marginStart: screenWidth / 5 }}>
              {/* <Button onPress={() => { toggleCameras(1) }} style={styles.camContainer}>
            <Image source={require('../images/modrek/cam.png')} style={styles.camImage} />
        </Button> */}
              {/* {selectedSheepsIds.length == 0
            ?
            <View style={{ width: '18%', height: '50%', }}>
            </View>
            : */}

            </View>

            {/* <Button onPress={() => {
              if (linkId == true) {
                console.log('link', link);
                console.log('linkId', linkId);
                setLinkId(false)
              }
            }} style={[styles.camContainer3, { backgroundColor: linkId == false ? Red : White }]}>
              <Image source={require('../images/modrek/cam.png')} style={[styles.camImage, { tintColor: linkId == false ? White : Red }]} />
            </Button> */}
          </View>

          <>

            {/* <Video
        source={{ uri: "https://cph-p2p-msl.akamaized.net/hls/live/2000341/test/master.m3u8" }}   // Can be a URL or a local file.
        // ref={(ref) => {
        //     this.player = ref
        // }}                                      // Store reference
        // onBuffer={this.onBuffer}                // Callback when remote video is buffering
        // onError={this.videoError}               // Callback when video cannot be loaded
        style={styles.backgroundVideo}
    /> */}



            {/* {cameraId == true
        ? */}
            <VLCPlayer
              source={{
                initType: 2,
                hwDecoderEnabled: 1,
                hwDecoderForced: 1,
                uri: linkId ? link2 : link,
                initOptions: [
                  '--no-audio',
                  '--rtsp-tcp',
                  '--network-caching=150',
                  '--rtsp-caching=150',
                  '--no-stats',
                  '--tcp-caching=150',
                  '--realrtsp-caching=150',
                ],
              }}
              style={[styles.backgroundVideo]}
              // source={{ uri: linkk }}
              videoAspectRatio="16:9"
              isLive={true}
              autoplay={true}
              autoReloadLive={true}
              hwDecoderEnabled={1}
              hwDecoderForced={1}
              mediaOptions={{
                // ':network-caching': 0,
                // ':live-caching': 300,
              }}
              onError={(err) => console.log("video error:", err)}
              resizeMode='contain'
            />
            {/* :
        <VLCPlayer
            source={{
                initType: 2,
                hwDecoderEnabled: 1,
                hwDecoderForced: 1,
                uri: linkk,
                initOptions: [
                    '--no-audio',
                    '--rtsp-tcp',
                    // '--network-caching=150',
                    // '--rtsp-caching=150',
                    '--no-stats',
                    // '--tcp-caching=150',
                    // '--realrtsp-caching=150',
                ],
            }}
            style={cameraId == false ? [styles.backgroundVideo] : [{ width: 0, height: 0 }]}
            // source={{ uri: linkk }}
            videoAspectRatio="16:9"
            isLive={true}
            autoplay={true}
            autoReloadLive={true}
            hwDecoderEnabled={1}
            hwDecoderForced={1}
            mediaOptions={{
                ':network-caching': 0,
                ':live-caching': 300,
            }}
            onError={(err) => console.log("video error:", err)}
            resizeMode='contain'

        />
    } */}


            {/* <Text style={{ color: White, fontSize: screenWidth / 25, fontFamily: appFontBold, alignSelf: 'center', transform: [{ rotate: '90deg' }] }}>{strings('lang.message18')}</Text> */}

            <View style={styles.infoContainer1}>
              <View style={{ width: screenWidth / 5, alignSelf: "center", flexDirection: 'row', height: screenHeight / 30, alignItems: "center", justifyContent: "center", }}>
                <View style={{ width: 8, height: 8, borderRadius: 10, backgroundColor: 'red', alignSelf: 'center', marginEnd: 5, marginTop: '2%' }}></View>
                <Text style={{ fontSize: screenWidth / 40, fontFamily: appFontBold, color: White, }}>{strings('lang.Direct')}</Text>
              </View>
            </View>
            {farmImageUrl
              ?
              <View style={styles.farmImageContainer}>
                <Image source={{ uri: farmImageUrl }} style={{ width: '100%', resizeMode: 'cover', height: '100%', alignSelf: 'center' }} />
                {/* <Image source={require('../images/modrek/farmmm.jpeg')} style={{ width: '100%', resizeMode: 'contain', height: '100%', alignSelf: 'center' }} /> */}
              </View>
              :
              <></>
            }
          </>


          <View style={{ width: screenWidth / 1.1, height: screenHeight / 12, flexDirection: 'row', marginBottom: screenHeight / 20 }}>
            {/* <Button onPress={() => { props.navigation.navigate('Home', { weightId: null, sheepCatId: null }) }} style={styles.backImageContainer}> */}
            <Button onPress={props.modalPress} style={styles.backImageContainer}>
              <Image source={require('../images/modrek/arrow.png')} style={styles.backImage} />
            </Button>
            {props.sheep && props.sheep.length >= 1
              ?
              <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'space-between', marginStart: screenWidth / 3.7, transform: [{ rotate: '90deg' }], flexDirection: 'row', height: screenWidth / 1.5, width: screenHeight / 15, }}>
                <ScrollView
                  horizontal={false}
                  showsVerticalScrollIndicator={false}
                  style={{ alignSelf: "center", }}
                >

                  {props.sheep.map((item, index) => {
                    return (
                      // <TouchableOpacity
                      //     onPress={() => { addSheepToPicked(item) }}
                      //     style={{ ...styles.colorContainer, ...{ backgroundColor: selectedSheepsIds.find(sheep => sheep == item.id) ? item.stamp_color : White, borderWidth: 2.5, borderColor: selectedSheepsIds.find(sheep => sheep == item.id) ? null : item.stamp_color } }} >
                      //     <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: selectedSheepsIds.find(sheep => sheep == item.id) ? White : item.stamp_color, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                      // </TouchableOpacity>
                      <View
                        style={{ ...styles.colorContainer, ...{ backgroundColor: item.sheep && item.sheep.collar_color, borderWidth: 2.5, borderColor: White } }} >
                        <Text style={{ fontSize: screenWidth / 26, fontFamily: appFontBold, color: White, alignSelf: 'center', textAlignVertical: 'center' }}>{index + 1}</Text>
                      </View>
                    )
                  })}
                </ScrollView>
              </View>
              :
              <></>
            }
            <View style={{ alignSelf: "center", alignItems: 'center', justifyContent: 'space-between', zIndex: 1000, transform: [{ rotate: '90deg' }], height: screenWidth / 20, width: screenHeight / 12, marginStart: screenWidth / 4.5 }}>
              <Button onPress={() => {
                if (linkId == false) {
                  console.log('link2', link2);
                  console.log('linkId', linkId);
                  setLinkId(true)
                }
              }} style={[styles.camContainer2, { backgroundColor: linkId == true ? Red : White }]}>
                <Image source={require('../images/modrek/cam.png')} style={[styles.camImage1, { tintColor: linkId == true ? White : Red }]} />
              </Button>


              {/* <Button onPress={() => { toggleCameras(1) }} style={styles.camContainer}>
            <Image source={require('../images/modrek/cam.png')} style={styles.camImage} />
        </Button> */}
            </View>
          </View>
        </View >

      </View>


    </Modal>
  );
}

export default LiveModal1;

const styles = StyleSheet.create({
  modal: {
    flex: 1,
    alignItems: 'center',
    width: screenWidth / 1,
    height: screenHeight / 1,
    position: 'absolute',
    // top: screenHeight / 18,
    alignSelf: 'center',
    backgroundColor: Black,
    // borderRadius: 10,
    // paddingVertical: '8%',
    justifyContent: 'center',
    // paddingHorizontal: '5%',
    overflow: 'hidden'
  },
  modaltext: {
    fontFamily: appFontBold,
    color: Red,
    fontSize: screenWidth / 22,
    alignSelf: 'center',
    marginTop: '0%',
    textAlign: 'center'
  },
  modalbuttonContainer: {
    width: screenWidth / 4,
    height: '75%',
    backgroundColor: White,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    marginTop: 7,
    borderWidth: 1,
    borderColor: Red,
  },
  modalbuttonContainer2: {
    width: screenWidth / 4,
    height: '75%',
    backgroundColor: Red,
    alignSelf: 'center',
    marginHorizontal: 5,
    justifyContent: 'center',
    borderRadius: 10,
    alignItems: 'center',
    padding: 12,
    marginTop: 7,
  },
  modalbuttonText2: {
    color: White,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28
  },
  modalbuttonText: {
    color: Red,
    fontFamily: appFontBold,
    fontSize: screenWidth / 28
  },
  // backgroundVideo: {
  //   flex: 1,
  //   // backgroundColor: 'red',
  //   height: screenWidth / 1.1,
  //   width: screenHeight / 1.2,
  //   // position: 'absolute',
  //   // top: -screenHeight / 100,
  //   // left: 0,
  //   // bottom: 0,
  //   // right: 0,
  //   transform: [{ rotate: '90deg' }],
  //   // top: -screenHeight / 20,
  //   // zIndex: 1000000000
  // },
  backgroundVideo: {
    flex: 1,
    // backgroundColor: 'red',
    width: screenHeight / 1.35,
    height: screenWidth / 1,
    // position: 'absolute',
    top: -screenHeight / 100,
    // left: 0,
    // bottom: 0,
    // right: 0,
    transform: [{ rotate: '90deg' }],
    // top: -screenHeight / 20,
    // zIndex: 1000000000
  },
  farmImageContainer: {
    width: screenWidth / 8,
    height: screenWidth / 8,
    // marginLeft: screenWidth / 17,
    position: 'absolute',
    end: -screenWidth / 30,
    top: screenHeight / 30,
    // marginTop: screenHeight / 40,
    overflow: 'hidden',
    alignSelf: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Red1,
    borderRadius: 10,
  },
  // farmImageContainer: {
  //   width: screenWidth / 8,
  //   height: screenWidth / 8, marginLeft: screenWidth / 17, borderRadius: 20, overflow: 'hidden',
  //   flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
  //   alignItems: 'center',
  //   justifyContent: 'center',
  //   marginTop: screenWidth / 14, backgroundColor: White, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 100, start: 0
  // },
  infoContainer1: {
    width: screenWidth / 5,
    height: screenWidth / 15, marginLeft: 10,
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: screenWidth / 14, backgroundColor: Red, borderRadius: 10, position: 'absolute', zIndex: 10000,
  },
  colorContainer: {
    width: screenHeight / 22,
    height: screenHeight / 22,
    alignSelf: 'center',
    marginHorizontal: 10,
    borderRadius: screenHeight / 10,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 20,
    marginStart: 20
  },
  camImage: {
    width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ rotate: '90deg' }]
  },
  camImage1: {
    width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ scaleX: -1 }]
  },
  camContainer2: {
    width: screenWidth / 9,
    height: screenWidth / 9,
    borderRadius: screenWidth / 15,
    backgroundColor: Red,
    position: 'absolute',
    top: screenHeight / 15,
    start: screenWidth / 5.5,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center'
    // transform: [{ rotate: '90deg' }]
  },
  camContainer3: {
    width: screenWidth / 9,
    height: screenWidth / 9,
    borderRadius: screenWidth / 15,
    backgroundColor: Red,
    // marginTop: screenHeight / ,
    position: 'absolute',
    top: screenHeight / 3.1,
    end: screenHeight / 100,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center', marginStart: screenWidth / 5, marginTop: screenHeight / 25,
    transform: [{ rotate: '260deg' }]
  },


  colorContainer: {
    width: screenHeight / 22,
    height: screenHeight / 22,
    alignSelf: 'center',
    marginHorizontal: 10,
    borderRadius: screenHeight / 10,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 4
  },
  container: {
    flex: 1,
    alignItems: 'center',
    backgroundColor: 'white',
  },
  backgroundVideo: {
    flex: 1,
    // backgroundColor: 'red',
    width: screenHeight / 1.35,
    height: screenWidth / 1,
    // position: 'absolute',
    top: -screenHeight / 100,
    // left: 0,
    // bottom: 0,
    // right: 0,
    transform: [{ rotate: '90deg' }],
    // top: -screenHeight / 20,
    // zIndex: 1000000000
  },
  // backImageContainer: {
  //     width: screenWidth / 10,
  //     height: screenWidth / 10,
  //     backgroundColor: White,
  //     position: 'absolute',
  //     bottom: '3%',
  //     start: '5%',
  //     alignItems: 'center',
  //     justifyContent: 'center',
  //     transform: [{ rotate: '90deg' }]
  // },
  backImage: {
    width: "50%", height: "70%", tintColor: Red, resizeMode: "contain", alignSelf: 'center',
  },
  infoContainer: {
    width: screenWidth / 5,
    height: screenWidth / 15, marginLeft: 10,
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: DarkGrey, borderRadius: 10,
  },
  infoContainer1: {
    width: screenWidth / 5,
    height: screenWidth / 15, marginLeft: 10,
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: Red, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 6, start: 0
  },
  farmImageContainer: {
    width: screenWidth / 8,
    height: screenWidth / 8, marginLeft: screenWidth / 17, borderRadius: 20, overflow: 'hidden',
    flexDirection: I18nManager.isRTL ? 'row-reverse' : 'row', alignSelf: 'center', marginVertical: screenHeight / 28,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '90deg' }], marginTop: screenWidth / 14, backgroundColor: White, borderRadius: 10, position: 'absolute', zIndex: 10000, top: screenHeight / 100, start: 0
  },
  reserveContainer: {
    width: screenWidth / 5,
    height: screenWidth / 11,
    // marginLeft: I18nManager.isRTL ? '5%' : '0%',
    // marginRight: I18nManager.isRTL ? '0%' : '5%',
    borderRadius: 10, borderWidth: 0, borderColor: White,
    backgroundColor: '#A30000',
    // position: 'absolute',
    // bottom: screenHeight / 5,
    // start: screenWidth / 1.35,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-end',
    marginVertical: '4%'
    // borderWidth: 1,
    // borderColor: '#A30000'
    // transform: [{ rotate: '90deg' }]
  },
  camContainer: {
    width: screenWidth / 9,
    height: screenWidth / 9,
    borderRadius: screenWidth / 15,
    backgroundColor: Red,
    // position: 'absolute',
    // bottom: screenHeight / 20,
    // start: screenWidth / 1.3,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center'
    // transform: [{ rotate: '90deg' }]
  },
  camContainer2: {
    width: screenWidth / 9,
    height: screenWidth / 9,
    borderRadius: screenWidth / 15,
    backgroundColor: Red,
    // position: 'absolute',
    // top: screenHeight / 15,
    // start: screenWidth / 1.3,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center'
    // transform: [{ rotate: '90deg' }]
  },
  camContainer3: {
    width: screenWidth / 9,
    height: screenWidth / 9,
    borderRadius: screenWidth / 15,
    backgroundColor: Red,
    // position: 'absolute',
    // top: screenHeight / 15,
    // start: screenWidth / 1.3,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center', marginStart: screenWidth / 5, marginTop: screenHeight / 25
    // transform: [{ rotate: '90deg' }]
  },
  backImageContainer: {
    width: screenWidth / 10,
    height: screenWidth / 10,
    backgroundColor: White,
    // position: 'absolute',
    // bottom: screenHeight / 30,
    // start: I18nManager.isRTL ? screenWidth / 25 : screenWidth / 1.20,
    alignItems: 'center',
    justifyContent: 'center',
    transform: [{ rotate: '90deg' }],
    borderWidth: 1,
    borderColor: MediumGrey,
    alignSelf: 'center'
  },
  backImage: {
    width: "50%", height: "70%", tintColor: Red, resizeMode: "contain", alignSelf: 'center',
  },
  camImage: {
    width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ rotate: '90deg' }]
  },
  camImage1: {
    width: "50%", height: "60%", tintColor: White, resizeMode: "contain", alignSelf: 'center', transform: [{ scaleX: -1 }]
  },
});

// import React from 'react';
// import { View, Text, Button } from 'react-native';
// import { copilot, CopilotStep, walkthroughable } from 'react-native-copilot';


// const MyComponent = (props) => {
//   const WalkthroughableText = walkthroughable(Text);

//   useEffect(() => {
//     props.copilotEvents.on();
//     props.start();
//   }, []);


//   const handleStepChange = (step) => {
//     console.log(`step change:${step.name}`);
//   }
//   return (
//     <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
//       <CopilotStep
//         text="This is a simple text element"
//         order={1}
//         name="firstStep"
//       >
//         <WalkthroughableText style={{ fontSize: 24 }}>
//           Hello, this is a tutorial!
//         </WalkthroughableText>
//       </CopilotStep>

//       <CopilotStep
//         text="This button will trigger an action"
//         order={2}
//         name="secondStep"
//       >
//         <WalkthroughableText>
//           <Button title="Press me" onPress={() => { }} />
//         </WalkthroughableText>
//       </CopilotStep>
//     </View>
//   );
// };

// export default copilot({
//   overlay: 'svg',
//   animated: true
// })(MyComponent);

import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, I18nManager, Platform } from 'react-native';
import { Red, White, WhiteGery, WhiteGreen, appFontBold, screenHeight, screenWidth } from './Styles';
import { strings } from '../screens/i18n';
import AsyncStorage from '@react-native-async-storage/async-storage';

const MyComponent = ({
  isFirstStep,
  isLastStep,
  handleNext,
  handlePrev,
  handleStop,
  currentStep,
}) => {

  const Finish = () => {
    AsyncStorage.setItem("intro", "Done")

  }
  return (
    <View style={styles.tooltipContainer}>
      <Text style={styles.tooltipText}>{currentStep.text}</Text>
      <View style={styles.buttonContainer}>
        <TouchableOpacity onPress={handleStop} style={[styles.button, {
          backgroundColor: WhiteGery
        }]}>
          <Text style={[styles.buttonText, { color: Red }]}>{strings('lang.Skip')}</Text>
        </TouchableOpacity>
        {/* {!isFirstStep && (
        <TouchableOpacity onPress={handleNext} style={[styles.button, {
          backgroundColor: WhiteGery
        }]}>
          <Text style={[styles.buttonText, { color: Red }]}>Back</Text>
        </TouchableOpacity>
      )} */}
        {isLastStep ? (
          <TouchableOpacity onPress={handleStop} style={[styles.button, {
            backgroundColor: Red
          }]}>
            <Text style={[styles.buttonText, { color: White }]}>{strings('lang.ending')}</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={handleNext} style={[styles.button, {
            backgroundColor: Red,
          }]}>
            <Text style={[styles.buttonText, { color: White }]}>{strings('lang.next')}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  )
};
export default MyComponent;

const styles = StyleSheet.create({
  tooltipContainer: {
    paddingVertical: 15,
    borderRadius: 5,
    width: screenWidth / 2,
  },
  tooltipText: {
    color: Red,
    fontSize: screenWidth / 30,
    fontFamily: appFontBold,
    marginBottom: 20,
    textAlign: 'center'
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    padding: 10,
    backgroundColor: '#007bff',
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 20,
    height: Platform.OS == 'ios' ? screenHeight / 22 : screenHeight / 20,
    width: '40%',

    // marginHorizontal: 10
  },
  buttonText: {
    color: Red,
    fontSize: screenWidth / 35,
    fontFamily: appFontBold,
  },
});
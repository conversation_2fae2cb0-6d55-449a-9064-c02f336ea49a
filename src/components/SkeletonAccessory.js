import React, { useEffect, useRef } from "react";
import {
    View,
    Text,
    StyleSheet,
    Dimensions,
} from "react-native";

const { width } = Dimensions.get("window");
import { DarkG<PERSON>, MediumGrey, screenWidth, <PERSON>, WhiteGery } from "./Styles";
import SkeletonPlaceholder from "react-native-skeleton-placeholder";


const SkeletonAccessory = (props) => {
    return (
        // <ContentLoader
        //     speed={1}
        //     width={screenWidth / 2.3}
        //     height={220}
        //     viewBox="0 0 180 220"
        //     backgroundColor="#f5f5f5"
        //     foregroundColor="#707070"
        // >
        //     <Rect x="2" y="10" rx="3" ry="3" width={screenWidth / 2.3} height="118" />
        //     <Rect x="130" y="145" rx="3" ry="3" width={screenWidth / 5} height="11" />
        //     <Rect x="-23" y="172" rx="3" ry="3" width={screenWidth / 1.5} height="6" />
        //     <Rect x="-5" y="193" rx="3" ry="3" width={screenWidth / 1.5} height="6" />
        //     <Rect x="-21" y="228" rx="3" ry="3" width={screenWidth / 1.5} height="6" />
        // </ContentLoader>
        <View
            style={{
                backgroundColor: White,
                width: screenWidth / 2.3,
                height: 180,
                flexDirection: 'column',
                borderColor: MediumGrey,
                borderWidth: 1,
                borderRadius: 8,
                marginBottom: 10,
                overflow: 'hidden',
            }}
        >
            <SkeletonPlaceholder highlightColor={MediumGrey} backgroundColor={WhiteGery} speed={1200}>
                <View style={{ width: '100%', height: 120, backgroundColor: WhiteGery, marginBottom: 10 }} />
                <View style={{ width: '40%', height: 20, backgroundColor: WhiteGery, marginBottom: 10 }} />
                <View style={{ width: '100%', height: 10, backgroundColor: WhiteGery, marginBottom: 10 }} />
                <View style={{ width: '100%', height: 10, backgroundColor: WhiteGery, marginBottom: 5 }} />
            </SkeletonPlaceholder>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
});

export default SkeletonAccessory;

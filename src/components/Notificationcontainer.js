import React, { useState } from 'react';
import { View, StyleSheet, Image, Text, Modal, I18nManager } from "react-native";
import { TouchableOpacity, TouchableWithoutFeedback } from 'react-native-gesture-handler';
import { appFont, White, screenWidth, screenHeight, DarkBlue, appFontBold, DarkGrey, Red, Green, DarkRed, DarkGreen, WhiteYellow, Grey, Black, appColor, Blue, MediumGrey, WhiteGery, appColor1 } from './Styles';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { strings } from '../screens/i18n';

const NotificationContainer = props => {


    return (
        <View style={props.active == true ? styles.containerActive : styles.container}>

            <TouchableOpacity onPress={props.onPress} style={{ flexDirection: 'row' }} >
                <View style={{ width: '20%', alignItems: 'center', paddingTop: '5%' }}>
                    <View style={{ width: screenWidth / 8, height: screenWidth / 8, borderRadius: 100, backgroundColor: WhiteGery, alignItems: 'center', justifyContent: 'center' }}>
                        <Image style={{ resizeMode: 'contain', width: '50%', height: '50%', tintColor: Red }} source={require('../images/bell.png')} />
                    </View>
                </View>
                <View style={{ width: '79%', flexDirection: 'column' }}>
                    <View style={{ width: '100%', paddingVertical: '5%', alignItems: 'flex-start', justifyContent: "center" }}>
                        <Text style={{ fontFamily: appFontBold, color: DarkGrey, fontSize: screenWidth / 30, alignSelf: 'flex-start', textAlign: I18nManager.isRTL ? 'left' : 'right' }}>
                            Lorem ipsum dolor sit amet consectetur adipisicing elit. Maxime mollitia,
                        </Text>
                    </View>
                </View>
            </TouchableOpacity>
        </View>
    )

};

export default NotificationContainer;
const styles = StyleSheet.create({
    container: {
        backgroundColor: White,
        width: '100%',
        // height: screenWidth / 4,
        alignSelf: 'center',
        flexDirection: 'row',
        borderBottomWidth: 1,
        height: screenWidth / 4.5,
        borderColor: MediumGrey
    },
    containerActive: {
        backgroundColor: White,
        width: '100%',
        height: screenWidth / 4.5,
        alignSelf: 'center',
        flexDirection: 'row',
        borderBottomWidth: 1,
        borderColor: DarkBlue
    },
    modal: {
        flex: 1,
        alignItems: 'center',
        width: screenWidth / 1.3,
        height: screenHeight / 3,
        position: 'absolute',
        top: screenHeight / 3.2,
        alignSelf: 'center',
        backgroundColor: White,
        borderRadius: 10
    },
    text: {
        fontFamily: appFontBold,
        color: DarkBlue,
        fontSize: screenWidth / 22,
        marginTop: '4%'
    },
    buttonContainer: {
        width: screenWidth / 4,
        height: '65%',
        backgroundColor: DarkBlue,
        alignSelf: 'center',
        marginHorizontal: 5,
        justifyContent: 'center',
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        marginTop: 7,
    },
    buttonContainer2: {
        width: screenWidth / 4,
        height: '65%',
        backgroundColor: '#fff',
        borderWidth: 1,
        borderColor: DarkBlue,
        alignSelf: 'center',
        justifyContent: 'center',
        marginHorizontal: 5,
        borderRadius: 10,
        alignItems: 'center',
        padding: 12,
        // marginTop: 10,
    },
    buttonText: {
        color: White,
        fontFamily: appFontBold,
        fontSize: screenWidth / 28
    },
    buttonText2: {
        color: DarkBlue, marginVertical: 5,
        alignSelf: 'center', fontSize: screenWidth / 25, fontFamily: 'Cairo-Bold'
    },
    icon: { resizeMode: 'contain', width: '90%', height: '55%', },
    modelIconContainer: { width: '15%', height: '100%', justifyContent: 'center' },
    modelTextContainer: { width: '85%', height: '100%', justifyContent: 'center' },
    modelText: { fontFamily: appFontBold, color: DarkBlue, fontSize: screenWidth / 25, },

});



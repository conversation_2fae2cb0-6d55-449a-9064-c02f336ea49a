
import { LOGOUT, AUTHENTICATE, UPDATE_PROFILE } from '../Actions/auth'

const initialState = {
    user: {},
    token: ''
};

export default (state = initialState, action) => {
    switch (action.type) {
        case UPDATE_PROFILE:
            return {
                ...state,
                user: action.user,
            };
        case AUTHENTICATE:
            return {
                ...state,
                token: action.token,
            };
        case LOGOUT:
            return {
                ...state,
            };
        default:
            return state;
    }
};

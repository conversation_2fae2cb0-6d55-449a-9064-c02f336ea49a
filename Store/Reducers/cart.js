import { TIMER, CART, <PERSON><PERSON>ARCART, TIMERCART } from '../Actions/cart'

const initialState = {
    cart: {
        items: []
    },
    mins: 9,
    secs: 59,
    minsCart: 0,
    secsCart: 59
};

export default (state = initialState, action) => {
    switch (action.type) {
        case CLEARCART:
            return {
                ...state,
                cart: {
                    items: []
                },
            };
        case CART:
            return {
                ...state,
                cart: action.cart,
            };
        case TIMER:
            return {
                ...state,
                mins: action.mins,
                secs: action.secs,
            };
        case TIMERCART:
            return {
                ...state,
                minsCart: action.minsCart,
                secsCart: action.secsCart,
            };
        default:
            return state;
    }
};

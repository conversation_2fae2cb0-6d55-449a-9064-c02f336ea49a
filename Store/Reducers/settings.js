import { SETTINGS, SPLASH } from "../Actions/settings";

const initialState = {
    settings: {},
    statuses: [],
    whatssApp: ''
};

export default (state = initialState, action) => {
    switch (action.type) {
        case SETTINGS:
            return {
                ...state,
                settings: action.settings,
            };
        case SPLASH:
            return {
                ...state,
                statuses: action.splash.statuses,
                whatssApp: action.splash.settings.whatsapp,
            };
        default:
            return state;
    }
};
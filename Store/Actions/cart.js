import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as CONSTANT from '../../src/Constant/constants';

export const TIMER = 'TIMER';
export const TIMERCART = 'TIMERCART';
export const CART = 'CART';
export const CLEARCART = 'CLEARCART';

export const timer = (mins, secs) => {
    return { type: TIMER, mins: mins, secs: secs }
};
export const timerCart = (minsCart, secsCart) => {
    return { type: TIMERCART, minsCart: minsCart, secsCart: secsCart }
};

export const getCart = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/current';
        // var url = CONSTANT.BaseUrl + 'cart';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getCart response', response)
                if (response.data) {
                    dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const addItem = (id, sharing_type_id) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('sheep_id', id);
        urlencoded.append('sharing_type_id', sharing_type_id);
        // urlencoded.append('item_id', id);
        console.log('addItem urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/add-sheep';
        // var url = CONSTANT.BaseUrl + 'cart/add-item';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('addItem response', response)
                if (response.data) {
                    dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const removeItem = (id) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('sheep_id', id);
        console.log('removeItem urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/remove-sheep';
        // var url = CONSTANT.BaseUrl + 'cart/remove-item';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('removeItem response', response)
                if (response.data) {
                    dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const clearCart = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/clear';
        // var url = CONSTANT.BaseUrl + 'cart/clear';
        console.log(url)
        return fetch(url, {
            method: 'POST',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('clearCart response', response)
                dispatch({ type: CLEARCART });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const clearPaymentType = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'orders/clear-payment-type';
        // var url = CONSTANT.BaseUrl + 'cart/clear';
        console.log(url)
        return fetch(url, {
            method: 'POST',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('clearPaymentType response', response)
                if (response.data) {
                    dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectPaymentType = (paymentType, partnerPhone) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('payment_type', paymentType == '2' ? 'Shared' : 'Full');
        // if (paymentType == '2') {
        urlencoded.append('partner_phone', partnerPhone);
        // }
        console.log('selectOrderTime urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/select-payment-type';
        // var url = CONSTANT.BaseUrl + 'cart/select-order-time';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                if (response.data) {
                    dispatch({ type: CART, cart: response.data });
                }
                console.log('selectOrderTime response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const addCoupon = (code) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();
        urlencoded.append('code', code);
        console.log('addCoupon urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/add-coupon';
        // var url = CONSTANT.BaseUrl + 'cart/add-coupon';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('addCoupon response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const removeCoupon = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');

        var url = CONSTANT.BaseUrl + 'orders/remove-coupon';
        // var url = CONSTANT.BaseUrl + 'cart/remove-coupon';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            // body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('removeCoupon response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectOptions = (DATA) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('item_id', DATA.item_id);
        urlencoded.append('item_type', DATA.item_type);
        DATA.options.map((item, index) => {
            urlencoded.append(`options[${index}][id]`, item.id);
            urlencoded.append(`options[${index}][type]`, item.type);
        })
        console.log('selectOptions urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/select-options';
        // var url = CONSTANT.BaseUrl + 'cart/select-options';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            // body: JSON.stringify(DATA),
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectOptions response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectPartnerOptions = (DATA, orderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('item_id', DATA.item_id);
        urlencoded.append('item_type', DATA.item_type);
        DATA.options.map((item, index) => {
            urlencoded.append(`options[${index}][id]`, item.id);
            urlencoded.append(`options[${index}][type]`, item.type);
        })
        console.log('selectOptions urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/' + orderId + '/partner-options';
        // var url = CONSTANT.BaseUrl + 'cart/select-options';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            // body: JSON.stringify(DATA),
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectPartnerOptions response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectOrderTime = (orderTimeId, delivery_type) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('order_time_id', orderTimeId);
        urlencoded.append('delivery_type', delivery_type);
        console.log('selectOrderTime urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/select-order-time';
        // var url = CONSTANT.BaseUrl + 'cart/select-order-time';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectOrderTime response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectAddress = (addressId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();
        urlencoded.append('address_id', addressId);
        console.log('selectAddress urlencoded data', urlencoded);
        console.log('url', url);
        var url;


        url = CONSTANT.BaseUrl + 'orders/select-address';
        // var url = CONSTANT.BaseUrl + 'cart/select-address';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectAddress response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectPartnerAddress = (addressId, orderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();
        urlencoded.append('address_id', addressId);
        console.log('selectPartnerAddress urlencoded data', urlencoded);
        console.log('selectPartnerAddress orderId', orderId);
        console.log('url', url);
        var url;
        url = CONSTANT.BaseUrl + 'orders/' + orderId + '/partner-address';

        // var url = CONSTANT.BaseUrl + 'cart/select-address';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectPartnerAddress response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectDeliveryTime = (deliveryTime) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('delivery_time', deliveryTime);
        console.log('selectDeliveryTime urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/select-delivery-time';
        // var url = CONSTANT.BaseUrl + 'cart/select-delivery-time';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectDeliveryTime response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const selectPaymentMethod = (paymentMethodId, shared, orderId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('payment_method_id', paymentMethodId);
        console.log('selectPaymentMethod urlencoded data', urlencoded);
        var url;
        if (shared == true && orderId) {
            url = CONSTANT.BaseUrl + 'orders/' + orderId + '/partner-payment-method';
        }
        else {
            url = CONSTANT.BaseUrl + 'orders/select-payment-method';
        }

        // var url = CONSTANT.BaseUrl + 'cart/select-payment-method';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('selectPaymentMethod response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const addNote = (note) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();
        urlencoded.append('notes', note);
        console.log('addNote urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'orders/add-notes';
        // var url = CONSTANT.BaseUrl + 'cart/add-note';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('addNote response', response)
                if (response.data) {
                    // dispatch({ type: CART, cart: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

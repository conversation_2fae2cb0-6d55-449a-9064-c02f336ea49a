import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as CONSTANT from '../../src/Constant/constants';
export const ADDRESSES = 'ADDRESSES';


export const createAddresss = (DATA) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('lat', DATA.lat);
        urlencoded.append('lng', DATA.lng);
        urlencoded.append('address', DATA.address);
        urlencoded.append('phone', DATA.phone);
        urlencoded.append('building', DATA.building);
        urlencoded.append('floor', DATA.floor);
        urlencoded.append('flat', DATA.apartment);
        urlencoded.append('landmark', DATA.specialMark);
        console.log('Login urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'addresses';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('response', response)
                // dispatch({ type: ADDRESSES, addresses: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const editAddresss = (DATA) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        const urlencoded = new URLSearchParams();
        urlencoded.append('lat', DATA.lat);
        urlencoded.append('lng', DATA.lng);
        urlencoded.append('city_id', DATA.cityId);
        urlencoded.append('phone', DATA.phone);
        urlencoded.append('building', DATA.building);
        urlencoded.append('floor', DATA.floor);
        urlencoded.append('flat', DATA.apartment);
        urlencoded.append('landmark', DATA.specialMark);
        console.log('Login urlencoded data', urlencoded);

        var url = CONSTANT.BaseUrl + 'addresses/' + DATA.ID;
        console.log(url);
        return fetch(url, {
            method: 'POST',
            body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('response', response)
                dispatch({ type: ADDRESSES, addresses: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getAddresses = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'addresses';
        console.log("addresses url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('addresses response', response)
                dispatch({ type: ADDRESSES, addresses: response.data });
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const deleteAddress = (addressId) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');

        var url = CONSTANT.BaseUrl + 'addresses/' + addressId + '/delete';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            // body: urlencoded.toString(),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const getAreas = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'areas';
        console.log("areas url", url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('areas response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};
import { ImagePropTypes } from 'react-native';
import { Alert } from 'react-native';
import { Item } from 'native-base';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as CONSTANT from '../../src/Constant/constants';
import { useState } from 'react';

// const [listSheep, setListSheep] = useState([]);
export const TODAY = 'TODAY';

export const getToday = () => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        let lan = await AsyncStorage.getItem('lan');
        var url = CONSTANT.BaseUrl + 'move-sheep-requests/today';
        console.log(url)
        return fetch(url, {
            method: 'GET',
            // body:JSON.stringify({
            // }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('getToday response', response)
                if (response.data) {
                    dispatch({ type: TODAY, today: response.data });
                }
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};

export const store = (farm_id) => {
    return async dispatch => {
        let token = await AsyncStorage.getItem('token');
        const urlencoded = new URLSearchParams();

        // await ids.map((item, index) => {
        //     urlencoded.append(`ids[${index}]`, item);
        // })

        urlencoded.append('farm_id', farm_id);

        console.log('store data', JSON.stringify({
            farm_id: farm_id
        }));

        var url = CONSTANT.BaseUrl + 'move-sheep-requests';
        console.log(url);
        return fetch(url, {
            method: 'POST',
            // body: urlencoded.toString(),
            body: JSON.stringify({
                farm_id: farm_id
            }),
            headers: {
                'Authorization': 'Bearer ' + JSON.parse(token),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        }).then(res => res.json())
            .then(response => {
                console.log('store response', response)
                return response
            })
            .catch(error => {
                console.log('error', error)
            });
    };
};







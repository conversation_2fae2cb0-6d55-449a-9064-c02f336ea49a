import { createStore, combineReducers } from 'redux';
import addressesReducer from '../Store/Reducers/addresses';
import authReducer from '../Store/Reducers/auth';
import General from '../Store/Reducers/general';

const rootReducer = combineReducers(
    {
        auth: authReducer,
        addresses: addressesReducer,
        General: General,
    }
);

const configureStore = () => {
    return createStore(rootReducer);
}

export default configureStore;
#!/bin/bash

# <PERSON><PERSON>t to fix React Native dependency resolution issues

echo "Fixing React Native dependency versions..."

# Function to replace dynamic React Native dependency with specific version
fix_rn_dependency() {
    local file="$1"
    
    if [ -f "$file" ]; then
        echo "Fixing React Native dependency in $file"
        # Replace dynamic version with specific version
        sed -i.bak 's/com\.facebook\.react:react-native:\+/com.facebook.react:react-native:0.72.12/g' "$file"
        sed -i.bak 's/com\.facebook\.react:react-native:\*/com.facebook.react:react-native:0.72.12/g' "$file"
    fi
}

# Fix all React Native package build.gradle files
find node_modules -name "build.gradle" -path "*/android/*" | while read file; do
    fix_rn_dependency "$file"
done

echo "React Native dependency fixes completed!"

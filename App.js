import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import React, { useCallback, useEffect } from 'react';
import { Provider, useDispatch } from 'react-redux';
import { applyMiddleware, combineReducers, createStore } from 'redux';
import ReduxThunk from 'redux-thunk';




import AddressDetails from './src/screens/AddressDetails';
import Addresses from './src/screens/Addresses';
import ArticleDetails from './src/screens/ArticleDetails';
import Cart from './src/screens/Cart';
import ChangePassword from './src/screens/ChangePassword';
import ComplaintsAndSuggestions from './src/screens/ComplaintsAndSuggestions';
import CompleteOrder0 from './src/screens/CompleteOrder0';
import CompleteOrder1 from './src/screens/CompleteOrder1';
import CompleteOrder2 from './src/screens/CompleteOrder2';
import CompleteOrder3 from './src/screens/CompleteOrder3';
import ConfirmationCode from './src/screens/ConfirmationCode';
import Favourites from './src/screens/Favourites';
import ForgetPassword from './src/screens/ForgetPassword';
import ForgetPasswordChange from './src/screens/ForgetPasswordChange';
import Home from './src/screens/Home';
import KnowMore from './src/screens/KnowMore';
import Live from './src/screens/Live';
import Login from './src/screens/Login';
import Map from './src/screens/Map';
import More from './src/screens/More';
import MoveOrderToSlaughter from './src/screens/MoveOrderToSlaughter';
import MyOrders from './src/screens/MyOrders';
import MyProfile from './src/screens/MyProfile';
import Notifications from './src/screens/Notifications';
import OrderTracking from './src/screens/OrderTracking';
import PartnershipOrders from './src/screens/PartnershipOrders';
import PartnershipOrdersDetails from './src/screens/PartnershipOrdersDetails';
import Regestration from './src/screens/Regestration';
import Splash from './src/screens/Splash';
import TermsAndConditions from './src/screens/TermsAndConditions';
import Test from './src/screens/Test';
import TraderAllContent from './src/screens/TraderAllContent';
import TwistingInTheMarket from './src/screens/TwistingInTheMarket';


import {
  Alert,
  Linking,
  LogBox,
  PermissionsAndroid,
  Platform,
  StatusBar,
  StyleSheet,
  useColorScheme
} from 'react-native';

import AsyncStorage from '@react-native-async-storage/async-storage';
import PushNotificationIOS from '@react-native-community/push-notification-ios';
import messaging from '@react-native-firebase/messaging';
import { Root } from 'native-base';
import appsFlyer from 'react-native-appsflyer';
import PushNotification from 'react-native-push-notification';
import Sound from 'react-native-sound';
import {
  Colors
} from 'react-native/Libraries/NewAppScreen';
import NotificationController from './NotificationController';
import * as cartActions from './Store/Actions/cart';
import addresses from './Store/Reducers/addresses';
import auth from './Store/Reducers/auth';
import cart from './Store/Reducers/cart';
import farms from './Store/Reducers/farms';
import moveSheepRequests from './Store/Reducers/moveSheepRequests';
import orders from './Store/Reducers/orders';
import settings from './Store/Reducers/settings';
import { MediumGrey, Red, Red1 } from './src/components/Styles';
import Toaster from './src/components/Toaster';
import LiveModal2 from './src/screens/LiveModal2';
import OrderDetails from './src/screens/OrderDetails';
import PaymentWebView from './src/screens/PaymentWebView';


const Stack = createStackNavigator();


function MyStack() {
  useEffect(() => {
    appsFlyer.initSdk(
      {
        devKey: 'GWVV27jYxtZAuWiNCbPZ2i',
        isDebug: true,
        appId: '605931374', // Only required for iOS
      },
      (result) => {
        console.log('AppsFlyer initialized:', result);
      },
      (error) => {
        console.error('AppsFlyer init failed:', error);
      }
    );


    // let sound = new Sound('notificationsound.mp3', Sound.MAIN_BUNDLE, (error) => {
    //   if (error) {
    //     console.log('failed to load the sound', error);
    //   } else {
    //     sound.play(); // have to put the call to play() in the onload callback
    //   }
    // });
    // sound.play((success) => {
    //   if (success) {
    //     console.log('Sound played successfully');
    //   } else {
    //     console.error('Failed to play the sound');
    //   }
    // });

    // const getFCMToken = async () => {
    //   try {
    //     const authorized = await firebase.messaging().hasPermission();
    //     const fcmToken = await getToken();

    //     if (authorized) return fcmToken;
    //     console.log('fcmToken', fcmToken);
    //     await firebase.messaging().requestPermission();
    //     return fcmToken;
    //   } catch (error) {
    //     console.log(error);
    //   }
    // };

    // getFCMToken()

    const notifications = async () => {
      messaging().onNotificationOpenedApp(remoteMessage => {
        console.log(
          'Notification caused app to open from background state:',
          remoteMessage.notification,
        );
        // navigation.navigate(remoteMessage.data.type);
      });

      // Check whether an initial notification is available
      messaging()
        .getInitialNotification()
        .then(remoteMessage => {
          if (remoteMessage) {
            console.log(
              'Notification caused app to open from quit state:',
              remoteMessage.notification,
            );
            // setInitialRoute(remoteMessage.data.type); // e.g. "Settings"
          }
        });
    }
    // const getFCMToken = async () => {
    //   try {
    //     const authorized = await firebase.messaging().hasPermission();
    //     const fcmToken = await getToken();

    //     if (authorized) return fcmToken;
    //     console.log('fcmToken', fcmToken);
    //     await firebase.messaging().requestPermission();
    //     return fcmToken;
    //   } catch (error) {
    //     console.log(error);
    //   }
    // };
    // getFCMToken()

    // const getToken = async () => {
    //   let tokenn = await AsyncStorage.getItem('token');
    //   if (tokenn) {
    //     await messaging().registerDeviceForRemoteMessages();
    //     const token = await messaging().getToken();
    //     console.log('getToken', token);
    //   }
    // }

    const requestUserPermission = async () => {
      console.log('requestUserPermission');

      if (Platform.OS === "android") {
        console.log('permission android');
        try {
          PermissionsAndroid.check('android.permission.POST_NOTIFICATIONS').then(
            // response => {
            //   console.log('notification response:', response);
            //   if (!response) {
            PermissionsAndroid.request('android.permission.POST_NOTIFICATIONS', {
              title: 'Notification',
              message:
                'Step needs access to your notification',
              buttonNeutral: 'Ask Me Later',
              buttonNegative: 'Cancel',
              buttonPositive: 'OK',
            })
            //   }
            // }
          ).catch(
            err => {
              console.log("Notification Error=====>", err);
            }
          )
        } catch (err) {
          console.log(err);
        }
      }
      else {
        console.log('permission ios');

        PushNotificationIOS.requestPermissions();
        const authStatus = await messaging().requestPermission();
        const enabled =
          authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
          authStatus === messaging.AuthorizationStatus.PROVISIONAL;

        if (enabled) {
          console.log('Authorization status:', authStatus);
        }
      }
    }



    notifications();
    // getToken();
    requestUserPermission();


    // const requestUserPermission = async () => {
    //   const authStatus = await messaging().requestPermission();
    //   const enabled =
    //     authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    //     authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    //   if (enabled) {
    //     console.log('Authorization status:', authStatus);
    //   }
    // };



    PushNotification.createChannel(
      {
        channelId: "channel-id", // (required)
        channelName: "My channel", // (required)
        playSound: true, // (optional) default: true
        vibrate: true, // (optional) default: true. Creates the default vibration pattern if true.

      },
      (created) => console.log(`createChannel returned '${created}'`) // (optional) callback returns whether the channel was created, false means it already existed.
    );

    if (Platform.OS == 'ios') {
      console.log('ios');
      PushNotification.configure({
        onRegister: function (token) {
          console.log("TOKEN:", token);
        },
        onNotification: function (notification) {
          console.log("NOTIFICATION:", notification);
          // Process the notification
          notification.finish(PushNotificationIOS.FetchResult.NoData);
        },
        // senderID: "YOUR_GCM_SENDER_ID",
        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },
        popInitialNotification: true,
        requestPermissions: true,
      });
      // PushNotification.localNotification({
      //   alertTitle: remoteMessage.notification.title,
      //   alertBody: remoteMessage.notification.body,
      // });

    }

    const unsubscribe = messaging().onMessage(async remoteMessage => {

      let sound = new Sound('notificationsound.mp3', Sound.MAIN_BUNDLE, (error) => {
        if (error) {
          console.log('failed to load the sound', error);
        } else {
          sound.play(); // have to put the call to play() in the onload callback
        }
      });
      sound.play((success) => {
        if (success) {
          console.log('Sound played successfully');
        } else {
          console.error('Failed to play the sound');
        }
      });

      console.log('A new FCM message arrived!', JSON.stringify(remoteMessage));
      if (Platform.OS == 'ios') {
        console.log('ios');
        // PushNotification.localNotification({
        //   alertTitle: remoteMessage.notification.title,
        //   alertBody: remoteMessage.notification.body,
        // });
        Alert.alert(
          'New Notification',
          `${remoteMessage.notification.title}\n${remoteMessage.notification.body}`,
          [
            { text: 'OK', onPress: () => console.log('OK Pressed') }
          ],
          { cancelable: true }
        );
      }
      else {
        PushNotification.localNotification({
          channelId: 'channel-id',
          title: remoteMessage.notification.title, // (optional)
          message: remoteMessage.notification.body, // (required)
          playSound: true, // (optional) default: true
        });
      }

    });

    return unsubscribe;
  }, []);

  const dispatch = useDispatch()

  useEffect(() => {

    const getCart = async () => {
      try {
        // setLoading(true)
        let token = await AsyncStorage.getItem('token')
        if (token) {
          let response = await dispatch(cartActions.getCart());
          if (response.success == true) {
          } else {
            if (response.message) {
              Toaster(
                'top',
                'danger',
                Red1,
                response.message,
                White,
                1500,
                screenHeight / 15,
              );
            }
          }
        }
        // setLoading(false);
      } catch (err) {
        console.log('err', err)
        // setLoading(false);
      }
    };

    const interval = setInterval(() => {
      getCart()
    }, 30000); // 60000ms = 1 minute

    // // Cleanup interval on component unmount
    return () => clearInterval(interval);
  }, []);
  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
      }}
      initialRouteName={'Splash'}
    >
      <Stack.Screen name="Splash" component={Splash} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Login" component={Login} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Home" component={Home} options={{ gestureEnabled: false }} />
      <Stack.Screen name="Live" component={Live} />
      <Stack.Screen name="Regestration" component={Regestration} />
      <Stack.Screen name="ConfirmationCode" component={ConfirmationCode} />
      <Stack.Screen name="Cart" component={Cart} options={{ gestureEnabled: false, }} />
      <Stack.Screen name="CompleteOrder0" component={CompleteOrder0} />
      <Stack.Screen name="CompleteOrder1" component={CompleteOrder1} />
      <Stack.Screen name="CompleteOrder2" component={CompleteOrder2} />
      <Stack.Screen name="CompleteOrder3" component={CompleteOrder3} />
      <Stack.Screen name="Map" component={Map} />
      <Stack.Screen name="AddressDetails" component={AddressDetails} />
      <Stack.Screen name="Addresses" component={Addresses} />
      <Stack.Screen name="TermsAndConditions" component={TermsAndConditions} />
      <Stack.Screen name="More" component={More} />
      <Stack.Screen name="Favourites" component={Favourites} />
      <Stack.Screen name="PartnershipOrders" component={PartnershipOrders} />
      <Stack.Screen name="PartnershipOrdersDetails" component={PartnershipOrdersDetails} />
      <Stack.Screen name="OrderTracking" component={OrderTracking} />
      <Stack.Screen name="MoveOrderToSlaughter" component={MoveOrderToSlaughter} />
      <Stack.Screen name="MyOrders" component={MyOrders} />
      <Stack.Screen name="ChangePassword" component={ChangePassword} />
      <Stack.Screen name="KnowMore" component={KnowMore} />
      <Stack.Screen name="ArticleDetails" component={ArticleDetails} />
      <Stack.Screen name="MyProfile" component={MyProfile} />
      <Stack.Screen name="Notifications" component={Notifications} />
      <Stack.Screen name="ForgetPassword" component={ForgetPassword} />
      <Stack.Screen name="ForgetPasswordChange" component={ForgetPasswordChange} />
      <Stack.Screen name="ComplaintsAndSuggestions" component={ComplaintsAndSuggestions} />
      <Stack.Screen name="Test" component={Test} />
      <Stack.Screen name="TraderAllContent" component={TraderAllContent} />
      <Stack.Screen name="TwistingInTheMarket" component={TwistingInTheMarket} />
      <Stack.Screen name="PaymentWebView" component={PaymentWebView} />
      <Stack.Screen name="LiveModal2" component={LiveModal2} />
      <Stack.Screen name="OrderDetails" component={OrderDetails} />

    </Stack.Navigator>
  );
}

const rootReducer = combineReducers({
  auth: auth,
  farms: farms,
  addresses: addresses,
  cart: cart,
  orders: orders,
  settings: settings,
  moveSheepRequests: moveSheepRequests,
});

const store = createStore(rootReducer, applyMiddleware(ReduxThunk));

const App = (props) => {

  const isDarkMode = useColorScheme() === 'dark';
  // const dispatch = useDispatch();
  LogBox.ignoreLogs(['Warning: ...']); // Ignore log notification by message
  LogBox.ignoreAllLogs();//Ignore all log notifications

  useEffect(() => {
    Linking.addEventListener("url", handleOpenURL);
    return () => {
      Linking.removeEventListener("url", handleOpenURL);
    };
  }, [handleOpenURL]);


  function navigate(url, data) {

    props.navigation.push('TraderAllContent', { trader_id: data, farms: [] })
  }


  const handleOpenURL = useCallback(async (url) => {

    let token = await AsyncStorage.getItem('token')
    console.log('urlurl1', url.url)
    console.log('token', token);
    if (url.url && token) {
      let i = url.url.split("/");
      console.log('asddsa', i);
      // dispatch(authActions.authenticate(JSON.parse(token)));
      // let response = await dispatch(farmsActions.getTraderFarms(i[4]));
      // if (response.success == true) {
      // tokenExisted = true
      navigate(url.url, i[4]);
      // }
      // else {
      // }
    }
  }, []);

  const backgroundStyle = {
    backgroundColor: isDarkMode ? Colors.darker : Colors.lighter,
  };

  return (
    <Root>
      <NotificationController />
      <StatusBar
        backgroundColor={Red}
        barStyle="light-content"
      />
      <Provider store={store}>
        <NavigationContainer>
          <MyStack />
        </NavigationContainer>
      </Provider>
    </Root>
  );
};

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 32,
    paddingHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: '600',
  },
  sectionDescription: {
    marginTop: 8,
    fontSize: 18,
    fontWeight: '400',
  },
  highlight: {
    fontWeight: '700',
  },
});

export default App;

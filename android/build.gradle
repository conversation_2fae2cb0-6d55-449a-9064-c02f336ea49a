import org.apache.tools.ant.taskdefs.condition.Os

// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 35
        googlePlayServicesVersion = '+'// default: “+”
        firebaseMessagingVersion = '23.2.1'
        firebaseVersion = '32.2.2'
        kotlinVersion = '1.8.0'
        // ndkVersion = "20.1.5948944"
        androidXCore = "1.10.1"
        if (System.properties['os.arch'] == "aarch64") {
            // For M1 Users we need to use the NDK 24 which added support for aarch64
        ndkVersion = "26.3.11579264"
        } else {
            // Otherwise we default to the side-by-side NDK version from AGP.
            ndkVersion = "26.3.11579264"
        }
    }
    repositories {
        google()
        mavenCentral()
        jcenter()
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.4")
        classpath("de.undercouch:gradle-download-task:5.0.1")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
        classpath 'com.google.gms:google-services:4.3.15'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        exclusiveContent {
           filter {
               includeGroup "com.facebook.react"
           }
           forRepository {
               maven {
                   url "$rootDir/../node_modules/react-native/android"
               }
           }
       }
        maven {
            // All of React Native (JS, Obj-C sources, Android binaries) is installed from npm
            url("$rootDir/../node_modules/react-native/android")
        }
        maven {
            // Android JSC is installed from npm
            url("$rootDir/../node_modules/jsc-android/dist")
        }
        google()
        mavenCentral {
            // We don't want to fetch react-native from Maven Central as there are
            // older versions over there.
            content {
                excludeGroup "com.facebook.react"
            }
        }
        maven { url 'https://www.jitpack.io' }
    }
}

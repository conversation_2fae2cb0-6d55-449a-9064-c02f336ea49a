package com.ModrkClient;
import android.util.Log;

import androidx.annotation.NonNull;

import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.google.android.exoplayer2.ExoPlayer;

import java.util.Map;
import java.util.HashMap;

public class LiveModule extends ReactContextBaseJavaModule {
    ExoPlayer player;
    String videoUrl ="rtsp://admin:<EMAIL>:4444/cam/realmonitor?channel=13&subtype=1";

    LiveModule(ReactApplicationContext context) {
        super(context);

    }


    @NonNull
    @Override
    public String getName() {
        return "LiveModule";
    }

    @ReactMethod
    public  void createLiveEvent(){
        Log.d("Live Module","Logged from Live Module");
    }
}


{"project_info": {"project_number": "795262046849", "project_id": "modrk-f6717", "storage_bucket": "modrk-f6717.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:795262046849:android:7cbae0c9fa00a301bcb380", "android_client_info": {"package_name": "com.ModrkClient"}}, "oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDNKYVTQvRGMGHA9IP9zBb_Wx9hqc-TI4M"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}, {"client_id": "795262046849-1k1aoqm5c2oofeaistf8kalkdnlcc42u.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "org.reactjs.native.example.ModrkProduction"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:795262046849:android:6564f3ad7e073111bcb380", "android_client_info": {"package_name": "com.ModrkDelivery"}}, "oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDNKYVTQvRGMGHA9IP9zBb_Wx9hqc-TI4M"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}, {"client_id": "795262046849-1k1aoqm5c2oofeaistf8kalkdnlcc42u.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "org.reactjs.native.example.ModrkProduction"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:795262046849:android:737a483015bccefbbcb380", "android_client_info": {"package_name": "com.ModrkProduction"}}, "oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDNKYVTQvRGMGHA9IP9zBb_Wx9hqc-TI4M"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}, {"client_id": "795262046849-1k1aoqm5c2oofeaistf8kalkdnlcc42u.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "org.reactjs.native.example.ModrkProduction"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:795262046849:android:1d143cf2850ff466bcb380", "android_client_info": {"package_name": "com.ModrkShepard"}}, "oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDNKYVTQvRGMGHA9IP9zBb_Wx9hqc-TI4M"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}, {"client_id": "795262046849-1k1aoqm5c2oofeaistf8kalkdnlcc42u.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "org.reactjs.native.example.ModrkProduction"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:795262046849:android:9d8e98a749a5171fbcb380", "android_client_info": {"package_name": "com.ModrkTrader"}}, "oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDNKYVTQvRGMGHA9IP9zBb_Wx9hqc-TI4M"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "795262046849-sjkj8aih64beffdcp0ikj6fhb2403q3u.apps.googleusercontent.com", "client_type": 3}, {"client_id": "795262046849-1k1aoqm5c2oofeaistf8kalkdnlcc42u.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "org.reactjs.native.example.ModrkProduction"}}]}}}], "configuration_version": "1"}